import { DataPlugin } from './data-plugin';
import { registerDatasetTypes } from './dataset-types';
import { registerLanguageConfigs } from './language-configs';

/**
 * Initializes the Data Plugin and registers all extensions
 * @returns A promise that resolves when initialization is complete
 */
export async function initializeDataPlugin(): Promise<void> {
  const dataPlugin = DataPlugin.getInstance();
  
  // Initialize the core plugin
  await dataPlugin.initialize();
  
  // Register dataset types
  registerDatasetTypes(dataPlugin);
  
  // Register language configurations
  registerLanguageConfigs(dataPlugin);
  
  console.log('Data Plugin extensions registered successfully');
}