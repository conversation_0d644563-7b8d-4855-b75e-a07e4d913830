import React, { useEffect, useState } from 'react';
import { DataPlugin } from '../data-plugin';
import { Dataset, DatasetField } from '../interfaces';
import { sampleDatasetType } from '../dataset-types/sample-dataset-type';

/**
 * Example React component demonstrating how to use the DatasetService
 */
export const DatasetExample: React.FC = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [fields, setFields] = useState<DatasetField[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get the DatasetService from the QueryService
  const queryService = DataPlugin.getInstance().getQueryService();
  const datasetService = queryService.getDatasetService();
  
  // Register the sample dataset type on component mount
  useEffect(() => {
    try {
      // Register the sample dataset type if not already registered
      if (!datasetService.getType(sampleDatasetType.id)) {
        datasetService.registerType(sampleDatasetType);
        console.log('Registered sample dataset type');
      }
      
      // Create and add sample datasets if they don't exist
      const logsPath = [{ id: 'logs', title: 'Sample Logs', type: 'dataset' }];
      const metricsPath = [{ id: 'metrics', title: 'Sample Metrics', type: 'dataset' }];
      
      const logsDataset = datasetService.createDataset('sample-data', logsPath);
      const metricsDataset = datasetService.createDataset('sample-data', metricsPath);
      
      if (logsDataset && !datasetService.getDataset(logsDataset.id)) {
        datasetService.addDataset(logsDataset);
      }
      
      if (metricsDataset && !datasetService.getDataset(metricsDataset.id)) {
        datasetService.addDataset(metricsDataset);
      }
      
      // Update the datasets state
      setDatasets(datasetService.getDatasets());
      setLoading(false);
    } catch (err) {
      setError(`Failed to initialize datasets: ${err instanceof Error ? err.message : String(err)}`);
      setLoading(false);
    }
  }, [datasetService]);
  
  // Load fields when a dataset is selected
  useEffect(() => {
    const loadFields = async () => {
      if (!selectedDataset) {
        setFields([]);
        return;
      }
      
      setLoading(true);
      setError(null);
      
      try {
        // Check if dataset is cached
        if (!datasetService.isDatasetCached(selectedDataset.id)) {
          await datasetService.cacheDataset(selectedDataset, {});
        }
        
        // Get cached fields
        const cachedFields = datasetService.getCachedFields(selectedDataset.id);
        setFields(cachedFields || []);
      } catch (err) {
        setError(`Failed to load fields: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setLoading(false);
      }
    };
    
    loadFields();
  }, [selectedDataset, datasetService]);
  
  // Handle dataset selection
  const handleDatasetSelect = (datasetId: string) => {
    const dataset = datasetService.getDataset(datasetId);
    setSelectedDataset(dataset || null);
  };
  
  // Render the component
  return (
    <div className="dataset-example">
      <h2>Dataset Example</h2>
      
      {loading && <div className="loading">Loading...</div>}
      {error && <div className="error">{error}</div>}
      
      <div className="dataset-selector">
        <h3>Available Datasets</h3>
        <ul>
          {datasets.map(dataset => (
            <li key={dataset.id}>
              <button 
                onClick={() => handleDatasetSelect(dataset.id)}
                className={selectedDataset?.id === dataset.id ? 'selected' : ''}
              >
                {dataset.title}
              </button>
            </li>
          ))}
        </ul>
      </div>
      
      {selectedDataset && (
        <div className="dataset-details">
          <h3>Selected Dataset: {selectedDataset.title}</h3>
          <p>ID: {selectedDataset.id}</p>
          <p>Type: {selectedDataset.type}</p>
          {selectedDataset.timeFieldName && <p>Time Field: {selectedDataset.timeFieldName}</p>}
          
          <h4>Fields</h4>
          <table className="fields-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Searchable</th>
                <th>Aggregatable</th>
              </tr>
            </thead>
            <tbody>
              {fields.map(field => (
                <tr key={field.name}>
                  <td>{field.name}</td>
                  <td>{field.type}</td>
                  <td>{field.searchable ? 'Yes' : 'No'}</td>
                  <td>{field.aggregatable ? 'Yes' : 'No'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};