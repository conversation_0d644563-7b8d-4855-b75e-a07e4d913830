import React, { useState } from 'react';
import { useDataset } from '../../hooks/dataHooks';
import { Dataset, DataStructure } from '../../data/plugin/interfaces';

interface DatasetSelectorProps {
  onDatasetSelected?: (dataset: Dataset) => void;
  className?: string;
}

/**
 * Component for selecting datasets
 */
const DatasetSelector: React.FC<DatasetSelectorProps> = ({ 
  onDatasetSelected,
  className = ''
}) => {
  const { datasets, selectedDataset, selectDataset } = useDataset();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [browsePath, setBrowsePath] = useState<DataStructure[]>([]);
  const [currentItems, setCurrentItems] = useState<DataStructure[]>([]);
  
  // Handle dataset selection
  const handleDatasetSelect = async (datasetId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      await selectDataset(datasetId);
      
      // Find the selected dataset
      const dataset = datasets.find(d => d.id === datasetId);
      
      // Call the onDatasetSelected callback if provided
      if (dataset && onDatasetSelected) {
        onDatasetSelected(dataset);
      }
    } catch (err) {
      setError(err as Error);
      console.error('Error selecting dataset:', err);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Filter datasets based on search term
  const filteredDatasets = searchTerm
    ? datasets.filter(dataset => 
        dataset.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dataset.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : datasets;
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  // Handle browse path navigation
  const handleBrowse = (item: DataStructure) => {
    // Add the item to the browse path
    const newPath = [...browsePath, item];
    setBrowsePath(newPath);
    
    // If the item has children, set them as current items
    if (item.children) {
      setCurrentItems(item.children);
    } else {
      setCurrentItems([]);
    }
  };
  
  // Handle navigation back in the browse path
  const handleBack = () => {
    if (browsePath.length <= 1) {
      // If at the root, clear the path
      setBrowsePath([]);
      setCurrentItems([]);
    } else {
      // Remove the last item from the path
      const newPath = browsePath.slice(0, -1);
      setBrowsePath(newPath);
      
      // Set the children of the new last item as current items
      const lastItem = newPath[newPath.length - 1];
      if (lastItem.children) {
        setCurrentItems(lastItem.children);
      } else {
        setCurrentItems([]);
      }
    }
  };
  
  return (
    <div className={`dataset-selector ${className}`}>
      <div className="dataset-selector-header">
        <h3>Select Dataset</h3>
        <div className="dataset-search">
          <input
            type="text"
            placeholder="Search datasets..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="dataset-search-input"
          />
        </div>
      </div>
      
      {isLoading && (
        <div className="dataset-selector-loading">
          <p>Loading...</p>
        </div>
      )}
      
      {error && (
        <div className="dataset-selector-error">
          <p>Error: {error.message}</p>
        </div>
      )}
      
      {browsePath.length > 0 && (
        <div className="dataset-browse-path">
          <button 
            onClick={() => setBrowsePath([])}
            className="dataset-browse-home"
          >
            Home
          </button>
          {browsePath.map((item, index) => (
            <span key={item.id} className="dataset-browse-path-item">
              <span className="dataset-browse-separator">/</span>
              <button
                onClick={() => {
                  // Navigate to this level in the path
                  const newPath = browsePath.slice(0, index + 1);
                  setBrowsePath(newPath);
                  
                  // Set the children of this item as current items
                  if (item.children) {
                    setCurrentItems(item.children);
                  } else {
                    setCurrentItems([]);
                  }
                }}
                className="dataset-browse-path-button"
              >
                {item.title}
              </button>
            </span>
          ))}
          <button 
            onClick={handleBack}
            className="dataset-browse-back"
          >
            Back
          </button>
        </div>
      )}
      
      {browsePath.length > 0 && currentItems.length > 0 ? (
        <div className="dataset-browse-items">
          <ul className="dataset-browse-list">
            {currentItems.map((item) => (
              <li key={item.id} className="dataset-browse-item">
                <button
                  onClick={() => handleBrowse(item)}
                  className="dataset-browse-item-button"
                >
                  <span className="dataset-browse-item-title">{item.title}</span>
                  <span className="dataset-browse-item-type">{item.type}</span>
                </button>
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className="dataset-list">
          {filteredDatasets.length > 0 ? (
            <ul className="dataset-items">
              {filteredDatasets.map((dataset) => (
                <li 
                  key={dataset.id} 
                  className={`dataset-item ${selectedDataset?.id === dataset.id ? 'selected' : ''}`}
                >
                  <button
                    onClick={() => handleDatasetSelect(dataset.id)}
                    className="dataset-item-button"
                    disabled={isLoading}
                  >
                    <div className="dataset-item-title">{dataset.title}</div>
                    <div className="dataset-item-type">{dataset.type}</div>
                    {selectedDataset?.id === dataset.id && (
                      <div className="dataset-item-selected-indicator">✓</div>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <div className="dataset-empty">
              <p>No datasets found</p>
            </div>
          )}
        </div>
      )}
      
      {selectedDataset && (
        <div className="selected-dataset-info">
          <h4>Selected Dataset</h4>
          <p>
            <strong>ID:</strong> {selectedDataset.id}
            <br />
            <strong>Title:</strong> {selectedDataset.title}
            <br />
            <strong>Type:</strong> {selectedDataset.type}
            {selectedDataset.timeFieldName && (
              <>
                <br />
                <strong>Time Field:</strong> {selectedDataset.timeFieldName}
              </>
            )}
          </p>
        </div>
      )}
    </div>
  );
};

export default DatasetSelector;