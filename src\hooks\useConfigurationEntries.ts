import { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { useConfigurationAssessment } from '../context/ConfigurationAssessmentContext';
import { ConfigurationEntry } from '../types/configuration';
import {
  getFieldValue,
  formatFieldValue,
  getFieldColor
} from '../utils/configurationAdapters';
import { configurationAssessmentActions } from '../context/ConfigurationAssessmentActions';
import { savePageSizePreference } from '../utils/storageUtils';
import { throttle } from '../utils/debounce';
import { createPerformanceTracker } from '../utils/performanceTracking';

// Create a performance tracker for this hook
const performanceTracker = createPerformanceTracker('useConfigurationEntries');

/**
 * Hook for managing configuration entries and details with optimized performance
 */
export const useConfigurationEntries = () => {
  const { state, dispatch } = useConfigurationAssessment();
  const { filteredData, selectedFields, pagination, processingOperation } = state;
  
  // Keep track of previous filtered data for consistency checks
  const prevFilteredDataRef = useRef<ConfigurationEntry[]>(filteredData);
  
  // Track if data has changed since last render
  const dataChanged = useMemo(() => {
    const changed = prevFilteredDataRef.current !== filteredData;
    if (changed) {
      prevFilteredDataRef.current = filteredData;
    }
    return changed;
  }, [filteredData]);

  // Calculate total pages based on filtered data and page size - memoized for performance
  const totalPages = useMemo(() => {
    return performanceTracker.trackOperation('calculateTotalPages', () => {
      return Math.max(1, Math.ceil(filteredData.length / pagination.pageSize));
    });
  }, [filteredData.length, pagination.pageSize]);

  // Get paginated entries based on current page and page size - memoized for performance
  const paginatedEntries = useMemo(() => {
    return performanceTracker.trackOperation('calculatePaginatedEntries', () => {
      const startIndex = pagination.currentPage * pagination.pageSize;
      const endIndex = startIndex + pagination.pageSize;
      return filteredData.slice(startIndex, endIndex);
    });
  }, [filteredData, pagination.currentPage, pagination.pageSize]);

  // Pagination helper functions - optimized with useCallback
  const setCurrentPage = useCallback((page: number) => {
    return performanceTracker.trackOperation('setCurrentPage', () => {
      // Validate input - ensure it's a number
      if (isNaN(page) || !Number.isInteger(page)) {
        console.error('Invalid page number:', page);
        return;
      }

      // Ensure page is within valid range (convert from 1-based to 0-based)
      const validPage = Math.max(0, Math.min(page - 1, totalPages - 1));

      // Don't do anything if the page is already the current page
      if (validPage === pagination.currentPage) {
        return;
      }

      // Set processing operation flag
      dispatch(configurationAssessmentActions.setProcessingOperation(true));

      // Set loading state before changing page - but with minimal delay
      dispatch({ type: 'SET_LOADING', payload: true });

      // Change page
      dispatch(configurationAssessmentActions.setCurrentPage(validPage));

      // Use a shorter timeout for better performance
      setTimeout(() => {
        dispatch({ type: 'SET_LOADING', payload: false });
        dispatch(configurationAssessmentActions.setProcessingOperation(false));
      }, 50);
    });
  }, [dispatch, pagination.currentPage, totalPages]);

  // Throttled version of setCurrentPage to prevent rapid changes
  const throttledSetCurrentPage = useMemo(() =>
    throttle(setCurrentPage, 200),
    [setCurrentPage]
  );

  const setPageSize = useCallback((size: number) => {
    return performanceTracker.trackOperation('setPageSize', () => {
      // Validate input - ensure it's a valid number
      if (isNaN(size) || !Number.isInteger(size) || size <= 0) {
        console.error('Invalid page size:', size);
        return;
      }

      // Don't do anything if the size is already the current page size
      if (size === pagination.pageSize) {
        return;
      }

      // Set processing operation flag
      dispatch(configurationAssessmentActions.setProcessingOperation(true));

      // Set loading state before changing page size
      dispatch({ type: 'SET_LOADING', payload: true });

      // Save the page size preference to local storage
      savePageSizePreference(size);

      // Change page size
      dispatch(configurationAssessmentActions.setPageSize(size));

      // Use a shorter timeout for better performance
      setTimeout(() => {
        dispatch({ type: 'SET_LOADING', payload: false });
        dispatch(configurationAssessmentActions.setProcessingOperation(false));
      }, 50);
    });
  }, [dispatch, pagination.pageSize]);

  // Optimized navigation functions with useCallback
  const goToFirstPage = useCallback(() => {
    if (pagination.currentPage !== 0) {
      throttledSetCurrentPage(1); // 1-based page number
    }
  }, [pagination.currentPage, throttledSetCurrentPage]);

  const goToPreviousPage = useCallback(() => {
    if (pagination.currentPage > 0) {
      throttledSetCurrentPage(pagination.currentPage); // Current page in 0-based + 1 - 1 = current page
    }
  }, [pagination.currentPage, throttledSetCurrentPage]);

  const goToNextPage = useCallback(() => {
    if (pagination.currentPage < totalPages - 1) {
      throttledSetCurrentPage(pagination.currentPage + 2); // Current page in 0-based + 1 + 1 = current page + 2
    }
  }, [pagination.currentPage, totalPages, throttledSetCurrentPage]);

  const goToLastPage = useCallback(() => {
    if (pagination.currentPage !== totalPages - 1) {
      throttledSetCurrentPage(totalPages); // 1-based page number
    }
  }, [pagination.currentPage, totalPages, throttledSetCurrentPage]);

  // State for expanded entries - optimized with useCallback for handlers
  const [expandedEntries, setExpandedEntries] = useState<Record<string, boolean>>({});

  // Toggle entry expansion - memoized with useCallback
  const toggleEntryExpansion = useCallback((entryId: string) => {
    setExpandedEntries(prev => ({
      ...prev,
      [entryId]: !prev[entryId],
    }));
  }, []);

  // Check if entry is expanded - memoized with useCallback
  const isEntryExpanded = useCallback((entryId: string) => {
    return !!expandedEntries[entryId];
  }, [expandedEntries]);

  // Expand entry - memoized with useCallback
  const expandEntry = useCallback((entryId: string) => {
    setExpandedEntries(prev => ({
      ...prev,
      [entryId]: true,
    }));
  }, []);

  // Collapse entry - memoized with useCallback
  const collapseEntry = useCallback((entryId: string) => {
    setExpandedEntries(prev => ({
      ...prev,
      [entryId]: false,
    }));
  }, []);

  // Collapse all entries - memoized with useCallback
  const collapseAllEntries = useCallback(() => {
    setExpandedEntries({});
  }, []);

  // Memoize pagination helper properties to prevent unnecessary recalculations
  const paginationInfo = useMemo(() => {
    return performanceTracker.trackOperation('calculatePaginationInfo', () => {
      const hasResults = filteredData.length > 0;
      const isFirstPage = pagination.currentPage === 0;
      const isLastPage = pagination.currentPage >= totalPages - 1;
      const startIndex = hasResults
        ? pagination.currentPage * pagination.pageSize + 1
        : 0;
      const endIndex = Math.min((pagination.currentPage + 1) * pagination.pageSize, filteredData.length);
      const showingText = hasResults
        ? `Showing ${startIndex} to ${endIndex} of ${filteredData.length} entries`
        : 'No entries to display';

      return {
        currentPage: pagination.currentPage + 1, // Convert to 1-based for display
        pageSize: pagination.pageSize,
        totalItems: filteredData.length,
        totalPages,
        isFirstPage,
        isLastPage,
        startIndex,
        endIndex,
        showingText,
        isLoading: state.isLoading,
        processingOperation,
        noResults: !hasResults
      };
    });
  }, [
    filteredData.length,
    pagination.currentPage,
    pagination.pageSize,
    totalPages,
    state.isLoading,
    processingOperation
  ]);

  // Performance metrics for the hook
  const metrics = useMemo(() => {
    return performanceTracker.trackOperation('calculateMetrics', () => ({
      totalEntries: filteredData.length,
      visibleEntries: paginatedEntries.length,
      processingOperation,
      isLoading: state.isLoading,
      dataChanged,
      performanceData: performanceTracker.getComponentMetrics()
    }));
  }, [filteredData.length, paginatedEntries.length, processingOperation, state.isLoading, dataChanged]);

  // Log performance data in development mode
  useEffect(() => {
    if (typeof process !== 'undefined' && process.env && process.env.NODE_ENV !== 'production') {
      performanceTracker.logPerformance();
    }
  }, [paginatedEntries]); // Log when paginated entries change

  return {
    entries: paginatedEntries,
    allEntries: filteredData,
    selectedFields,
    expandedEntries,
    toggleEntryExpansion,
    isEntryExpanded,
    expandEntry,
    collapseEntry,
    collapseAllEntries,
    getFieldValue,
    formatFieldValue,
    getFieldColor,
    metrics,
    // Pagination state and functions
    pagination: {
      ...paginationInfo,
      // Navigation functions
      setCurrentPage: throttledSetCurrentPage, // Use throttled version
      setPageSize,
      goToFirstPage,
      goToPreviousPage,
      goToNextPage,
      goToLastPage
    }
  };
};