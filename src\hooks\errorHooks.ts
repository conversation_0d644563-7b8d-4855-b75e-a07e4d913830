import { useState, useEffect, useCallback } from 'react';
import { error<PERSON><PERSON><PERSON>, ErrorInfo } from '../services/ErrorHandler';

/**
 * Hook for handling errors in components
 * @returns Object with error state and functions
 */
export const useErrorHandler = () => {
  const [error, setError] = useState<ErrorInfo | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  
  // Handle an error
  const handleError = useCallback((err: unknown) => {
    const errorInfo = errorHandler.handleError(err);
    setError(errorInfo);
    setIsVisible(true);
    return errorInfo;
  }, []);
  
  // Clear the error
  const clearError = useCallback(() => {
    setError(null);
    setIsVisible(false);
  }, []);
  
  // Retry the operation that caused the error
  const retry = useCallback((retryFn?: () => void) => {
    setIsVisible(false);
    if (retryFn) {
      try {
        retryFn();
      } catch (err) {
        handleError(err);
      }
    }
  }, [handleError]);
  
  return {
    error,
    isVisible,
    handleError,
    clearError,
    retry
  };
};

/**
 * Hook for global error notifications
 * @param onError Optional callback for when an error occurs
 */
export const useGlobalErrorListener = (onError?: (error: ErrorInfo) => void) => {
  useEffect(() => {
    // Add a listener to the error handler
    const removeListener = errorHandler.addListener((errorInfo) => {
      if (onError) {
        onError(errorInfo);
      }
    });
    
    // Remove the listener when the component unmounts
    return removeListener;
  }, [onError]);
};

/**
 * Hook for wrapping async functions with error handling
 * @returns A function to wrap async functions with error handling
 */
export const useAsyncErrorHandler = () => {
  const { handleError } = useErrorHandler();
  
  // Wrap an async function with error handling
  const wrapAsync = useCallback(<T>(asyncFn: () => Promise<T>): Promise<T> => {
    return asyncFn().catch(err => {
      handleError(err);
      throw err;
    });
  }, [handleError]);
  
  return { wrapAsync };
};