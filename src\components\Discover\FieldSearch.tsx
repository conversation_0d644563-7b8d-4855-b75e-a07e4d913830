import React, { useState, useEffect } from 'react';

interface FieldSearchProps {
  onSearch: (searchTerm: string) => void;
  placeholder?: string;
}

/**
 * Component for searching and filtering available fields in the Discover sidebar
 */
const FieldSearch: React.FC<FieldSearchProps> = ({ 
  onSearch, 
  placeholder = "Search fields..." 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  // Debounce search to avoid excessive filtering
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearch(searchTerm);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [searchTerm, onSearch]);
  
  return (
    <div style={{ marginBottom: '16px' }}>
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={placeholder}
        aria-label="Search fields"
        style={{
          width: '100%',
          padding: '8px',
          background: 'rgba(0, 0, 0, 0.3)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          color: 'white',
          fontSize: '14px',
        }}
      />
      {searchTerm && (
        <button
          onClick={() => setSearchTerm('')}
          aria-label="Clear search"
          style={{
            position: 'absolute',
            right: '24px',
            top: '50%',
            transform: 'translateY(-50%)',
            background: 'transparent',
            border: 'none',
            color: 'rgba(255, 255, 255, 0.7)',
            cursor: 'pointer',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default FieldSearch;