import React from 'react';
import { configurationFieldMappings } from '../../utils/configurationAdapters';

interface ConfigurationSearchSuggestionsProps {
  isVisible: boolean;
  searchInput: string;
  onSelectSuggestion: (suggestion: string) => void;
}

/**
 * Component that displays search suggestions for configuration fields as the user types
 * 
 * Requirements: 2.1, 2.2
 */
const ConfigurationSearchSuggestions: React.FC<ConfigurationSearchSuggestionsProps> = ({ 
  isVisible, 
  searchInput, 
  onSelectSuggestion 
}) => {
  // Get field paths from configuration field mappings
  const fieldPaths = Object.keys(configurationFieldMappings);
  
  if (!isVisible || !searchInput) return null;
  
  // Filter field paths based on search input
  const filteredFields = fieldPaths.filter(field => 
    field.toLowerCase().includes(searchInput.toLowerCase())
  );
  
  // Generate suggestions based on the search input
  const suggestions = filteredFields.map(field => `${field}:`);
  
  // If no suggestions found or no input, don't show anything
  if (suggestions.length === 0) return null;
  
  return (
    <div style={{
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      marginTop: '4px',
      background: 'rgba(10, 14, 23, 0.95)',
      border: '1px solid rgba(0, 229, 255, 0.3)',
      borderRadius: '4px',
      padding: '8px 0',
      zIndex: 10,
      maxHeight: '200px',
      overflowY: 'auto',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
    }}>
      {suggestions.slice(0, 10).map((suggestion, index) => (
        <div 
          key={index}
          onClick={() => onSelectSuggestion(suggestion)}
          style={{
            padding: '8px 16px',
            cursor: 'pointer',
            color: 'white',
            fontSize: '14px',
            transition: 'background-color 0.2s',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.background = 'rgba(0, 229, 255, 0.05)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.background = 'transparent';
          }}
        >
          {suggestion}
        </div>
      ))}
    </div>
  );
};

export default ConfigurationSearchSuggestions;