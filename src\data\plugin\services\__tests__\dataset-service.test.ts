import { DatasetServiceImpl } from '../dataset-service';
import { Dataset, DatasetTypeConfig } from '../../interfaces';

describe('DatasetService', () => {
  let datasetService: DatasetServiceImpl;
  let mockDatasetTypeConfig: DatasetTypeConfig;
  let mockDataset: Dataset;

  beforeEach(() => {
    // Create a new instance of DatasetService for each test
    datasetService = new DatasetServiceImpl();

    // Create a mock dataset type config
    mockDatasetTypeConfig = {
      id: 'test-type',
      title: 'Test Type',
      meta: {
        icon: 'test-icon',
        tooltip: 'Test tooltip',
        searchOnLoad: true,
        isFieldLoadAsync: false
      },
      toDataset: jest.fn().mockImplementation(() => ({
        id: 'test-dataset',
        title: 'Test Dataset',
        type: 'test-type'
      })),
      fetch: jest.fn().mockResolvedValue({
        id: 'test-structure',
        title: 'Test Structure',
        type: 'test-type'
      }),
      fetchFields: jest.fn().mockResolvedValue([
        {
          name: 'field1',
          type: 'string',
          searchable: true,
          aggregatable: true
        },
        {
          name: 'field2',
          type: 'number',
          searchable: true,
          aggregatable: true
        }
      ]),
      supportedLanguages: jest.fn().mockReturnValue(['kuery', 'lucene'])
    };

    // Create a mock dataset
    mockDataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'test-type'
    };
  });

  describe('Dataset Type Registration', () => {
    it('should register a dataset type', () => {
      datasetService.registerType(mockDatasetTypeConfig);
      const retrievedType = datasetService.getType('test-type');
      expect(retrievedType).toBe(mockDatasetTypeConfig);
    });

    it('should throw an error when registering an invalid dataset type', () => {
      expect(() => {
        datasetService.registerType(undefined as unknown as DatasetTypeConfig);
      }).toThrow('Invalid dataset type configuration');
    });

    it('should get all registered types', () => {
      datasetService.registerType(mockDatasetTypeConfig);
      const types = datasetService.getTypes();
      expect(types).toHaveLength(1);
      expect(types[0]).toBe(mockDatasetTypeConfig);
    });
  });

  describe('Dataset Management', () => {
    it('should add a dataset', () => {
      datasetService.addDataset(mockDataset);
      const datasets = datasetService.getDatasets();
      expect(datasets).toHaveLength(1);
      expect(datasets[0]).toBe(mockDataset);
    });

    it('should not add duplicate datasets', () => {
      datasetService.addDataset(mockDataset);
      datasetService.addDataset(mockDataset);
      const datasets = datasetService.getDatasets();
      expect(datasets).toHaveLength(1);
    });

    it('should get a dataset by ID', () => {
      datasetService.addDataset(mockDataset);
      const dataset = datasetService.getDataset('test-dataset');
      expect(dataset).toBe(mockDataset);
    });

    it('should return undefined for non-existent dataset', () => {
      const dataset = datasetService.getDataset('non-existent');
      expect(dataset).toBeUndefined();
    });

    it('should remove a dataset', () => {
      datasetService.addDataset(mockDataset);
      datasetService.removeDataset('test-dataset');
      const datasets = datasetService.getDatasets();
      expect(datasets).toHaveLength(0);
    });

    it('should throw an error when adding an invalid dataset', () => {
      expect(() => {
        datasetService.addDataset(undefined as unknown as Dataset);
      }).toThrow('Invalid dataset');
    });
  });

  describe('Dataset Caching', () => {
    beforeEach(() => {
      // Register the mock dataset type
      datasetService.registerType(mockDatasetTypeConfig);
    });

    it('should cache a dataset', async () => {
      await datasetService.cacheDataset(mockDataset, {});
      expect(mockDatasetTypeConfig.fetchFields).toHaveBeenCalledWith(mockDataset);
      expect(datasetService.isDatasetCached('test-dataset')).toBe(true);
    });

    it('should retrieve cached fields', async () => {
      await datasetService.cacheDataset(mockDataset, {});
      const fields = datasetService.getCachedFields('test-dataset');
      expect(fields).toHaveLength(2);
      expect(fields?.[0].name).toBe('field1');
      expect(fields?.[1].name).toBe('field2');
    });

    it('should throw an error when caching a dataset with unregistered type', async () => {
      const invalidDataset: Dataset = {
        id: 'invalid-dataset',
        title: 'Invalid Dataset',
        type: 'unregistered-type'
      };

      await expect(datasetService.cacheDataset(invalidDataset, {}))
        .rejects
        .toThrow("Dataset type 'unregistered-type' not registered");
    });

    it('should invalidate cache', async () => {
      await datasetService.cacheDataset(mockDataset, {});
      datasetService.invalidateCache('test-dataset');
      expect(datasetService.isDatasetCached('test-dataset')).toBe(false);
    });

    it('should automatically invalidate cache when removing a dataset', async () => {
      datasetService.addDataset(mockDataset);
      await datasetService.cacheDataset(mockDataset, {});
      datasetService.removeDataset('test-dataset');
      expect(datasetService.isDatasetCached('test-dataset')).toBe(false);
    });
  });

  describe('Dataset Creation', () => {
    beforeEach(() => {
      // Register the mock dataset type
      datasetService.registerType(mockDatasetTypeConfig);
    });

    it('should create a dataset from a path', () => {
      const path = [{ id: 'path1', title: 'Path 1', type: 'folder' }];
      const dataset = datasetService.createDataset('test-type', path);
      expect(mockDatasetTypeConfig.toDataset).toHaveBeenCalledWith(path);
      expect(dataset).toEqual({
        id: 'test-dataset',
        title: 'Test Dataset',
        type: 'test-type'
      });
    });

    it('should return undefined when creating a dataset with unregistered type', () => {
      const path = [{ id: 'path1', title: 'Path 1', type: 'folder' }];
      const dataset = datasetService.createDataset('unregistered-type', path);
      expect(dataset).toBeUndefined();
    });
  });
});