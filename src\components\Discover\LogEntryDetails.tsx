import React, { useState } from 'react';
import { LogEntry } from '../../types/discover';
import { useDiscoverLogs } from '../../hooks';

interface LogEntryDetailsProps {
  log: LogEntry;
  onClose?: () => void;
}

/**
 * Component for displaying detailed information about a log entry
 */
const LogEntryDetails: React.FC<LogEntryDetailsProps> = ({ log, onClose }) => {
  const { formatFieldValue } = useDiscoverLogs();
  const [activeTab, setActiveTab] = useState<'json' | 'table'>('table');
  
  // Format JSON for display
  const formattedJson = JSON.stringify(log, null, 2);
  
  // Get all fields from the log entry
  const getLogFields = () => {
    const fields: Array<{ key: string; value: unknown }> = [];
    
    // Add base fields
    fields.push({ key: 'timestamp', value: log.timestamp });
    fields.push({ key: 'level', value: log.level });
    fields.push({ key: 'source', value: log.source });
    fields.push({ key: 'message', value: log.message });
    fields.push({ key: 'location', value: log.location });
    
    // Add agent fields
    fields.push({ key: 'agent.id', value: log.agent.id });
    fields.push({ key: 'agent.name', value: log.agent.name });
    fields.push({ key: 'agent.ip', value: log.agent.ip });
    
    // Add rule fields
    fields.push({ key: 'rule.id', value: log.rule.id });
    fields.push({ key: 'rule.description', value: log.rule.description });
    fields.push({ key: 'rule.level', value: log.rule.level });
    fields.push({ key: 'rule.groups', value: log.rule.groups });
    
    // Add decoder fields
    fields.push({ key: 'decoder.name', value: log.decoder.name });
    
    // Add data fields
    Object.entries(log.data).forEach(([key, value]) => {
      fields.push({ key: `data.${key}`, value });
    });
    
    return fields;
  };
  
  const fields = getLogFields();
  
  return (
    <div 
      role="region"
      aria-label="Log entry details"
      style={{
        padding: '16px',
        background: 'rgba(0, 0, 0, 0.2)',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        color: 'white',
        fontSize: '14px',
      }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px',
      }}>
        <h4 style={{ margin: 0 }}>Log Details</h4>
        
        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          {/* Tab navigation */}
          <div role="tablist" style={{ display: 'flex', gap: '8px' }}>
            <button
              role="tab"
              aria-selected={activeTab === 'table'}
              aria-controls="table-panel"
              id="table-tab"
              onClick={() => setActiveTab('table')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'table' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'table' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              Table View
            </button>
            <button
              role="tab"
              aria-selected={activeTab === 'json'}
              aria-controls="json-panel"
              id="json-tab"
              onClick={() => setActiveTab('json')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'json' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'json' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              JSON View
            </button>
          </div>
          
          {/* Close button */}
          {onClose && (
            <button
              onClick={onClose}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'rgba(255, 255, 255, 0.7)',
                cursor: 'pointer',
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M18 6L6 18M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>
      
      {/* Table view */}
      <div 
        role="tabpanel"
        id="table-panel"
        aria-labelledby="table-tab"
        hidden={activeTab !== 'table'}
        style={{
          display: activeTab === 'table' ? 'grid' : 'none',
          gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
          gap: '8px',
          maxHeight: '400px',
          overflowY: 'auto',
        }}
      >
        {fields.map(({ key, value }) => (
          <React.Fragment key={key}>
            <div style={{ 
              color: 'rgba(255, 255, 255, 0.7)',
              fontWeight: 'bold',
              padding: '4px 8px',
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
            }}>
              {key}
            </div>
            <div style={{
              padding: '4px 8px',
              wordBreak: 'break-word',
            }}>
              {formatFieldValue(value)}
            </div>
          </React.Fragment>
        ))}
      </div>
      
      {/* JSON view */}
      <pre 
        role="tabpanel"
        id="json-panel"
        aria-labelledby="json-tab"
        hidden={activeTab !== 'json'}
        style={{
          display: activeTab === 'json' ? 'block' : 'none',
          background: 'rgba(0, 0, 0, 0.3)',
          padding: '12px',
          borderRadius: '4px',
          overflowX: 'auto',
          maxHeight: '400px',
          fontSize: '12px',
          fontFamily: 'monospace',
          whiteSpace: 'pre-wrap',
        }}
      >
        {formattedJson}
      </pre>
    </div>
  );
};

export default LogEntryDetails;