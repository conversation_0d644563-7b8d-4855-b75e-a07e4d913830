// Export the Data Plugin
export { DataPlugin } from './data-plugin';

// Export interfaces
export * from './interfaces';

// Export services
export { SearchServiceImpl } from './services/search-service';
export { QueryServiceImpl } from './services/query-service';
export { DatasetServiceImpl } from './services/dataset-service';
export { LanguageServiceImpl } from './services/language-service';
export { UiServiceImpl } from './services/ui-service';
export { FieldFormatsServiceImpl } from './services/field-formats-service';
export { AutocompleteServiceImpl } from './services/autocomplete-service';
export { DataStorageImpl } from './services/data-storage';
export { IndexPatternService } from './services/index-pattern-service';

// Export search components
export { 
  SearchInterceptor,
  BaseSearchInterceptor,
  SampleSearchInterceptor,
  IndexPatternInterceptor,
  SearchStrategy,
  SearchStrategyRegistry,
  SampleSearchStrategy,
  DataFrameTransformer
} from './search';

// Export React components
export { DataPluginProvider, useDataPlugin } from './react/data-plugin-context';