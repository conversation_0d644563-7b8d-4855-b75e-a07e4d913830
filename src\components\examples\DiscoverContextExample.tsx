import React, { useState } from 'react';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';
import { useDataPlugin } from '../../context/DataPluginContext';
import { useDataset } from '../../hooks/dataHooks';

/**
 * Example component that demonstrates how to use the updated DiscoverContext
 */
const DiscoverContextExample: React.FC = () => {
  const { state, dispatch } = useDiscover();
  const dataPlugin = useDataPlugin();
  const { datasets, selectedDataset, selectDataset } = useDataset();
  
  const [searchInput, setSearchInput] = useState(state.searchQuery);
  
  // Handle search query change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };
  
  // Handle search form submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    dispatch(discoverActions.setSearchQuery(searchInput));
  };
  
  // Handle time range change
  const handleTimeRangeChange = (preset: string) => {
    const now = new Date();
    let from: Date;
    
    switch (preset) {
      case 'last-15m':
        from = new Date(now.getTime() - (15 * 60 * 1000));
        break;
      case 'last-1h':
        from = new Date(now.getTime() - (60 * 60 * 1000));
        break;
      case 'last-24h':
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        break;
      case 'last-7d':
        from = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
        break;
      case 'last-30d':
        from = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
        break;
      default:
        from = new Date(now.getTime() - (24 * 60 * 60 * 1000));
    }
    
    dispatch(discoverActions.setTimeRange(from, now, preset));
  };
  
  // Handle filter add
  const handleAddFilter = () => {
    dispatch(discoverActions.addFilter('level', 'info', 'is'));
  };
  
  // Handle filter remove
  const handleRemoveFilter = (field: string) => {
    dispatch(discoverActions.removeFilter(field));
  };
  
  // Handle dataset selection
  const handleDatasetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    selectDataset(e.target.value);
  };
  
  // Handle auto refresh toggle
  const handleAutoRefreshToggle = () => {
    dispatch(discoverActions.setAutoRefresh(!state.autoRefresh));
  };
  
  // Handle refresh interval change
  const handleRefreshIntervalChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    dispatch(discoverActions.setRefreshInterval(parseInt(e.target.value, 10)));
  };
  
  // Handle manual refresh
  const handleRefresh = () => {
    dispatch(discoverActions.refresh());
  };
  
  return (
    <div className="discover-context-example">
      <h2>Discover Context Example</h2>
      
      <div className="section">
        <h3>Data Plugin Integration</h3>
        <div className="data-plugin-info">
          <p>Data Plugin is initialized: {dataPlugin ? 'Yes' : 'No'}</p>
          
          {datasets && datasets.length > 0 && (
            <div className="dataset-selector">
              <label>Dataset:</label>
              <select 
                value={selectedDataset?.id || ''} 
                onChange={handleDatasetChange}
              >
                <option value="">Select a dataset</option>
                {datasets.map((dataset) => (
                  <option key={dataset.id} value={dataset.id}>
                    {dataset.title}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Search</h3>
        <form onSubmit={handleSearchSubmit} className="search-form">
          <input 
            type="text" 
            value={searchInput} 
            onChange={handleSearchChange} 
            placeholder="Enter search query"
            className="search-input"
          />
          <button type="submit" className="search-button">Search</button>
        </form>
        
        <div className="current-query">
          <p>Current query: <strong>{state.searchQuery}</strong></p>
        </div>
      </div>
      
      <div className="section">
        <h3>Time Range</h3>
        <div className="time-range-controls">
          <button onClick={() => handleTimeRangeChange('last-15m')}>Last 15 minutes</button>
          <button onClick={() => handleTimeRangeChange('last-1h')}>Last hour</button>
          <button onClick={() => handleTimeRangeChange('last-24h')}>Last 24 hours</button>
          <button onClick={() => handleTimeRangeChange('last-7d')}>Last 7 days</button>
          <button onClick={() => handleTimeRangeChange('last-30d')}>Last 30 days</button>
        </div>
        
        <div className="current-time-range">
          <p>
            From: <strong>{state.timeRange.from.toLocaleString()}</strong>
            <br />
            To: <strong>{state.timeRange.to.toLocaleString()}</strong>
            <br />
            Preset: <strong>{state.timeRange.preset || 'custom'}</strong>
          </p>
        </div>
      </div>
      
      <div className="section">
        <h3>Filters</h3>
        <div className="filter-controls">
          <button onClick={handleAddFilter}>Add Example Filter</button>
        </div>
        
        <div className="current-filters">
          <h4>Applied Filters:</h4>
          {state.appliedFilters.length > 0 ? (
            <ul className="filter-list">
              {state.appliedFilters.map((filter, index) => (
                <li key={index} className="filter-item">
                  <span>
                    {filter.field} {filter.operator} {String(filter.value)}
                  </span>
                  <button 
                    onClick={() => handleRemoveFilter(filter.field)}
                    className="remove-filter"
                  >
                    Remove
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <p>No filters applied</p>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Auto Refresh</h3>
        <div className="auto-refresh-controls">
          <label>
            <input 
              type="checkbox" 
              checked={state.autoRefresh} 
              onChange={handleAutoRefreshToggle}
            />
            Enable Auto Refresh
          </label>
          
          <div className="refresh-interval">
            <label>Refresh Interval:</label>
            <select 
              value={state.refreshInterval} 
              onChange={handleRefreshIntervalChange}
              disabled={!state.autoRefresh}
            >
              <option value={5000}>5 seconds</option>
              <option value={10000}>10 seconds</option>
              <option value={30000}>30 seconds</option>
              <option value={60000}>1 minute</option>
              <option value={300000}>5 minutes</option>
            </select>
          </div>
          
          <button onClick={handleRefresh} className="refresh-button">
            Refresh Now
          </button>
        </div>
      </div>
      
      <div className="section">
        <h3>Results</h3>
        <div className="results-info">
          <p>
            Loading: <strong>{state.isLoading ? 'Yes' : 'No'}</strong>
            <br />
            Total logs: <strong>{state.logData.length}</strong>
            <br />
            Filtered logs: <strong>{state.filteredData.length}</strong>
          </p>
        </div>
        
        <div className="log-table">
          <h4>Log Entries:</h4>
          {state.isLoading ? (
            <p>Loading...</p>
          ) : state.filteredData.length > 0 ? (
            <table>
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>Source</th>
                  <th>Level</th>
                  <th>Message</th>
                </tr>
              </thead>
              <tbody>
                {state.filteredData.slice(0, 10).map((log) => (
                  <tr key={log.id}>
                    <td>{log.timestamp.toLocaleString()}</td>
                    <td>{log.source}</td>
                    <td>{log.level}</td>
                    <td>{log.message}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No logs found</p>
          )}
          
          {state.filteredData.length > 10 && (
            <p>Showing 10 of {state.filteredData.length} logs</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default DiscoverContextExample;