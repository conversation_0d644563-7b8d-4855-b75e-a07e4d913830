import { useState, useEffect, useCallback } from 'react';
import { debounce } from '../utils/debounce';

/**
 * Custom hook for debounced value
 * @param value The value to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced value
 */
export function useDebouncedValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * Custom hook for debounced callback
 * @param callback The callback to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced callback
 */
export function useDebouncedCallback<T extends (...args: unknown[]) => unknown>(
  callback: T,
  delay: number
): (...args: Parameters<T>) => void {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  return useCallback(debounce(callback, delay), [callback, delay]);
}

/**
 * Custom hook for debounced search
 * @param initialValue Initial search value
 * @param onSearch Callback to execute when search is debounced
 * @param delay Debounce delay in milliseconds
 * @returns Object with search value, setter, and debounced value
 */
export function useDebouncedSearch<T>(
  initialValue: T,
  onSearch: (value: T) => void,
  delay: number = 300
): {
  value: T;
  setValue: (value: T) => void;
  debouncedValue: T;
} {
  const [value, setValue] = useState<T>(initialValue);
  const debouncedValue = useDebouncedValue(value, delay);
  
  useEffect(() => {
    if (debouncedValue !== initialValue) {
      onSearch(debouncedValue);
    }
  }, [debouncedValue, onSearch, initialValue]);
  
  return {
    value,
    setValue,
    debouncedValue,
  };
}