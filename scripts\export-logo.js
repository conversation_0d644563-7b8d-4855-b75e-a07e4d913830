import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ensure the output directory exists
const outputDir = path.resolve(__dirname, '../public/images');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Path to the SVG file
const svgPath = path.resolve(__dirname, '../src/assets/logo.svg');

// Function to convert SVG to PNG with different sizes
async function convertToPng(size) {
  try {
    const outputPath = path.resolve(outputDir, `logo-${size}x${size}.png`);
    
    await sharp(svgPath)
      .resize(size, size)
      .png()
      .toFile(outputPath);
    
    console.log(`Successfully created ${outputPath}`);
  } catch (error) {
    console.error(`Error creating ${size}x${size} PNG:`, error);
  }
}

// Convert to different sizes
async function main() {
  const sizes = [16, 32, 64, 128, 256, 512];
  
  for (const size of sizes) {
    await convertToPng(size);
  }
}

main().catch(console.error); 