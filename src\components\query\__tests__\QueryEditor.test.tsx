import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import QueryEditor from '../QueryEditor';
import { DataPluginProvider } from '../../../context/DataPluginContext';
import { useQuery, useDataset } from '../../../hooks/dataHooks';
import { useLanguageAutocomplete } from '../../../hooks/uiHooks';

// Mock the hooks
jest.mock('../../../hooks/dataHooks', () => {
  const originalModule = jest.requireActual('../../../hooks/dataHooks');
  
  return {
    ...originalModule,
    useQuery: jest.fn(),
    useDataset: jest.fn()
  };
});

jest.mock('../../../hooks/uiHooks', () => {
  const originalModule = jest.requireActual('../../../hooks/uiHooks');
  
  return {
    ...originalModule,
    useLanguageAutocomplete: jest.fn()
  };
});

describe('QueryEditor', () => {
  const mockSetQuery = jest.fn();
  const mockOnSubmit = jest.fn();
  const mockGetCompletions = jest.fn();
  const mockValidateQuery = jest.fn();
  const mockHighlightQuery = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementations
    (useQuery as jest.Mock).mockReturnValue({
      query: { query: 'test query', language: 'kuery' },
      setQuery: mockSetQuery
    });
    
    (useDataset as jest.Mock).mockReturnValue({
      selectedDataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', supportedLanguages: ['kuery', 'lucene'] }
    });
    
    (useLanguageAutocomplete as jest.Mock).mockReturnValue({
      getCompletions: mockGetCompletions.mockResolvedValue([
        { value: 'field:', score: 1 },
        { value: 'value', score: 0.8 }
      ]),
      validateQuery: mockValidateQuery.mockReturnValue({ valid: true }),
      highlightQuery: mockHighlightQuery.mockReturnValue('<span class="highlighted">test query</span>'),
      hasAutocomplete: true
    });
  });
  
  it('should render with initial query', () => {
    render(
      <DataPluginProvider>
        <QueryEditor initialQuery="initial query" />
      </DataPluginProvider>
    );
    
    // Check that the input has the initial query
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    expect(input.value).toBe('initial query');
  });
  
  it('should update query on input change', () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Change the input value
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'new query' } });
    
    // Check that the input value was updated
    expect(input.value).toBe('new query');
  });
  
  it('should submit query on button click', () => {
    render(
      <DataPluginProvider>
        <QueryEditor onSubmit={mockOnSubmit} />
      </DataPluginProvider>
    );
    
    // Change the input value
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'new query' } });
    
    // Click the submit button
    const submitButton = screen.getByText('Search');
    fireEvent.click(submitButton);
    
    // Check that setQuery and onSubmit were called
    expect(mockSetQuery).toHaveBeenCalledWith({
      query: 'new query',
      language: 'kuery',
      dataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', supportedLanguages: ['kuery', 'lucene'] }
    });
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      query: 'new query',
      language: 'kuery',
      dataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', supportedLanguages: ['kuery', 'lucene'] }
    });
  });
  
  it('should submit query on Enter key', () => {
    render(
      <DataPluginProvider>
        <QueryEditor onSubmit={mockOnSubmit} />
      </DataPluginProvider>
    );
    
    // Change the input value
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'new query' } });
    
    // Press Enter
    fireEvent.keyDown(input, { key: 'Enter' });
    
    // Check that setQuery and onSubmit were called
    expect(mockSetQuery).toHaveBeenCalledWith({
      query: 'new query',
      language: 'kuery',
      dataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', supportedLanguages: ['kuery', 'lucene'] }
    });
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      query: 'new query',
      language: 'kuery',
      dataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', supportedLanguages: ['kuery', 'lucene'] }
    });
  });
  
  it('should change language', () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Change the language
    const languageSelect = screen.getByRole('combobox') as HTMLSelectElement;
    fireEvent.change(languageSelect, { target: { value: 'lucene' } });
    
    // Check that setQuery was called with the new language
    expect(mockSetQuery).toHaveBeenCalledWith({
      query: 'test query',
      language: 'lucene'
    });
  });
  
  it('should show validation error', () => {
    // Mock validation error
    mockValidateQuery.mockReturnValue({ valid: false, error: 'Invalid query syntax' });
    
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Check that the validation error is shown
    expect(screen.getByText('Invalid query syntax')).toBeInTheDocument();
    
    // Check that the submit button is disabled
    const submitButton = screen.getByText('Search');
    expect(submitButton).toBeDisabled();
  });
  
  it('should show suggestions on focus', async () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Focus the input
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.focus(input);
    
    // Check that getCompletions was called
    expect(mockGetCompletions).toHaveBeenCalledWith('test query', 0);
    
    // Wait for suggestions to be shown
    await waitFor(() => {
      expect(screen.getByText('field:')).toBeInTheDocument();
      expect(screen.getByText('value')).toBeInTheDocument();
    });
  });
  
  it('should navigate suggestions with arrow keys', async () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Focus the input
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.focus(input);
    
    // Wait for suggestions to be shown
    await waitFor(() => {
      expect(screen.getByText('field:')).toBeInTheDocument();
    });
    
    // Check that the first suggestion is selected
    const suggestions = screen.getAllByRole('presentation');
    expect(suggestions[0]).toHaveClass('selected');
    
    // Press arrow down
    fireEvent.keyDown(input, { key: 'ArrowDown' });
    
    // Check that the second suggestion is selected
    expect(suggestions[1]).toHaveClass('selected');
    
    // Press arrow up
    fireEvent.keyDown(input, { key: 'ArrowUp' });
    
    // Check that the first suggestion is selected again
    expect(suggestions[0]).toHaveClass('selected');
  });
  
  it('should select suggestion on Enter key', async () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Focus the input
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.focus(input);
    
    // Wait for suggestions to be shown
    await waitFor(() => {
      expect(screen.getByText('field:')).toBeInTheDocument();
    });
    
    // Press Enter to select the first suggestion
    fireEvent.keyDown(input, { key: 'Enter' });
    
    // Check that the input value was updated
    expect(input.value).toBe('field:');
  });
  
  it('should select suggestion on click', async () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Focus the input
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.focus(input);
    
    // Wait for suggestions to be shown
    await waitFor(() => {
      expect(screen.getByText('field:')).toBeInTheDocument();
    });
    
    // Click on the second suggestion
    const suggestions = screen.getAllByRole('presentation');
    fireEvent.click(suggestions[1]);
    
    // Check that the input value was updated
    expect(input.value).toBe('value');
  });
  
  it('should hide suggestions on Escape key', async () => {
    render(
      <DataPluginProvider>
        <QueryEditor />
      </DataPluginProvider>
    );
    
    // Focus the input
    const input = screen.getByPlaceholderText('Enter a query...') as HTMLInputElement;
    fireEvent.focus(input);
    
    // Wait for suggestions to be shown
    await waitFor(() => {
      expect(screen.getByText('field:')).toBeInTheDocument();
    });
    
    // Press Escape
    fireEvent.keyDown(input, { key: 'Escape' });
    
    // Check that suggestions are hidden
    await waitFor(() => {
      expect(screen.queryByText('field:')).not.toBeInTheDocument();
    });
  });
});