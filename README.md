# GuardBear Admin Panel

A modern, responsive admin dashboard for GuardBear security management.

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

### Development

To start the development server:

```bash
npm run dev
```

This will start the Vite development server and open your admin panel in the browser at http://localhost:5173/

### Building for Production

To build the application for production:

```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

### Previewing Production Build

To preview the production build locally:

```bash
npm run preview
```

## Features

- Responsive sidebar navigation
- Modern UI design with gradient background
- Collapsible sidebar for maximizing content area

## Technology Stack

- React
- TypeScript
- Vite
