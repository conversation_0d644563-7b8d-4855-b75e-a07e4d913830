import React, { useState } from 'react';

/**
 * Grid View page component.
 * 
 * This component displays a grid of security-related cards and widgets.
 */
const GridView: React.FC = () => {
  const [layout, setLayout] = useState('grid'); // 'grid' or 'list'
  
  // Sample grid items
  const gridItems = [
    { id: 1, title: 'Security Score', type: 'metric', value: '19/100', status: 'critical' },
    { id: 2, title: 'Active Threats', type: 'metric', value: '13', status: 'warning' },
    { id: 3, title: 'Protected Assets', type: 'metric', value: '223/241', status: 'good' },
    { id: 4, title: 'Network Traffic', type: 'chart', status: 'normal' },
    { id: 5, title: 'System Status', type: 'status', status: 'normal' },
    { id: 6, title: 'Recent Alerts', type: 'list', status: 'warning' },
    { id: 7, title: 'Compliance', type: 'progress', value: '78%', status: 'warning' },
    { id: 8, title: 'User Activity', type: 'chart', status: 'normal' },
    { id: 9, title: 'Endpoint Health', type: 'status', status: 'good' },
    { id: 10, title: 'Vulnerability Scan', type: 'progress', value: '92%', status: 'good' },
    { id: 11, title: 'Firewall Rules', type: 'list', status: 'normal' },
    { id: 12, title: 'Backup Status', type: 'status', status: 'good' },
  ];
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical':
        return '#ff4d4d';
      case 'warning':
        return '#ffcc00';
      case 'good':
        return '#00e5ff';
      default:
        return 'rgba(255, 255, 255, 0.7)';
    }
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'auto',
      position: 'relative',
      padding: '24px',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
      }}>
        <h1 style={{ 
          fontSize: '28px', 
          fontWeight: 600,
          background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
        }}>
          Grid View
        </h1>
        
        <div style={{
          display: 'flex',
          gap: '12px',
        }}>
          <button 
            onClick={() => setLayout('grid')}
            style={{
              background: layout === 'grid' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
              border: layout === 'grid' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '4px',
              padding: '8px 12px',
              color: layout === 'grid' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              fontSize: '14px',
              cursor: 'pointer',
            }}
          >
            Grid
          </button>
          <button 
            onClick={() => setLayout('list')}
            style={{
              background: layout === 'list' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
              border: layout === 'list' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '4px',
              padding: '8px 12px',
              color: layout === 'list' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              fontSize: '14px',
              cursor: 'pointer',
            }}
          >
            List
          </button>
        </div>
      </div>
      
      <div style={{
        display: layout === 'grid' ? 'grid' : 'flex',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        flexDirection: layout === 'list' ? 'column' : undefined,
        gap: '20px',
      }}>
        {gridItems.map(item => (
          <div 
            key={item.id}
            style={{
              borderRadius: '12px',
              padding: '20px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              height: layout === 'grid' ? '200px' : 'auto',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              overflow: 'hidden',
            }}
          >
            {/* Status indicator */}
            <div style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: '3px',
              background: `linear-gradient(90deg, transparent, ${getStatusColor(item.status)}, transparent)`,
              opacity: 0.8,
            }} />
            
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '16px',
            }}>
              <h3 style={{ 
                fontSize: '16px', 
                fontWeight: 500,
                color: 'white',
              }}>
                {item.title}
              </h3>
              
              <div style={{
                fontSize: '12px',
                color: getStatusColor(item.status),
                textTransform: 'capitalize',
              }}>
                {item.status}
              </div>
            </div>
            
            {/* Content based on item type */}
            {item.type === 'metric' && (
              <div style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <div style={{
                  fontSize: '36px',
                  fontWeight: 'bold',
                  color: getStatusColor(item.status),
                }}>
                  {item.value}
                </div>
              </div>
            )}
            
            {item.type === 'chart' && (
              <div style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                [Chart Visualization]
              </div>
            )}
            
            {item.type === 'status' && (
              <div style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}>
                {['Firewall', 'IDS/IPS', 'Antivirus', 'EDR'].map((service, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                    <span style={{ fontSize: '14px', color: 'white' }}>{service}</span>
                    <span style={{ 
                      fontSize: '12px', 
                      color: index === 3 && item.title === 'System Status' ? '#ff4d4d' : '#00e5ff',
                    }}>
                      {index === 3 && item.title === 'System Status' ? 'Update Required' : 'Active'}
                    </span>
                  </div>
                ))}
              </div>
            )}
            
            {item.type === 'list' && (
              <div style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                overflow: 'hidden',
              }}>
                {[1, 2, 3].map((_, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '14px',
                    padding: '8px 0',
                    borderBottom: index < 2 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                  }}>
                    <span style={{ color: 'white' }}>
                      {item.title === 'Recent Alerts' ? `Alert #${1000 + index}` : `Rule #${42 + index}`}
                    </span>
                    <span style={{ color: 'rgba(255, 255, 255, 0.5)' }}>
                      {item.title === 'Recent Alerts' ? `${index + 1}h ago` : 'Active'}
                    </span>
                  </div>
                ))}
              </div>
            )}
            
            {item.type === 'progress' && (
              <div style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                gap: '16px',
              }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: getStatusColor(item.status), textAlign: 'center' }}>
                  {item.value}
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '4px',
                  overflow: 'hidden',
                }}>
                  <div style={{
                    width: item.value,
                    height: '100%',
                    backgroundColor: getStatusColor(item.status),
                    borderRadius: '4px',
                  }} />
                </div>
              </div>
            )}
            
            {/* Actions */}
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
              marginTop: '16px',
            }}>
              <button style={{
                background: 'transparent',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '6px 12px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                Details
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GridView;