/**
 * Export Utilities
 * 
 * Utility functions for exporting data in various formats
 */

import { ConfigurationEntry } from '../types/configuration';
import { exportDatasetToJSON, exportDatasetToCSV } from '../data/configurationDatasets';

/**
 * Export format options
 */
export type ExportFormat = 'json' | 'csv';

/**
 * Export configuration data to a file
 * @param data Configuration entries to export
 * @param format Export format (json or csv)
 * @param filename Optional filename (without extension)
 * @returns void
 */
export function exportConfigurationData(
  data: ConfigurationEntry[],
  format: ExportFormat,
  filename: string = 'configuration-assessment'
): void {
  try {
    // Generate content based on format
    let content: string;
    let mimeType: string;
    let extension: string;
    
    if (format === 'json') {
      content = exportDatasetToJSON(data);
      mimeType = 'application/json';
      extension = 'json';
    } else {
      content = exportDatasetToCSV(data);
      mimeType = 'text/csv';
      extension = 'csv';
    }
    
    // Create a blob with the content
    const blob = new Blob([content], { type: mimeType });
    
    // Create a download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${filename}.${extension}`;
    
    // Trigger the download
    document.body.appendChild(link);
    link.click();
    
    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    return true;
  } catch (error) {
    console.error('Error exporting configuration data:', error);
    return false;
  }
}