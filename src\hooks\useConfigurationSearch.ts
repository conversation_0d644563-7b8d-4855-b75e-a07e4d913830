import { useMemo } from 'react';
import { useConfigurationAssessment } from '../context/ConfigurationAssessmentContext';
import { configurationAssessmentActions } from '../context/ConfigurationAssessmentActions';
import { ConfigurationFilter } from '../types/configuration';
import { 
  calculateResultDistribution, 
  calculateComplianceDistribution, 
  calculateSeverityDistribution 
} from '../utils/configurationAdapters';

/**
 * Hook for managing search and filtering functionality for configuration assessment data
 * 
 * This hook adapts the search functionality for configuration-specific fields
 * and provides real-time search results updating.
 * 
 * Requirements: 2.1, 2.2
 */
export const useConfigurationSearch = () => {
  const { state, dispatch } = useConfigurationAssessment();
  const { searchQuery, appliedFilters, filteredData, configData } = state;
  
  // Get total count and filtered count
  const counts = useMemo(() => ({
    total: configData.length,
    filtered: filteredData.length,
  }), [configData.length, filteredData.length]);
  
  // Set search query with real-time updating
  const setSearchQuery = (query: string) => {
    dispatch(configurationAssessmentActions.setSearchQuery(query));
  };
  
  // Parse search query into structured filters
  const parseQuery = (query: string): { textSearch: string; filters: ConfigurationFilter[] } => {
    if (!query) {
      return { textSearch: '', filters: [] };
    }

    const filters: ConfigurationFilter[] = [];
    let textSearch = query;

    // Match patterns like field:value, field:"value with spaces", field:(value1 OR value2)
    const fieldPattern = /(\w+(\.\w+)*):(?:"([^"]+)"|([^\s]+))/g;
    let match;

    while ((match = fieldPattern.exec(query)) !== null) {
      const field = match[1]; // Field name (possibly with dot notation)
      const value = match[3] || match[4]; // Quoted or non-quoted value
      
      // Remove the matched filter from the text search
      textSearch = textSearch.replace(match[0], '');
      
      // Add the filter
      filters.push({
        field,
        value,
        operator: 'contains' // Default to contains for text search
      });
    }

    // Clean up the remaining text search
    textSearch = textSearch.replace(/\s+/g, ' ').trim();

    return { textSearch, filters };
  };
  
  // Add filter
  const addFilter = (field: string, value: unknown, operator: 'eq' | 'neq' | 'contains' | 'in' | 'not_in' = 'eq') => {
    dispatch(configurationAssessmentActions.addFilter(field, value, operator));
  };
  
  // Remove filter
  const removeFilter = (field: string) => {
    dispatch(configurationAssessmentActions.removeFilter(field));
  };
  
  // Clear all filters
  const clearFilters = () => {
    appliedFilters.forEach(filter => {
      dispatch(configurationAssessmentActions.removeFilter(filter.field.toString()));
    });
  };
  
  // Convert filters to query string
  const filtersToQueryString = (filters: ConfigurationFilter[], textSearch: string = ''): string => {
    const filterStrings = filters.map(filter => {
      const value = typeof filter.value === 'string' && filter.value.includes(' ')
        ? `"${filter.value}"`
        : filter.value;
      
      return `${filter.field}:${value}`;
    });
    
    return [...filterStrings, textSearch].filter(Boolean).join(' ');
  };
  
  // Get result status distribution
  const resultDistribution = useMemo(() => {
    return calculateResultDistribution(filteredData);
  }, [filteredData]);
  
  // Get compliance standards distribution
  const complianceDistribution = useMemo(() => {
    return calculateComplianceDistribution(filteredData);
  }, [filteredData]);
  
  // Get severity distribution
  const severityDistribution = useMemo(() => {
    return calculateSeverityDistribution(filteredData);
  }, [filteredData]);
  
  return {
    searchQuery,
    appliedFilters,
    filteredData,
    counts,
    setSearchQuery,
    parseQuery,
    addFilter,
    removeFilter,
    clearFilters,
    filtersToQueryString,
    resultDistribution,
    complianceDistribution,
    severityDistribution,
  };
};