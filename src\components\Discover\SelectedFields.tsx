import React from 'react';


interface SelectedFieldsProps {
  fields: string[];
  onRemoveField: (field: string) => void;
}

/**
 * Component for displaying and managing selected fields in the Discover sidebar
 */
const SelectedFields: React.FC<SelectedFieldsProps> = ({ 
  fields, 
  onRemoveField 
}) => {
  // const { formatFieldValue } = useDiscoverFields();
  
  return (
    <div style={{ marginBottom: '24px' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '8px'
      }}>
        <h4 style={{ 
          color: 'white', 
          margin: 0,
          fontSize: '14px',
          textTransform: 'uppercase',
          letterSpacing: '1px',
        }}>
          Selected Fields
        </h4>
        {fields.length > 0 && (
          <button
            onClick={() => {
              // Remove all fields one by one
              [...fields].forEach(field => onRemoveField(field));
            }}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#00e5ff',
              cursor: 'pointer',
              fontSize: '12px',
              padding: '2px 4px',
            }}
          >
            Clear All
          </button>
        )}
      </div>
      
      <div style={{ 
        background: 'rgba(0, 0, 0, 0.2)', 
        borderRadius: '4px',
        padding: '8px',
        maxHeight: '200px',
        overflowY: 'auto',
      }}>
        {fields.length === 0 ? (
          <p style={{ color: 'rgba(255, 255, 255, 0.5)', fontSize: '14px', margin: '4px 0' }}>
            No fields selected
          </p>
        ) : (
          <ul style={{ 
            listStyle: 'none', 
            padding: 0, 
            margin: 0,
          }}>
            {fields.map(field => (
              <li 
                key={field}
                style={{
                  padding: '6px 8px',
                  marginBottom: '4px',
                  background: 'rgba(0, 229, 255, 0.1)',
                  borderRadius: '4px',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  color: 'white',
                  fontSize: '14px',
                }}
              >
                <span style={{ 
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  marginRight: '8px'
                }}>
                  {field}
                </span>
                <button
                  onClick={() => onRemoveField(field)}
                  aria-label={`Remove ${field} field`}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: '#00e5ff',
                    cursor: 'pointer',
                    padding: '2px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0,
                  }}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default SelectedFields;