import React from 'react';

export interface ExploreItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  route: string;
  description?: string;
}

export interface NavigationState {
  activeNav: string;
  exploreExpanded: boolean;
  activeExploreItem?: string;
  collapsed: boolean;
}

export interface ExploreNavItemProps {
  isCollapsed: boolean;
  isExpanded: boolean;
  onToggle: () => void;
  activeSubItem?: string;
  isActive?: boolean;
}

export interface ExploreDropdownProps {
  isExpanded: boolean;
  isCollapsed: boolean;
  activeItem?: string;
  onItemClick: (item: string) => void;
}

export interface ExploreSubItemProps {
  id: string;
  label: string;
  icon: React.ComponentType;
  isActive: boolean;
  isCollapsed: boolean;
  onClick: (id: string) => void;
}

export type EndpointSecurityItem = ExploreItem;