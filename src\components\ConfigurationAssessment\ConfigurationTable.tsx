import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useConfigurationEntries } from '../../hooks';
import ConfigurationEntryRow from './ConfigurationEntryRow';
import ConfigurationEntryDetails from './ConfigurationEntryDetails';
import TableColumnResizer from './TableColumnResizer';
import PaginationControls from './PaginationControls';
import PageSizeSelector from './PageSizeSelector';
import { configurationFieldMappings } from '../../utils/configurationAdapters';
import { useConfigurationAssessment } from '../../context/ConfigurationAssessmentContext';
import { configurationAssessmentActions } from '../../context/ConfigurationAssessmentActions';
import { usePerformanceMonitoring } from '../../hooks/usePerformanceMonitoring';
import PerformanceMonitor from './PerformanceMonitor';

/**
 * Table component for the Configuration Assessment page
 * Displays configuration entries with expandable details and virtual scrolling
 */
const ConfigurationTable: React.FC = () => {
  // Initialize performance monitoring
  const performance = usePerformanceMonitoring('ConfigurationTable', 'DiscoverTable');
  // Add loading animation style and column header hover effect
  useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      @keyframes loading-animation {
        0% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      .config-column-header:hover {
        background-color: rgba(0, 229, 255, 0.05) !important;
      }
    `;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  const {
    entries,
    allEntries,
    selectedFields,
    toggleEntryExpansion,
    isEntryExpanded,
    pagination
  } = useConfigurationEntries();

  // Refs for virtual scrolling
  const tableRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // Get context for sorting
  const { state, dispatch } = useConfigurationAssessment();

  // State for virtual scrolling and column management
  const [visibleStartIndex, setVisibleStartIndex] = useState(0);
  const [visibleEndIndex, setVisibleEndIndex] = useState(50);
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});

  // Constants for virtual scrolling - optimized with more adaptive buffer size
  const rowHeight = 40; // Approximate height of each row
  const minBufferSize = 5; // Minimum buffer size for small viewports
  const maxBufferSize = 20; // Maximum buffer size for large viewports
  
  // Calculate optimal buffer size based on viewport height
  const getOptimalBufferSize = useCallback(() => {
    if (!tableRef.current) return minBufferSize;
    
    const containerHeight = tableRef.current.clientHeight;
    const visibleRows = Math.ceil(containerHeight / rowHeight);
    
    // Scale buffer size based on visible rows, but keep within limits
    return Math.min(maxBufferSize, Math.max(minBufferSize, Math.floor(visibleRows / 2)));
  }, [rowHeight, minBufferSize, maxBufferSize]);

  // Calculate visible rows based on scroll position with optimized algorithm
  // FIXED: Removed performance tracking wrapper that was causing infinite update loop
  const calculateVisibleRows = useCallback(() => {
    if (!tableRef.current) return;

    const containerHeight = tableRef.current.clientHeight;
    const scrollPosition = tableRef.current.scrollTop;
    const bufferSize = getOptimalBufferSize();
    
    // Calculate visible range with dynamic buffer
    const startIndex = Math.max(0, Math.floor(scrollPosition / rowHeight) - bufferSize);
    const endIndex = Math.min(
      entries.length - 1,
      Math.ceil((scrollPosition + containerHeight) / rowHeight) + bufferSize
    );
    
    // Only update state if the range has changed significantly (at least 25% of buffer size)
    // This prevents excessive re-renders during small scroll movements
    const bufferThreshold = Math.max(1, Math.floor(bufferSize * 0.25));
    const startDiff = Math.abs(startIndex - visibleStartIndex);
    const endDiff = Math.abs(endIndex - visibleEndIndex);
    
    if (startDiff >= bufferThreshold || endDiff >= bufferThreshold) {
      setVisibleStartIndex(startIndex);
      setVisibleEndIndex(endIndex);
    }
  }, [entries.length, rowHeight, visibleStartIndex, visibleEndIndex, getOptimalBufferSize]);

  // Handle scroll event with throttling to prevent excessive calculations
  const handleScroll = useCallback(() => {
    // Use requestAnimationFrame for smoother scrolling
    requestAnimationFrame(calculateVisibleRows);
  }, [calculateVisibleRows]);

  // Initialize column widths
  const initializeColumnWidths = useCallback(() => {
    if (!headerRef.current) return;

    const headerCells = headerRef.current.querySelectorAll('[data-field]');
    const newWidths: Record<string, number> = {};

    headerCells.forEach((cell) => {
      const field = cell.getAttribute('data-field');
      if (field) {
        // Use predefined widths from field mappings if available
        const mapping = configurationFieldMappings[field as keyof typeof configurationFieldMappings];
        if (mapping && mapping.width) {
          newWidths[field] = parseInt(mapping.width, 10);
        } else {
          newWidths[field] = cell.clientWidth;
        }
      }
    });

    setColumnWidths(newWidths);
  }, []);

  // Initialize virtual scrolling
  useEffect(() => {
    calculateVisibleRows();
    initializeColumnWidths();

    window.addEventListener('resize', initializeColumnWidths);
    return () => {
      window.removeEventListener('resize', initializeColumnWidths);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initializeColumnWidths]);

  // Update visible rows when entries change
  useEffect(() => {
    calculateVisibleRows();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entries]);

  // Get visible entries - FIXED: Removed performance tracking wrapper
  const visibleEntries = useMemo(() => {
    return entries.slice(visibleStartIndex, visibleEndIndex + 1);
  }, [entries, visibleStartIndex, visibleEndIndex]);

  // Calculate total height of all rows
  const totalHeight = entries.length * rowHeight;

  // Calculate padding to maintain scroll position
  const topPadding = visibleStartIndex * rowHeight;
  const bottomPadding = Math.max(0, totalHeight - topPadding - visibleEntries.length * rowHeight);

  // Handle column click for sorting - FIXED: Removed performance tracking
  const handleColumnClick = (field: string) => {
    // Set loading state to indicate sorting is in progress
    dispatch({ type: 'SET_LOADING', payload: true });

    if (state.sort && state.sort.field === field) {
      // Toggle sort direction if clicking the same field
      const newDirection = state.sort.direction === 'asc' ? 'desc' : 'asc';
      dispatch(configurationAssessmentActions.setSort(field, newDirection));
    } else {
      // Set new sort field and default to descending
      dispatch(configurationAssessmentActions.setSort(field, 'desc'));
    }

    // Simulate loading time for sorting
    setTimeout(() => {
      dispatch({ type: 'SET_LOADING', payload: false });
    }, 300);
  };

  // Add keyboard event listener for sorting shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt+S to clear sorting
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        dispatch(configurationAssessmentActions.clearSort());
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [dispatch]);

  // Handle column resize
  const handleColumnResize = (field: string, width: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [field]: width
    }));
  };

  // Get display name for field
  const getFieldDisplayName = (field: string): string => {
    const mapping = configurationFieldMappings[field as keyof typeof configurationFieldMappings];
    return mapping ? mapping.displayName : field;
  };

  // FIXED: Removed interval that could cause issues
  // Log performance metrics in development mode only once on mount
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') {
      performance.logPerformance(false);
    }
  }, [performance]);

  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {/* Performance Monitor (only in development mode, hidden by default) */}
      {process.env.NODE_ENV !== 'production' && <PerformanceMonitor hidden={true} />}
      {/* Scrollable table area (header + body) */}
      <div style={{
        flex: 1,
        minHeight: 0,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}>
        {/* Table header */}
        <div
          ref={headerRef}
          role="row"
          aria-rowindex={1}
          style={{
            display: 'flex',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
            background: 'rgba(10, 14, 23, 0.8)',
            padding: '8px 16px',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '14px',
            position: 'sticky',
            top: 0,
            zIndex: 10,
          }}
        >
          <div style={{ width: '30px' }} role="columnheader" aria-hidden="true"></div>
          {selectedFields.map((field, index) => (
            <div
              key={field}
              data-field={field}
              role="columnheader"
              aria-sort={state.sort && state.sort.field === field ? (state.sort.direction === 'asc' ? 'ascending' : 'descending') : 'none'}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleColumnClick(field);
                }
              }}
              style={{
                flexGrow: field === 'rule.description' ? 3 : 1,
                padding: '0 8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'pointer',
                userSelect: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                position: 'relative',
                width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                transition: 'background-color 0.2s ease',
                borderRadius: '4px',
                backgroundColor: state.sort && state.sort.field === field ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
              }}
              onClick={() => handleColumnClick(field)}
              title={`Sort by ${getFieldDisplayName(field)}`}
            >
              <span>{getFieldDisplayName(field)}</span>
              <div
                aria-hidden="true"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '20px',
                  height: '20px',
                  marginLeft: '4px',
                  color: state.sort && state.sort.field === field ? 'rgba(0, 229, 255, 0.9)' : 'rgba(255, 255, 255, 0.5)',
                  opacity: state.sort && state.sort.field === field ? 1 : 0.5,
                  transition: 'transform 0.2s ease, color 0.2s ease',
                }}
              >
                {state.sort && state.sort.field === field ? (
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    style={{
                      transform: state.sort.direction === 'asc' ? 'rotate(180deg)' : 'none',
                      transition: 'transform 0.2s ease',
                    }}
                  >
                    <path d="M6 9l6 6 6-6" />
                  </svg>
                ) : (
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <path d="M7 15l5-5 5 5" />
                    <path d="M7 9l5-5 5 5" />
                  </svg>
                )}
              </div>
              <TableColumnResizer
                index={index}
                onResize={(_, width) => handleColumnResize(field, width)}
              />
            </div>
          ))}
        </div>
        {/* Table body with virtual scrolling */}
        <div
          ref={tableRef}
          style={{
            flex: 1,
            minHeight: 0,
            overflowY: 'auto',
            position: 'relative',
          }}
          onScroll={handleScroll}
        >
          {/* Enhanced loading indicators with better visual feedback */}
          {(state.isLoading || state.processingOperation) && (
            <>
              {/* Top loading bar */}
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '3px',
                background: 'linear-gradient(to right, rgba(0, 229, 255, 0), rgba(0, 229, 255, 0.8), rgba(0, 229, 255, 0))',
                backgroundSize: '200% 100%',
                animation: 'loading-animation 1.5s infinite',
                zIndex: 20,
              }} />
              
              {/* Subtle overlay for longer operations */}
              {state.processingOperation && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  zIndex: 10,
                  pointerEvents: 'none', // Allow clicks to pass through
                  transition: 'opacity 0.3s ease',
                }} />
              )}
            </>
          )}
          {entries.length === 0 ? (
            <div style={{
              padding: '24px',
              textAlign: 'center',
              color: 'rgba(255, 255, 255, 0.7)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px',
              gap: '16px',
            }}>
              <div style={{
                fontSize: '18px',
                fontWeight: 'bold',
              }}>
                No configuration entries found
              </div>
              <div style={{
                fontSize: '14px',
                maxWidth: '500px',
                lineHeight: '1.5',
              }}>
                No configuration entries match your current filters. Try adjusting your search criteria or time range.
              </div>
            </div>
          ) : (
            <div style={{ height: totalHeight, position: 'relative' }}>
              <div style={{ paddingTop: topPadding, paddingBottom: bottomPadding }}>
                {visibleEntries.map(entry => (
                  <React.Fragment key={entry.id}>
                    {/* Entry row */}
                    <ConfigurationEntryRow
                      entry={entry}
                      isExpanded={isEntryExpanded(entry.id)}
                      onToggleExpand={() => toggleEntryExpansion(entry.id)}
                      selectedFields={selectedFields}
                      columnWidths={columnWidths}
                    />
                    {/* Expanded entry details */}
                    {isEntryExpanded(entry.id) && (
                      <ConfigurationEntryDetails
                        entry={entry}
                        onClose={() => toggleEntryExpansion(entry.id)}
                      />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Pagination controls (always visible at bottom) */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px 0',
        background: 'rgba(10, 14, 23, 0.8)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        position: 'relative', // Added for loading indicator positioning
      }}>
        <PaginationControls
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.setCurrentPage}
          onFirstPage={pagination.goToFirstPage}
          onPreviousPage={pagination.goToPreviousPage}
          onNextPage={pagination.goToNextPage}
          onLastPage={pagination.goToLastPage}
          isFirstPage={pagination.isFirstPage}
          isLastPage={pagination.isLastPage}
          isLoading={pagination.isLoading}
          noResults={pagination.noResults}
          processingOperation={pagination.processingOperation}
        />
      </div>
      {/* Status bar (always visible at very bottom) */}
      <div style={{
        padding: '8px 16px',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        background: 'rgba(10, 14, 23, 0.8)',
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {pagination.noResults ? (
            <span style={{ color: 'rgba(255, 255, 255, 0.5)', fontStyle: 'italic' }}>
              No configuration entries match your current filters
            </span>
          ) : (
            <span>{`${allEntries.length} configuration entries found`}</span>
          )}

          {/* Sort indicator */}
          {state.sort && (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '4px',
              background: 'rgba(0, 229, 255, 0.1)',
              padding: '2px 6px',
              borderRadius: '4px',
              border: '1px solid rgba(0, 229, 255, 0.2)',
            }}>
              <span style={{ color: 'rgba(255, 255, 255, 0.7)' }}>Sorted by:</span>
              <span style={{ color: '#00e5ff', fontWeight: 'bold' }}>
                {getFieldDisplayName(state.sort.field)}
              </span>
              <svg
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#00e5ff"
                strokeWidth="2"
                style={{
                  transform: state.sort.direction === 'asc' ? 'rotate(180deg)' : 'none',
                }}
              >
                <path d="M6 9l6 6 6-6" />
              </svg>
              <button
                onClick={() => dispatch(configurationAssessmentActions.clearSort())}
                style={{
                  background: 'transparent',
                  border: 'none',
                  padding: '0 0 0 4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  color: 'rgba(255, 255, 255, 0.5)',
                }}
                title="Clear sorting (Alt+S)"
              >
                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M18 6L6 18M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}
        </div>

        <PageSizeSelector
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.setPageSize}
          options={[10, 25, 50, 100]}
          noResults={pagination.noResults}
        />

        <div style={{
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '14px',
          fontFamily: 'monospace',
        }}>
          {pagination.showingText}
        </div>
      </div>
    </div>
  );
};

export default ConfigurationTable;