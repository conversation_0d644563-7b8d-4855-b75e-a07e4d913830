import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseSearchInterceptor } from './base-search-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../interfaces';

/**
 * A custom search interceptor for the sample data source.
 * This interceptor transforms search requests for the custom sample data type
 * and adds additional processing to the search responses.
 */
export class CustomSampleInterceptor extends BaseSearchInterceptor {
  /**
   * Processes the search request and transforms it before passing it to the next interceptor.
   * @param request The search request to process
   * @param options Options for the search execution
   * @returns An Observable of the search response
   */
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    // Check if this request is for a custom sample dataset
    if (request.index && request.index.startsWith('custom-')) {
      // Clone the request to avoid modifying the original
      const modifiedRequest: SearchRequest = { ...request };
      
      // Transform the query if it's using the simple-query language
      if (modifiedRequest.query && modifiedRequest.query.language === 'simple-query') {
        // Convert simple-query to a format that the search strategy can understand
        modifiedRequest.query = this.transformSimpleQuery(modifiedRequest.query);
      }
      
      // Add custom fields for the sample data source
      if (!modifiedRequest.fields || modifiedRequest.fields.length === 0) {
        // Add default fields based on the dataset type
        if (modifiedRequest.index.includes('transactions')) {
          modifiedRequest.fields = ['timestamp', 'id', 'amount', 'status', 'user_id', 'product_id'];
        } else if (modifiedRequest.index.includes('users')) {
          modifiedRequest.fields = ['timestamp', 'id', 'name', 'email', 'age', 'active'];
        } else if (modifiedRequest.index.includes('products')) {
          modifiedRequest.fields = ['timestamp', 'id', 'name', 'price', 'category', 'in_stock'];
        }
      }
      
      // Pass the modified request to the next interceptor or execute the search
      const response$ = super.search(modifiedRequest, options);
      
      // Transform the response
      return response$.pipe(
        map(response => {
          // Add custom metadata to the response
          const enhancedResponse: SearchResponse = {
            ...response,
            meta: {
              ...response.meta,
              dataSource: 'custom-sample',
              transformedBy: 'CustomSampleInterceptor',
              timestamp: new Date().toISOString()
            }
          };
          
          // Add custom formatting for specific fields
          if (enhancedResponse.hits && enhancedResponse.hits.hits) {
            enhancedResponse.hits.hits = enhancedResponse.hits.hits.map(hit => {
              // Format amount field for transactions
              if (hit._source && hit._source.amount !== undefined) {
                hit._source.formattedAmount = `$${hit._source.amount.toFixed(2)}`;
              }
              
              // Format active field for users
              if (hit._source && hit._source.active !== undefined) {
                hit._source.activeStatus = hit._source.active ? 'Active' : 'Inactive';
              }
              
              // Format in_stock field for products
              if (hit._source && hit._source.in_stock !== undefined) {
                hit._source.stockStatus = hit._source.in_stock ? 'In Stock' : 'Out of Stock';
              }
              
              return hit;
            });
          }
          
          return enhancedResponse;
        })
      );
    }
    
    // If not a custom sample dataset, pass through unchanged
    return super.search(request, options);
  }
  
  /**
   * Transforms a simple query to a format that the search strategy can understand
   * @param query The query to transform
   * @returns The transformed query
   */
  private transformSimpleQuery(query: unknown): unknown {
    // Clone the query to avoid modifying the original
    const transformedQuery = { ...query };
    
    // For simple-query language, we'll convert it to a format that works with our search strategy
    // This is a simplified example - in a real implementation, this would be more complex
    if (query.query) {
      // Replace simple-query syntax with a format the search strategy understands
      // For example, convert "field:value AND field2:value2" to a structured query
      
      const simpleQueryStr = query.query;
      
      // Simple parsing of field:value pairs
      const fieldValuePairs = simpleQueryStr.split(/\s+AND\s+|\s+OR\s+/);
      const operators = simpleQueryStr.match(/\s+AND\s+|\s+OR\s+/g) || [];
      
      // Build a structured query
      const structuredQuery = {
        bool: {
          must: [] as unknown[],
          should: [] as unknown[],
          filter: [] as unknown[]
        }
      };
      
      let currentClause = 'must'; // Default to AND (must)
      
      fieldValuePairs.forEach((pair, index) => {
        const [field, value] = pair.split(':').map(s => s.trim());
        
        if (field && value) {
          const termQuery = {
            term: {
              [field]: value.replace(/"/g, '') // Remove quotes if present
            }
          };
          
          structuredQuery.bool[currentClause].push(termQuery);
        }
        
        // Update the clause for the next pair based on the operator
        if (index < operators.length) {
          currentClause = operators[index].includes('OR') ? 'should' : 'must';
        }
      });
      
      // Set the transformed query
      transformedQuery.query = JSON.stringify(structuredQuery);
      transformedQuery.language = 'structured';
    }
    
    return transformedQuery;
  }
}