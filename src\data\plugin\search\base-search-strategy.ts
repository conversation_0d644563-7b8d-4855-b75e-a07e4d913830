import { SearchStrategy } from './search-strategy';
import { SearchRequest, SearchResponse, DataFrame } from '../interfaces';
import { DataFrameTransformer } from './data-frame-transformer';

/**
 * Base class for search strategies that provides common functionality
 */
export abstract class BaseSearchStrategy implements SearchStrategy {
  /**
   * The unique identifier for this search strategy
   */
  public abstract id: string;
  
  /**
   * Executes a search request
   * @param request The search request to execute
   * @param options Additional options for the search
   * @returns A promise that resolves to the search response
   */
  public async search(request: SearchRequest, options?: { signal?: AbortSignal }): Promise<SearchResponse> {
    try {
      // Check if the request has been aborted
      if (options?.signal?.aborted) {
        throw new Error('Search request was aborted');
      }
      
      // Execute the search
      const response = await this.executeSearch(request, options);
      
      // Return the response
      return response;
    } catch (error) {
      // Handle errors
      console.error(`Error executing search with strategy ${this.id}:`, error);
      
      // Return an error response
      return {
        hits: {
          total: 0,
          hits: []
        },
        took: 0,
        timed_out: true,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          type: error instanceof Error ? error.name : '<PERSON>rror'
        }
      };
    }
  }
  
  /**
   * Transforms a search response to DataFrame format
   * @param response The search response to transform
   * @returns The DataFrame representation of the search response
   */
  public toDataFrame(response: SearchResponse): DataFrame {
    return DataFrameTransformer.transform(response);
  }
  
  /**
   * Abstract method that must be implemented by concrete strategies
   * @param request The search request to execute
   * @param options Additional options for the search
   * @returns A promise that resolves to the search response
   */
  protected abstract executeSearch(request: SearchRequest, options?: { signal?: AbortSignal }): Promise<SearchResponse>;
}