import { SearchResponse, DataFrame } from '../interfaces';

/**
 * Utility class for transforming search responses to DataFrame format
 */
export class DataFrameTransformer {
  /**
   * Transforms a search response to DataFrame format
   * @param response The search response to transform
   * @returns The DataFrame representation of the search response
   */
  public static transform(response: SearchResponse): DataFrame {
    if (!response.hits || !Array.isArray(response.hits.hits)) {
      return {
        columns: [],
        rows: [],
        meta: {
          total: 0,
          took: response.took || 0
        }
      };
    }

    // Extract all unique field names from the hits
    const fieldNames = new Set<string>();
    response.hits.hits.forEach(hit => {
      if (hit._source) {
        Object.keys(hit._source).forEach(key => fieldNames.add(key));
      }
      if (hit.fields) {
        Object.keys(hit.fields).forEach(key => fieldNames.add(key));
      }
    });

    // Add _id as a field
    fieldNames.add('_id');

    // Create columns
    const columns = Array.from(fieldNames).map(name => ({
      id: name,
      name,
      type: name === '_id' ? 'string' : this.inferType(response.hits.hits, name)
    }));

    // Create rows
    const rows = response.hits.hits.map(hit => {
      const row: Record<string, unknown> = { _id: hit._id };
      
      // Add source fields
      if (hit._source) {
        Object.entries(hit._source).forEach(([key, value]) => {
          row[key] = value;
        });
      }
      
      // Add fields (usually from field formatters)
      if (hit.fields) {
        Object.entries(hit.fields).forEach(([key, value]) => {
          // Fields are always arrays in Elasticsearch responses
          row[key] = Array.isArray(value) && value.length === 1 ? value[0] : value;
        });
      }
      
      return row;
    });

    return {
      columns,
      rows,
      meta: {
        total: response.hits.total,
        took: response.took
      }
    };
  }

  /**
   * Infers the type of a field based on its values in the hits
   * @param hits The search hits
   * @param fieldName The name of the field
   * @returns The inferred type of the field
   */
  private static inferType(hits: SearchResponse['hits']['hits'], fieldName: string): string {
    // Try to find a non-null value for the field
    for (const hit of hits) {
      const value = hit._source?.[fieldName] ?? hit.fields?.[fieldName]?.[0];
      if (value !== undefined && value !== null) {
        const type = typeof value;
        
        if (type === 'object') {
          if (Array.isArray(value)) {
            return 'array';
          }
          if (value instanceof Date || (typeof value === 'string' && !isNaN(Date.parse(value)))) {
            return 'date';
          }
          return 'object';
        }
        
        return type;
      }
    }
    
    // Default to string if no non-null values are found
    return 'string';
  }
}