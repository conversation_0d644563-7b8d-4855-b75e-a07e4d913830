import React, { useState } from 'react';
import QueryEditor from '../query/QueryEditor';
import { Query } from '../../data/plugin/interfaces';

import { useSearch } from '../../hooks/dataHooks';

/**
 * Example component that demonstrates how to use the QueryEditor
 */
const QueryEditorExample: React.FC = () => {
  const { results, isLoading, error } = useSearch();
  const [currentQuery, setCurrentQuery] = useState<Query>({
    query: '',
    language: 'kuery'
  });
  
  // Handle query submit
  const handleQuerySubmit = (query: Query) => {
    setCurrentQuery(query);
    console.log('Query submitted:', query);
  };
  
  return (
    <div className="query-editor-example">
      <h2>Query Editor Example</h2>
      
      <div className="example-container">
        <div className="editor-container">
          <h3>Query Editor</h3>
          <QueryEditor
            initialQuery="source: web-server-01"
            initialLanguage="kuery"
            onSubmit={handleQuerySubmit}
            placeholder="Try typing 'source:' or 'level:'"
            autoFocus={true}
          />
        </div>
        
        <div className="query-info">
          <h3>Current Query</h3>
          <pre className="query-json">
            {JSON.stringify(currentQuery, null, 2)}
          </pre>
        </div>
      </div>
      
      <div className="results-container">
        <h3>Search Results</h3>
        
        {isLoading ? (
          <p>Loading...</p>
        ) : error ? (
          <div className="error-message">
            <p>Error: {error.message}</p>
          </div>
        ) : results ? (
          <div className="results-info">
            <p>Total hits: {results.hits.total}</p>
            <p>Took: {results.took}ms</p>
            
            {results.hits.hits.length > 0 ? (
              <div className="results-table-container">
                <table className="results-table">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Source</th>
                      <th>Message</th>
                    </tr>
                  </thead>
                  <tbody>
                    {results.hits.hits.slice(0, 5).map((hit) => (
                      <tr key={hit._id}>
                        <td>{hit._id}</td>
                        <td>{hit._source.source}</td>
                        <td>{hit._source.message}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {results.hits.hits.length > 5 && (
                  <p>Showing 5 of {results.hits.hits.length} results</p>
                )}
              </div>
            ) : (
              <p>No results found</p>
            )}
          </div>
        ) : (
          <p>No search executed yet</p>
        )}
      </div>
      
      <div className="usage-info">
        <h3>How to Use</h3>
        <pre>{`
import QueryEditor from '../query/QueryEditor';
import { Query } from '../../data/plugin/interfaces';

const MyComponent: React.FC = () => {
  const handleQuerySubmit = (query: Query) => {
    console.log('Query submitted:', query);
    // Do something with the query
  };
  
  return (
    <QueryEditor
      initialQuery="source: web-server-01"
      initialLanguage="kuery"
      onSubmit={handleQuerySubmit}
      placeholder="Enter a query..."
      autoFocus={true}
    />
  );
};
        `}</pre>
      </div>
      
      <div className="features-info">
        <h3>Features</h3>
        <ul>
          <li>
            <strong>Language-aware syntax highlighting</strong> - 
            The query is highlighted based on the selected language
          </li>
          <li>
            <strong>Autocomplete suggestions</strong> - 
            Type to see suggestions based on the current context
          </li>
          <li>
            <strong>Validation</strong> - 
            The query is validated in real-time with error messages
          </li>
          <li>
            <strong>Multiple languages</strong> - 
            Switch between different query languages
          </li>
          <li>
            <strong>Dataset integration</strong> - 
            The query is associated with the selected dataset
          </li>
        </ul>
      </div>
    </div>
  );
};

export default QueryEditorExample;