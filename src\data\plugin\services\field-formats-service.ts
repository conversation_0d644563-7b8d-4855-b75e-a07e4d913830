import { FieldFormatsService, FieldFormat } from '../interfaces';

/**
 * Implementation of the FieldFormatsService interface
 */
export class FieldFormatsServiceImpl implements FieldFormatsService {
  private formats: Map<string, Map<string, FieldFormat>> = new Map();
  private defaultFormats: Map<string, FieldFormat> = new Map();

  constructor() {
    // Initialize with some default formatters
    this.registerDefaultFormatters();
  }

  /**
   * Gets the default format for a field type
   * @param fieldType The field type
   * @returns The default field format
   */
  public getDefaultFormat(fieldType: string): FieldFormat {
    return this.defaultFormats.get(fieldType) || this.getStringFormat();
  }

  /**
   * Gets a specific format for a field type
   * @param fieldType The field type
   * @param formatId The format ID
   * @returns The field format or undefined if not found
   */
  public getFormat(fieldType: string, formatId: string): FieldFormat | undefined {
    const typeFormats = this.formats.get(fieldType);
    return typeFormats ? typeFormats.get(formatId) : undefined;
  }

  /**
   * Registers a new field format
   * @param fieldType The field type
   * @param format The field format
   */
  public registerFormat(fieldType: string, format: FieldFormat): void {
    if (!this.formats.has(fieldType)) {
      this.formats.set(fieldType, new Map());
    }
    
    const typeFormats = this.formats.get(fieldType)!;
    typeFormats.set(format.id, format);
    
    // If this is the first format for this type, set it as default
    if (!this.defaultFormats.has(fieldType)) {
      this.defaultFormats.set(fieldType, format);
    }
  }

  /**
   * Sets a format as the default for a field type
   * @param fieldType The field type
   * @param formatId The format ID
   */
  public setDefaultFormat(fieldType: string, formatId: string): void {
    const format = this.getFormat(fieldType, formatId);
    if (format) {
      this.defaultFormats.set(fieldType, format);
    }
  }

  /**
   * Registers default formatters for common field types
   */
  private registerDefaultFormatters(): void {
    // String formatter
    this.registerFormat('string', this.getStringFormat());
    
    // Number formatter
    this.registerFormat('number', {
      id: 'number',
      title: 'Number',
      format: (value: unknown) => {
        if (value === null || value === undefined) return '';
        return Number(value).toString();
      }
    });
    
    // Date formatter
    this.registerFormat('date', {
      id: 'date',
      title: 'Date',
      format: (value: unknown) => {
        if (value === null || value === undefined) return '';
        try {
          return new Date(value as string | number | Date).toLocaleString();
        } catch {
          return String(value);
        }
      }
    });
    
    // Boolean formatter
    this.registerFormat('boolean', {
      id: 'boolean',
      title: 'Boolean',
      format: (value: unknown) => {
        if (value === null || value === undefined) return '';
        return Boolean(value).toString();
      }
    });
  }

  /**
   * Gets the default string format
   * @returns The string field format
   */
  private getStringFormat(): FieldFormat {
    return {
      id: 'string',
      title: 'String',
      format: (value: unknown) => {
        return String(value);
      }
    };
  }
}