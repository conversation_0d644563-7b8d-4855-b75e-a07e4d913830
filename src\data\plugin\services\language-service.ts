import { LanguageService, LanguageConfig } from '../interfaces';

/**
 * Implementation of the LanguageService interface
 * Responsible for managing query languages and their configurations
 */
export class LanguageServiceImpl implements LanguageService {
  private languages: Map<string, LanguageConfig> = new Map();
  
  /**
   * Gets all registered languages
   * @returns Array of language configurations
   */
  public getLanguages(): LanguageConfig[] {
    return Array.from(this.languages.values());
  }
  
  /**
   * Gets a language configuration by ID
   * @param id The language ID
   * @returns The language configuration or undefined if not found
   */
  public getLanguage(id: string): LanguageConfig | undefined {
    return this.languages.get(id);
  }
  
  /**
   * Registers a new language configuration
   * @param config The language configuration
   */
  public registerLanguage(config: LanguageConfig): void {
    if (!config || !config.id) {
      throw new Error('Invalid language configuration');
    }
    
    this.languages.set(config.id, config);
    console.log(`Registered language: ${config.id}`);
  }
  
  /**
   * Validates a query using the appropriate language configuration
   * @param query The query string
   * @param languageId The language ID
   * @returns Validation result with valid flag and optional error message
   */
  public validateQuery(query: string, languageId: string): { valid: boolean; error?: string } {
    const language = this.getLanguage(languageId);
    
    if (!language) {
      return { valid: false, error: `Language '${languageId}' not found` };
    }
    
    return language.syntax.validate(query);
  }
  
  /**
   * Highlights a query using the appropriate language configuration
   * @param query The query string
   * @param languageId The language ID
   * @returns Highlighted query string or original query if language not found
   */
  public highlightQuery(query: string, languageId: string): string {
    const language = this.getLanguage(languageId);
    
    if (!language) {
      console.warn(`Language '${languageId}' not found for highlighting`);
      return query;
    }
    
    return language.syntax.highlight(query);
  }
  
  /**
   * Gets completions for a query at a specific position
   * @param query The query string
   * @param position The cursor position in the query
   * @param languageId The language ID
   * @returns Promise resolving to an array of completions or empty array if not supported
   */
  public async getCompletions(
    query: string, 
    position: number, 
    languageId: string
  ): Promise<{ value: string; score: number; meta?: string }[]> {
    const language = this.getLanguage(languageId);
    
    if (!language || !language.autocomplete) {
      return [];
    }
    
    try {
      return await language.autocomplete.getCompletions(query, position);
    } catch (error) {
      console.error(`Error getting completions for language '${languageId}':`, error);
      return [];
    }
  }
  
  /**
   * Checks if a language supports autocomplete
   * @param languageId The language ID
   * @returns True if the language supports autocomplete
   */
  public supportsAutocomplete(languageId: string): boolean {
    const language = this.getLanguage(languageId);
    return !!language && !!language.autocomplete;
  }
  
  /**
   * Removes a language configuration
   * @param languageId The language ID to remove
   */
  public removeLanguage(languageId: string): void {
    if (this.languages.delete(languageId)) {
      console.log(`Removed language: ${languageId}`);
    }
  }
}