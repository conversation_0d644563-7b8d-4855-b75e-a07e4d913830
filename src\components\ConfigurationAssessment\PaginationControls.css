@keyframes loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(0, 229, 255, 0) 0%, rgba(0, 229, 255, 0.8) 50%, rgba(0, 229, 255, 0) 100%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  z-index: 1;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.processing-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(0, 229, 255, 0.8);
  margin-left: 8px;
  animation: pulse 1.5s infinite;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  background: rgba(10, 14, 23, 0.8);
  color: rgba(0, 229, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-button:disabled {
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  opacity: 0.7;
}

.pagination-button:not(:disabled):hover {
  background-color: rgba(0, 229, 255, 0.1);
  border-color: rgba(0, 229, 255, 0.5);
}

.pagination-info {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  background: rgba(10, 14, 23, 0.8);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-family: monospace;
  user-select: none;
  position: relative;
}