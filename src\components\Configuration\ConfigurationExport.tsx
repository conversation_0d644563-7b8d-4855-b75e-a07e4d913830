import React, { useState, useRef, useEffect } from 'react';
import { useConfigurationAssessment } from '../../context/ConfigurationAssessmentContext';
import { exportConfigurationData, ExportFormat } from '../../utils/exportUtils';
import ConfigurationNotification from './ConfigurationNotification';

/**
 * Export options component for Configuration Assessment
 * Allows exporting configuration data in various formats
 * 
 * Requirements: 4.1, 4.2, 4.3, 4.4
 */
const ConfigurationExport: React.FC = () => {
  const { state } = useConfigurationAssessment();
  const { filteredData } = state;
  
  // State for dropdown
  const [isExportMenuOpen, setIsExportMenuOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [notification, setNotification] = useState<{
    show: boolean;
    type: 'success' | 'error';
    message: string;
  }>({ show: false, type: 'success', message: '' });
  const [exportFormat, setExportFormat] = useState<ExportFormat>('csv');
  const [exportScope, setExportScope] = useState<'filtered' | 'all'>('filtered');
  const [customFilename, setCustomFilename] = useState('configuration-assessment');
  
  // Ref for dropdown menu
  const exportMenuRef = useRef<HTMLDivElement>(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportMenuRef.current && !exportMenuRef.current.contains(event.target as Node)) {
        setIsExportMenuOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Add keyboard shortcut for export (Alt+E)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.altKey && e.key === 'e' && filteredData.length > 0) {
        e.preventDefault();
        setIsExportMenuOpen(true);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [filteredData.length]);
  
  // Handle notification close
  const handleNotificationClose = () => {
    setNotification({ ...notification, show: false });
  };
  
  // Handle export
  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Determine which data to export
      const dataToExport = exportScope === 'filtered' ? filteredData : state.configData;
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
      const filename = `${customFilename}-${timestamp}`;
      
      // Export the data
      const success = exportConfigurationData(dataToExport, exportFormat, filename);
      
      // Show success message
      if (success) {
        setNotification({
          show: true,
          type: 'success',
          message: `Successfully exported ${dataToExport.length} configuration entries as ${exportFormat.toUpperCase()}`
        });
      } else {
        setNotification({
          show: true,
          type: 'error',
          message: 'Failed to export configuration data'
        });
      }
    } catch (error) {
      console.error('Error during export:', error);
      setNotification({
        show: true,
        type: 'error',
        message: 'Error exporting configuration data'
      });
    } finally {
      setIsExporting(false);
      setIsExportMenuOpen(false);
    }
  };
  
  return (
    <div 
      ref={exportMenuRef}
      style={{ 
        position: 'relative',
        marginRight: '8px',
      }}
    >
      <button
        onClick={() => setIsExportMenuOpen(!isExportMenuOpen)}
        disabled={filteredData.length === 0}
        style={{
          background: filteredData.length === 0 ? 'rgba(0, 229, 255, 0.05)' : 'rgba(0, 229, 255, 0.2)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          color: filteredData.length === 0 ? 'rgba(0, 229, 255, 0.5)' : '#00e5ff',
          padding: '8px 12px',
          cursor: filteredData.length === 0 ? 'not-allowed' : 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px',
          transition: 'background-color 0.2s',
        }}
        title={filteredData.length === 0 ? 'No data to export' : 'Export configuration data (Alt+E)'}
      >
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          strokeWidth="2"
          style={{ marginRight: '6px' }}
        >
          <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
          <polyline points="7 10 12 15 17 10" />
          <line x1="12" y1="15" x2="12" y2="3" />
        </svg>
        Export
      </button>
      
      {/* Export notification */}
      {notification.show && (
        <ConfigurationNotification
          type={notification.type}
          message={notification.message}
          duration={3000}
          onClose={handleNotificationClose}
        />
      )}
      
      {/* Export options dropdown */}
      {isExportMenuOpen && (
        <div style={{
          position: 'absolute',
          top: '100%',
          right: 0,
          marginTop: '4px',
          background: 'rgba(10, 14, 23, 0.95)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '12px 16px',
          zIndex: 10,
          width: '280px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
        }}>
          <h4 style={{ 
            margin: '0 0 12px 0', 
            color: 'white',
            fontSize: '14px',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
            paddingBottom: '8px',
          }}>
            Export Configuration Data
          </h4>
          
          {/* Format selection */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              marginBottom: '4px',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '12px',
            }}>
              Format
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                background: exportFormat === 'csv' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: exportFormat === 'csv' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
              }}>
                <input
                  type="radio"
                  name="exportFormat"
                  value="csv"
                  checked={exportFormat === 'csv'}
                  onChange={() => setExportFormat('csv')}
                  style={{ marginRight: '6px' }}
                />
                CSV
              </label>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                background: exportFormat === 'json' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: exportFormat === 'json' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
              }}>
                <input
                  type="radio"
                  name="exportFormat"
                  value="json"
                  checked={exportFormat === 'json'}
                  onChange={() => setExportFormat('json')}
                  style={{ marginRight: '6px' }}
                />
                JSON
              </label>
            </div>
          </div>
          
          {/* Data scope selection */}
          <div style={{ marginBottom: '12px' }}>
            <div style={{ 
              marginBottom: '4px',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '12px',
            }}>
              Data to export
            </div>
            <div style={{ display: 'flex', gap: '8px' }}>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                background: exportScope === 'filtered' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: exportScope === 'filtered' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
              }}>
                <input
                  type="radio"
                  name="exportScope"
                  value="filtered"
                  checked={exportScope === 'filtered'}
                  onChange={() => setExportScope('filtered')}
                  style={{ marginRight: '6px' }}
                />
                Current view ({filteredData.length} items)
              </label>
              <label style={{
                display: 'flex',
                alignItems: 'center',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
                padding: '4px 8px',
                borderRadius: '4px',
                background: exportScope === 'all' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: exportScope === 'all' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
              }}>
                <input
                  type="radio"
                  name="exportScope"
                  value="all"
                  checked={exportScope === 'all'}
                  onChange={() => setExportScope('all')}
                  style={{ marginRight: '6px' }}
                />
                All data ({state.configData.length} items)
              </label>
            </div>
          </div>
          
          {/* Filename */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ 
              marginBottom: '4px',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '12px',
            }}>
              Filename prefix
            </div>
            <input
              type="text"
              value={customFilename}
              onChange={(e) => setCustomFilename(e.target.value)}
              placeholder="configuration-assessment"
              style={{
                width: '100%',
                padding: '6px 8px',
                background: 'rgba(0, 0, 0, 0.3)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                color: 'white',
                fontSize: '14px',
              }}
            />
            <div style={{ 
              marginTop: '4px',
              color: 'rgba(255, 255, 255, 0.5)',
              fontSize: '11px',
            }}>
              A timestamp will be added automatically
            </div>
          </div>
          
          {/* Action buttons */}
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <button
              onClick={() => setIsExportMenuOpen(false)}
              style={{
                padding: '6px 12px',
                background: 'transparent',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '4px',
                color: 'white',
                fontSize: '14px',
                cursor: 'pointer',
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleExport}
              disabled={isExporting}
              style={{
                padding: '6px 12px',
                background: 'rgba(0, 229, 255, 0.2)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: isExporting ? 'wait' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
              }}
            >
              {isExporting ? (
                <>
                  <div style={{
                    width: '14px',
                    height: '14px',
                    border: '2px solid rgba(0, 229, 255, 0.3)',
                    borderTop: '2px solid #00e5ff',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                  }} />
                  Exporting...
                </>
              ) : (
                <>
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" y1="15" x2="12" y2="3" />
                  </svg>
                  Export {exportFormat.toUpperCase()}
                </>
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConfigurationExport;