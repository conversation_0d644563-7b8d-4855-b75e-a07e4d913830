import React, { useEffect, useState } from 'react';

/**
 * Notification types
 */
export type NotificationType = 'success' | 'error' | 'info' | 'warning';

/**
 * Notification props
 */
interface ConfigurationNotificationProps {
  type: NotificationType;
  message: string;
  duration?: number;
  onClose?: () => void;
}

/**
 * Notification component for Configuration Assessment
 * Displays temporary notifications for user feedback
 * 
 * Requirements: 4.3
 */
const ConfigurationNotification: React.FC<ConfigurationNotificationProps> = ({
  type,
  message,
  duration = 3000,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(true);
  
  // Auto-close notification after duration
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      if (onClose) {
        onClose();
      }
    }, duration);
    
    return () => clearTimeout(timer);
  }, [duration, onClose]);
  
  // Get notification color based on type
  const getNotificationColor = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'rgba(0, 200, 83, 0.2)',
          border: 'rgba(0, 200, 83, 0.5)',
          text: '#00c853'
        };
      case 'error':
        return {
          bg: 'rgba(255, 69, 58, 0.2)',
          border: 'rgba(255, 69, 58, 0.5)',
          text: '#ff4538'
        };
      case 'warning':
        return {
          bg: 'rgba(255, 171, 0, 0.2)',
          border: 'rgba(255, 171, 0, 0.5)',
          text: '#ffab00'
        };
      case 'info':
      default:
        return {
          bg: 'rgba(0, 229, 255, 0.2)',
          border: 'rgba(0, 229, 255, 0.5)',
          text: '#00e5ff'
        };
    }
  };
  
  // Get icon based on notification type
  const getNotificationIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M22 11.08V12a10 10 0 11-5.93-9.14" />
            <polyline points="22 4 12 14.01 9 11.01" />
          </svg>
        );
      case 'error':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="12" />
            <line x1="12" y1="16" x2="12.01" y2="16" />
          </svg>
        );
      case 'warning':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M10.29 3.86L1.82 18a2 2 0 001.71 3h16.94a2 2 0 001.71-3L13.71 3.86a2 2 0 00-3.42 0z" />
            <line x1="12" y1="9" x2="12" y2="13" />
            <line x1="12" y1="17" x2="12.01" y2="17" />
          </svg>
        );
      case 'info':
      default:
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="16" x2="12" y2="12" />
            <line x1="12" y1="8" x2="12.01" y2="8" />
          </svg>
        );
    }
  };
  
  const colors = getNotificationColor();
  
  if (!isVisible) {
    return null;
  }
  
  return (
    <div 
      style={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 16px',
        borderRadius: '4px',
        zIndex: 1000,
        backgroundColor: colors.bg,
        border: `1px solid ${colors.border}`,
        color: colors.text,
        fontSize: '14px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        maxWidth: '400px',
        animation: 'fadeIn 0.3s ease-out',
      }}
    >
      {getNotificationIcon()}
      <span>{message}</span>
      <button
        onClick={() => {
          setIsVisible(false);
          if (onClose) {
            onClose();
          }
        }}
        style={{
          background: 'transparent',
          border: 'none',
          color: colors.text,
          cursor: 'pointer',
          padding: '4px',
          marginLeft: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        aria-label="Close notification"
      >
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <line x1="18" y1="6" x2="6" y2="18" />
          <line x1="6" y1="6" x2="18" y2="18" />
        </svg>
      </button>
      
      {/* Animation styles */}
      <style>
        {`
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `}
      </style>
    </div>
  );
};

export default ConfigurationNotification;