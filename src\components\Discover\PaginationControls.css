@keyframes loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.loading-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(0, 229, 255, 0) 0%, rgba(0, 229, 255, 0.8) 50%, rgba(0, 229, 255, 0) 100%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  z-index: 1;
}