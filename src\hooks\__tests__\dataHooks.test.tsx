import { renderHook, act } from '@testing-library/react-hooks';
import { useSearch, useQuery, useDataset } from '../dataHooks';
import { DataPluginProvider } from '../../context/DataPluginContext';
import React from 'react';
import { DataPlugin } from '../../data/plugin/data-plugin';
import { BehaviorSubject } from 'rxjs';

// Mock the DataPlugin
jest.mock('../../data/plugin/data-plugin', () => {
  // Create mock observables
  const querySubject = new BehaviorSubject({ query: 'test query', language: 'kuery' });
  const filtersSubject = new BehaviorSubject([{ field: 'test', operator: 'is', value: 'value' }]);
  const timeRangeSubject = new BehaviorSubject({ from: 'now-15m', to: 'now' });
  
  // Mock search source
  const mockSearchSource = {
    setField: jest.fn().mockReturnThis(),
    getField: jest.fn(),
    fetch: jest.fn().mockResolvedValue({
      hits: {
        total: 10,
        hits: [{ _id: '1', _source: { field: 'value' } }]
      },
      took: 5,
      timed_out: false
    })
  };
  
  // Mock services
  const mockQueryStringManager = {
    getQuery: jest.fn().mockReturnValue({ query: 'test query', language: 'kuery' }),
    setQuery: jest.fn((query) => querySubject.next(query)),
    getUpdates$: jest.fn().mockReturnValue(querySubject)
  };
  
  const mockFilterManager = {
    getFilters: jest.fn().mockReturnValue([{ field: 'test', operator: 'is', value: 'value' }]),
    setFilters: jest.fn((filters) => filtersSubject.next(filters)),
    getFiltersUpdate$: jest.fn().mockReturnValue(filtersSubject)
  };
  
  const mockTimeFilterService = {
    getTime: jest.fn().mockReturnValue({ from: 'now-15m', to: 'now' }),
    setTime: jest.fn((timeRange) => timeRangeSubject.next(timeRange)),
    getTimeUpdate$: jest.fn().mockReturnValue(timeRangeSubject)
  };
  
  const mockDatasetService = {
    getDatasets: jest.fn().mockReturnValue([
      { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
      { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
    ]),
    getDataset: jest.fn().mockImplementation((id) => {
      const datasets = [
        { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
        { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
      ];
      return datasets.find(d => d.id === id);
    }),
    cacheDataset: jest.fn().mockResolvedValue(undefined)
  };
  
  const mockQueryService = {
    queryString: mockQueryStringManager,
    filterManager: mockFilterManager,
    timefilter: mockTimeFilterService,
    getDatasetService: jest.fn().mockReturnValue(mockDatasetService),
    getLanguageService: jest.fn().mockReturnValue({})
  };
  
  const mockSearchService = {
    searchSource: {
      create: jest.fn().mockResolvedValue(mockSearchSource)
    }
  };
  
  const mockDataPlugin = {
    initialize: jest.fn().mockResolvedValue(undefined),
    getSearchService: jest.fn().mockReturnValue(mockSearchService),
    getQueryService: jest.fn().mockReturnValue(mockQueryService),
    getUiService: jest.fn().mockReturnValue({}),
    getFieldFormatsService: jest.fn().mockReturnValue({}),
    getAutocompleteService: jest.fn().mockReturnValue({}),
    getStorage: jest.fn().mockReturnValue({}),
    getIndexPatternService: jest.fn().mockReturnValue({})
  };
  
  return {
    DataPlugin: {
      getInstance: jest.fn().mockReturnValue(mockDataPlugin),
    },
  };
});

// Wrapper component for hooks
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <DataPluginProvider>{children}</DataPluginProvider>
);

describe('Data Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('useSearch', () => {
    it('should execute search and return results', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useSearch(), { wrapper });
      
      // Wait for the initial search to complete
      await waitForNextUpdate();
      
      // Check initial state
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(null);
      expect(result.current.results).toEqual({
        hits: {
          total: 10,
          hits: [{ _id: '1', _source: { field: 'value' } }]
        },
        took: 5,
        timed_out: false
      });
      
      // Execute search manually
      act(() => {
        result.current.executeSearch();
      });
      
      // Wait for the search to complete
      await waitForNextUpdate();
      
      // Check that search was executed
      const searchService = DataPlugin.getInstance().getSearchService();
      expect(searchService.searchSource.create).toHaveBeenCalled();
    });
  });
  
  describe('useQuery', () => {
    it('should return query state and update functions', async () => {
      const { result } = renderHook(() => useQuery(), { wrapper });
      
      // Check initial state
      expect(result.current.query).toEqual({ query: 'test query', language: 'kuery' });
      
      // Update query
      act(() => {
        result.current.setQuery({ query: 'new query', language: 'lucene' });
      });
      
      // Check that query was updated
      const queryService = DataPlugin.getInstance().getQueryService();
      expect(queryService.queryString.setQuery).toHaveBeenCalledWith({ 
        query: 'new query', 
        language: 'lucene' 
      });
      
      // Update filters
      act(() => {
        result.current.setFilters([{ field: 'new', operator: 'is', value: 'test' }]);
      });
      
      // Check that filters were updated
      expect(queryService.filterManager.setFilters).toHaveBeenCalledWith([
        { field: 'new', operator: 'is', value: 'test' }
      ]);
      
      // Update time range
      act(() => {
        result.current.setTimeRange({ from: 'now-30m', to: 'now' });
      });
      
      // Check that time range was updated
      expect(queryService.timefilter.setTime).toHaveBeenCalledWith({ 
        from: 'now-30m', 
        to: 'now' 
      });
    });
  });
  
  describe('useDataset', () => {
    it('should return datasets and selection functions', async () => {
      const { result, waitForNextUpdate } = renderHook(() => useDataset(), { wrapper });
      
      // Check initial state
      expect(result.current.datasets).toEqual([
        { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
        { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
      ]);
      
      // Select a dataset
      act(() => {
        result.current.selectDataset('dataset2');
      });
      
      // Wait for async operations
      await waitForNextUpdate();
      
      // Check that dataset was selected
      const queryService = DataPlugin.getInstance().getQueryService();
      const datasetService = queryService.getDatasetService();
      
      expect(datasetService.getDataset).toHaveBeenCalledWith('dataset2');
      expect(queryService.queryString.setQuery).toHaveBeenCalled();
      expect(datasetService.cacheDataset).toHaveBeenCalled();
    });
  });
});