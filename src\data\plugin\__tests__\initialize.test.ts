import { initializeDataPlugin } from '../initialize';
import { DataPlugin } from '../data-plugin';
import { registerDatasetTypes } from '../dataset-types';
import { registerLanguageConfigs } from '../language-configs';

// Mock the dependencies
jest.mock('../data-plugin');
jest.mock('../dataset-types');
jest.mock('../language-configs');

describe('initializeDataPlugin', () => {
  let mockDataPlugin: jest.Mocked<DataPlugin>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock DataPlugin
    mockDataPlugin = {
      initialize: jest.fn().mockResolvedValue(undefined),
      getSearchService: jest.fn(),
      getQueryService: jest.fn().mockReturnValue({
        getDatasetService: jest.fn(),
        getLanguageService: jest.fn()
      }),
      getUiService: jest.fn(),
      getFieldFormatsService: jest.fn(),
      getAutocompleteService: jest.fn(),
      getStorage: jest.fn(),
      getIndexPatternService: jest.fn()
    } as unknown as jest.Mocked<DataPlugin>;
    
    // Mock the getInstance method
    (DataPlugin.getInstance as jest.Mock).mockReturnValue(mockDataPlugin);
    
    // Mock the registration functions
    (registerDatasetTypes as jest.Mock).mockImplementation(() => {});
    (registerLanguageConfigs as jest.Mock).mockImplementation(() => {});
  });
  
  it('should initialize the data plugin and register extensions', async () => {
    // Call the function
    await initializeDataPlugin();
    
    // Check that the plugin was initialized
    expect(DataPlugin.getInstance).toHaveBeenCalled();
    expect(mockDataPlugin.initialize).toHaveBeenCalled();
    
    // Check that extensions were registered
    expect(registerDatasetTypes).toHaveBeenCalledWith(mockDataPlugin);
    expect(registerLanguageConfigs).toHaveBeenCalledWith(mockDataPlugin);
  });
  
  it('should handle initialization errors', async () => {
    // Mock an error during initialization
    mockDataPlugin.initialize.mockRejectedValue(new Error('Initialization error'));
    
    // Call the function and expect it to throw
    await expect(initializeDataPlugin()).rejects.toThrow('Initialization error');
    
    // Check that extensions were not registered
    expect(registerDatasetTypes).not.toHaveBeenCalled();
    expect(registerLanguageConfigs).not.toHaveBeenCalled();
  });
});