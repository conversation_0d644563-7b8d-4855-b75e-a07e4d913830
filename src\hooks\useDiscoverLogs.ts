import { useState, useMemo } from 'react';
import { useDiscover } from '../context/DiscoverContext';
import { LogEntry } from '../types/discover';
import { DiscoverUtils } from '../utils/discoverUtils';
import { discoverActions } from '../context/DiscoverContext';
import { savePageSizePreference } from '../utils/storageUtils';

/**
 * Hook for managing log entries and details
 */
export const useDiscoverLogs = () => {
  const { state, dispatch } = useDiscover();
  const { filteredData, selectedFields, pagination } = state;
  
  // Calculate total pages based on filtered data and page size
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(filteredData.length / pagination.pageSize));
  }, [filteredData.length, pagination.pageSize]);
  
  // Get paginated logs based on current page and page size
  const paginatedLogs = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, pagination.currentPage, pagination.pageSize]);
  
  // Pagination helper functions
  const setCurrentPage = (page: number) => {
    // Validate input - ensure it's a number
    if (isNaN(page) || !Number.isInteger(page)) {
      console.error('Invalid page number:', page);
      return;
    }
    
    // Ensure page is within valid range
    const validPage = Math.max(1, Math.min(page, totalPages));
    
    // Don't do anything if the page is already the current page
    if (validPage === pagination.currentPage) {
      return;
    }
    
    // Set loading state before changing page
    dispatch({ type: 'SET_LOADING', payload: true });
    
    // Change page
    dispatch(discoverActions.setCurrentPage(validPage));
    
    // Simulate loading time for page transitions
    // In a real app, this would be handled by the async data fetching
    setTimeout(() => {
      dispatch({ type: 'SET_LOADING', payload: false });
    }, 500);
  };
  
  const setPageSize = (size: number) => {
    // Validate input - ensure it's a valid number
    if (isNaN(size) || !Number.isInteger(size) || size <= 0) {
      console.error('Invalid page size:', size);
      return;
    }
    
    // Don't do anything if the size is already the current page size
    if (size === pagination.pageSize) {
      return;
    }
    
    // Set loading state before changing page size
    dispatch({ type: 'SET_LOADING', payload: true });
    
    // Save the page size preference to local storage
    savePageSizePreference(size);
    
    // Change page size
    dispatch(discoverActions.setPageSize(size));
    
    // Simulate loading time for page size changes
    // In a real app, this would be handled by the async data fetching
    setTimeout(() => {
      dispatch({ type: 'SET_LOADING', payload: false });
    }, 500);
  };
  
  const goToFirstPage = () => {
    if (pagination.currentPage !== 1) {
      setCurrentPage(1);
    }
  };
  
  const goToPreviousPage = () => {
    if (pagination.currentPage > 1) {
      setCurrentPage(pagination.currentPage - 1);
    }
  };
  
  const goToNextPage = () => {
    if (pagination.currentPage < totalPages) {
      setCurrentPage(pagination.currentPage + 1);
    }
  };
  
  const goToLastPage = () => {
    if (pagination.currentPage !== totalPages) {
      setCurrentPage(totalPages);
    }
  };
  
  // State for expanded log entries
  const [expandedLogs, setExpandedLogs] = useState<Record<string, boolean>>({});
  
  // Toggle log expansion
  const toggleLogExpansion = (logId: string) => {
    setExpandedLogs(prev => ({
      ...prev,
      [logId]: !prev[logId],
    }));
  };
  
  // Check if log is expanded
  const isLogExpanded = (logId: string) => {
    return !!expandedLogs[logId];
  };
  
  // Expand log
  const expandLog = (logId: string) => {
    setExpandedLogs(prev => ({
      ...prev,
      [logId]: true,
    }));
  };
  
  // Collapse log
  const collapseLog = (logId: string) => {
    setExpandedLogs(prev => ({
      ...prev,
      [logId]: false,
    }));
  };
  
  // Collapse all logs
  const collapseAllLogs = () => {
    setExpandedLogs({});
  };
  
  // Get log color based on level
  const getLogLevelColor = (level: 'info' | 'warning' | 'error' | 'critical') => {
    return DiscoverUtils.getLevelColor(level);
  };
  
  // Format log timestamp
  const formatLogTimestamp = (timestamp: Date) => {
    return DiscoverUtils.formatDate(timestamp);
  };
  
  // Get field value from log
  const getFieldValue = (log: LogEntry, fieldPath: string) => {
    const parts = fieldPath.split('.');
    let value: unknown = log;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  };
  
  // Format field value for display
  const formatFieldValue = (value: unknown) => {
    return DiscoverUtils.formatFieldValue(value);
  };
  
  // Memoize pagination helper properties to prevent unnecessary recalculations
  const paginationInfo = useMemo(() => {
    const hasResults = filteredData.length > 0;
    const isFirstPage = pagination.currentPage === 1;
    const isLastPage = pagination.currentPage >= totalPages;
    const startIndex = hasResults 
      ? (pagination.currentPage - 1) * pagination.pageSize + 1 
      : 0;
    const endIndex = Math.min(pagination.currentPage * pagination.pageSize, filteredData.length);
    const showingText = hasResults 
      ? `Showing ${startIndex} to ${endIndex} of ${filteredData.length} entries`
      : 'No entries to display';
      
    return {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      totalItems: filteredData.length,
      totalPages,
      isFirstPage,
      isLastPage,
      startIndex,
      endIndex,
      showingText,
      isLoading: state.isLoading,
      noResults: !hasResults
    };
  }, [
    pagination.currentPage, 
    pagination.pageSize, 
    filteredData.length, 
    totalPages,
    state.isLoading
  ]);
  
  return {
    logs: paginatedLogs, // Return paginated logs instead of all filtered logs
    allLogs: filteredData, // Keep the original filtered data available if needed
    selectedFields,
    expandedLogs,
    toggleLogExpansion,
    isLogExpanded,
    expandLog,
    collapseLog,
    collapseAllLogs,
    getLogLevelColor,
    formatLogTimestamp,
    getFieldValue,
    getLogFieldValue: getFieldValue, // Alias for backward compatibility
    formatFieldValue,
    // Pagination state and functions
    pagination: {
      ...paginationInfo,
      // Navigation functions
      setCurrentPage,
      setPageSize,
      goToFirstPage,
      goToPreviousPage,
      goToNextPage,
      goToLastPage
    }
  };
};
