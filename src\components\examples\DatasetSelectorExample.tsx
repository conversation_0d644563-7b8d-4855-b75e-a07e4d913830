import React, { useState } from 'react';
import DatasetSelector from '../dataset/DatasetSelector';
import { Dataset } from '../../data/plugin/interfaces';
import { useDataPlugin } from '../../context/DataPluginContext';

/**
 * Example component that demonstrates how to use the DatasetSelector
 */
const DatasetSelectorExample: React.FC = () => {
  const dataPlugin = useDataPlugin();
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [fields, setFields] = useState<Array<{ name: string; type: string }>>([]);
  const [isLoadingFields, setIsLoadingFields] = useState<boolean>(false);
  
  // Handle dataset selection
  const handleDatasetSelected = async (dataset: Dataset) => {
    setSelectedDataset(dataset);
    
    // Load fields for the selected dataset
    setIsLoadingFields(true);
    
    try {
      // Get the dataset service
      const datasetService = dataPlugin.getQueryService().getDatasetService();
      
      // Get the dataset type config
      const typeConfig = datasetService.getType(dataset.type);
      
      if (typeConfig) {
        // Fetch fields for the dataset
        const datasetFields = await typeConfig.fetchFields(dataset);
        setFields(datasetFields);
      } else {
        setFields([]);
      }
    } catch (error) {
      console.error('Error loading dataset fields:', error);
      setFields([]);
    } finally {
      setIsLoadingFields(false);
    }
  };
  
  return (
    <div className="dataset-selector-example">
      <h2>Dataset Selector Example</h2>
      
      <div className="example-container">
        <div className="selector-container">
          <DatasetSelector onDatasetSelected={handleDatasetSelected} />
        </div>
        
        <div className="dataset-details">
          <h3>Dataset Details</h3>
          
          {selectedDataset ? (
            <div className="dataset-info">
              <h4>{selectedDataset.title}</h4>
              <p>
                <strong>ID:</strong> {selectedDataset.id}
                <br />
                <strong>Type:</strong> {selectedDataset.type}
                {selectedDataset.timeFieldName && (
                  <>
                    <br />
                    <strong>Time Field:</strong> {selectedDataset.timeFieldName}
                  </>
                )}
              </p>
              
              <div className="dataset-fields">
                <h4>Fields</h4>
                
                {isLoadingFields ? (
                  <p>Loading fields...</p>
                ) : fields.length > 0 ? (
                  <table className="fields-table">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Type</th>
                      </tr>
                    </thead>
                    <tbody>
                      {fields.map((field) => (
                        <tr key={field.name}>
                          <td>{field.name}</td>
                          <td>{field.type}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <p>No fields available</p>
                )}
              </div>
            </div>
          ) : (
            <p>No dataset selected</p>
          )}
        </div>
      </div>
      
      <div className="usage-info">
        <h3>How to Use</h3>
        <pre>{`
import DatasetSelector from '../dataset/DatasetSelector';
import { Dataset } from '../../data/plugin/interfaces';

const MyComponent: React.FC = () => {
  const handleDatasetSelected = (dataset: Dataset) => {
    console.log('Selected dataset:', dataset);
    // Do something with the selected dataset
  };
  
  return (
    <DatasetSelector onDatasetSelected={handleDatasetSelected} />
  );
};
        `}</pre>
      </div>
    </div>
  );
};

export default DatasetSelectorExample;