import { CustomSampleInterceptor } from '../custom-sample-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../../interfaces';
import { of } from 'rxjs';
import { BaseSearchInterceptor } from '../base-search-interceptor';

// Mock BaseSearchInterceptor to avoid actual search execution
jest.mock('../base-search-interceptor');

describe('CustomSampleInterceptor', () => {
  let interceptor: CustomSampleInterceptor;
  let mockBaseInterceptor: jest.Mocked<BaseSearchInterceptor>;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a mock for the base interceptor
    mockBaseInterceptor = new BaseSearchInterceptor() as jest.Mocked<BaseSearchInterceptor>;
    
    // Mock the search method to return a simple response
    (mockBaseInterceptor.search as jest.Mock).mockImplementation(() => {
      return of({
        hits: {
          total: 2,
          hits: [
            {
              _id: '1',
              _source: {
                timestamp: '2023-01-01T12:00:00Z',
                id: 'TX001',
                amount: 100.50,
                status: 'completed',
                user_id: 'U001',
                product_id: 'P001'
              }
            },
            {
              _id: '2',
              _source: {
                timestamp: '2023-01-02T14:30:00Z',
                id: 'TX002',
                amount: 75.25,
                status: 'pending',
                user_id: 'U002',
                product_id: 'P002'
              }
            }
          ]
        },
        took: 5,
        timed_out: false
      } as SearchResponse);
    });
    
    // Create the interceptor and set the next interceptor
    interceptor = new CustomSampleInterceptor();
    
    // Use prototype to access protected method
    Object.setPrototypeOf(interceptor, {
      ...Object.getPrototypeOf(interceptor),
      next: mockBaseInterceptor
    });
  });
  
  it('should pass through non-custom requests unchanged', (done) => {
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' },
      index: 'regular-index',
      size: 10
    };
    
    const options: SearchOptions = {};
    
    interceptor.search(request, options).subscribe(response => {
      // Verify that the base interceptor was called with the original request
      expect(mockBaseInterceptor.search).toHaveBeenCalledWith(request, options);
      
      // Verify that the response is unchanged
      expect(response.meta).toBeUndefined();
      
      done();
    });
  });
  
  it('should transform simple-query language for custom sample datasets', (done) => {
    const request: SearchRequest = {
      query: { query: 'status:completed AND amount:100.50', language: 'simple-query' },
      index: 'custom-transactions',
      size: 10
    };
    
    const options: SearchOptions = {};
    
    interceptor.search(request, options).subscribe(() => {
      // Verify that the base interceptor was called with a transformed request
      const transformedRequest = (mockBaseInterceptor.search as jest.Mock).mock.calls[0][0];
      
      // Check that the query was transformed
      expect(transformedRequest.query.language).toBe('structured');
      expect(typeof transformedRequest.query.query).toBe('string');
      
      // Check that it contains the structured query
      const parsedQuery = JSON.parse(transformedRequest.query.query);
      expect(parsedQuery.bool).toBeDefined();
      expect(parsedQuery.bool.must).toBeDefined();
      
      done();
    });
  });
  
  it('should add default fields based on dataset type', (done) => {
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' },
      index: 'custom-transactions',
      size: 10
    };
    
    const options: SearchOptions = {};
    
    interceptor.search(request, options).subscribe(() => {
      // Verify that the base interceptor was called with a request that includes fields
      const transformedRequest = (mockBaseInterceptor.search as jest.Mock).mock.calls[0][0];
      
      // Check that fields were added
      expect(transformedRequest.fields).toBeDefined();
      expect(transformedRequest.fields).toContain('timestamp');
      expect(transformedRequest.fields).toContain('amount');
      expect(transformedRequest.fields).toContain('status');
      
      done();
    });
  });
  
  it('should add custom metadata to the response', (done) => {
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' },
      index: 'custom-transactions',
      size: 10
    };
    
    const options: SearchOptions = {};
    
    interceptor.search(request, options).subscribe(response => {
      // Verify that metadata was added to the response
      expect(response.meta).toBeDefined();
      expect(response.meta?.dataSource).toBe('custom-sample');
      expect(response.meta?.transformedBy).toBe('CustomSampleInterceptor');
      expect(response.meta?.timestamp).toBeDefined();
      
      done();
    });
  });
  
  it('should format fields in the response', (done) => {
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' },
      index: 'custom-transactions',
      size: 10
    };
    
    const options: SearchOptions = {};
    
    interceptor.search(request, options).subscribe(response => {
      // Verify that fields were formatted
      expect(response.hits.hits[0]._source.formattedAmount).toBe('$100.50');
      expect(response.hits.hits[1]._source.formattedAmount).toBe('$75.25');
      
      done();
    });
  });
});