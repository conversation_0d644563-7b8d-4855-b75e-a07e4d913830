import { Observable, from, catchError } from 'rxjs';
import { SearchInterceptor } from './search-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../interfaces';

/**
 * Base implementation of the SearchInterceptor that provides common functionality
 * for concrete interceptors.
 * 
 * This class implements the search method to:
 * 1. Pass the request to the next interceptor if available
 * 2. Execute the search if this is the last interceptor
 * 3. Handle errors in the search execution
 * 
 * Concrete interceptors should extend this class and override the search method
 * to add their specific transformations.
 */
export class BaseSearchInterceptor extends SearchInterceptor {
  /**
   * Executes the search by passing the request to the next interceptor in the chain
   * or by running the search if this is the last interceptor.
   * 
   * @param request The search request to process
   * @param options Options for the search execution
   * @returns An Observable of the search response
   */
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    try {
      // If there's a next interceptor, pass the request to it
      if (this.next) {
        return this.next.search(request, options);
      }
      
      // Otherwise, this is the last interceptor, so execute the search
      return this.runSearch(request, options.signal, options.strategy)
        .pipe(
          catchError(error => {
            console.error('Error executing search:', error);
            // Return a default response in case of error
            return from(Promise.resolve({
              hits: {
                total: 0,
                hits: []
              },
              took: 0,
              timed_out: true,
              error: {
                message: error.message || 'Unknown error',
                type: error.name || 'Error'
              }
            } as SearchResponse));
          })
        );
    } catch (error) {
      console.error('Error in search interceptor:', error);
      // Return a default response in case of error
      return from(Promise.resolve({
        hits: {
          total: 0,
          hits: []
        },
        took: 0,
        timed_out: true,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          type: error instanceof Error ? error.name : 'Error'
        }
      } as SearchResponse));
    }
  }
  
  /**
   * Helper method to transform a search request.
   * Concrete interceptors can use this method to apply common transformations.
   * 
   * @param request The search request to transform
   * @returns The transformed search request
   */
  protected transformRequest(request: SearchRequest): SearchRequest {
    // Clone the request to avoid modifying the original
    return { ...request };
  }
  
  /**
   * Helper method to transform a search response.
   * Concrete interceptors can use this method to apply common transformations.
   * 
   * @param response The search response to transform
   * @returns The transformed search response
   */
  protected transformResponse(response: SearchResponse): SearchResponse {
    // Clone the response to avoid modifying the original
    return { ...response };
  }
}