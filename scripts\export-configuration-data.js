/**
 * Export Configuration Assessment Sample Data
 * 
 * This script exports sample configuration assessment data to JSON and CSV files
 * for testing and development purposes.
 * 
 * Usage:
 *   node scripts/export-configuration-data.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { 
  tinyDataset,
  smallDataset, 
  mediumDataset, 
  largeDataset,
  allPassedDataset,
  allFailedDataset,
  mixedDataset,
  mostlyPassedDataset,
  mostlyFailedDataset,
  exportDatasetToJSON,
  exportDatasetToCSV
} from '../src/data/configurationDatasets.js';

// Get the directory name
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Create output directory if it doesn't exist
const outputDir = path.resolve(__dirname, '../sample-data');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir);
}

// Define datasets to export
const datasetsToExport = {
  'tiny': tinyDataset,
  'small': smallDataset,
  'medium': mediumDataset,
  'large': largeDataset,
  'all-passed': allPassedDataset,
  'all-failed': allFailedDataset,
  'mixed': mixedDataset,
  'mostly-passed': mostlyPassedDataset,
  'mostly-failed': mostlyFailedDataset
};

// Export each dataset to JSON and CSV
Object.entries(datasetsToExport).forEach(([name, dataset]) => {
  // Export to JSON
  const jsonPath = path.join(outputDir, `configuration-${name}.json`);
  fs.writeFileSync(jsonPath, exportDatasetToJSON(dataset));
  console.log(`Exported JSON dataset to ${jsonPath}`);
  
  // Export to CSV
  const csvPath = path.join(outputDir, `configuration-${name}.csv`);
  fs.writeFileSync(csvPath, exportDatasetToCSV(dataset));
  console.log(`Exported CSV dataset to ${csvPath}`);
});

console.log('All configuration assessment datasets exported successfully!');