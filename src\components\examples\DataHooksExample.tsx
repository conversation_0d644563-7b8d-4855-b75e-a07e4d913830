import React from 'react';
import { useSearch, useQuery, useDataset } from '../../hooks/dataHooks';

/**
 * Example component that demonstrates how to use the data hooks
 */
const DataHooksExample: React.FC = () => {
  // Use the search hook
  const { results, isLoading, error, executeSearch } = useSearch();
  
  // Use the query hook
  const { 
    query, 
    setQuery, 
    filters, 
    setFilters, 
    timeRange, 
    setTimeRange 
  } = useQuery();
  
  // Use the dataset hook
  const { datasets, selectedDataset, selectDataset } = useDataset();
  
  // Update the query
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery({
      ...query,
      query: e.target.value
    });
  };
  
  // Update the language
  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setQuery({
      ...query,
      language: e.target.value
    });
  };
  
  // Add a filter
  const handleAddFilter = () => {
    setFilters([
      ...filters,
      { field: 'example', operator: 'is', value: 'test' }
    ]);
  };
  
  // Remove a filter
  const handleRemoveFilter = (index: number) => {
    const newFilters = [...filters];
    newFilters.splice(index, 1);
    setFilters(newFilters);
  };
  
  // Update time range
  const handleTimeRangeChange = (from: string, to: string) => {
    setTimeRange({ from, to });
  };
  
  return (
    <div className="data-hooks-example">
      <h2>Data Hooks Example</h2>
      
      <div className="section">
        <h3>Dataset Selection</h3>
        <div className="dataset-selector">
          <select 
            value={selectedDataset?.id || ''} 
            onChange={(e) => selectDataset(e.target.value)}
          >
            <option value="">Select a dataset</option>
            {datasets.map((dataset) => (
              <option key={dataset.id} value={dataset.id}>
                {dataset.title}
              </option>
            ))}
          </select>
          
          {selectedDataset && (
            <div className="selected-dataset">
              <h4>Selected Dataset</h4>
              <p>ID: {selectedDataset.id}</p>
              <p>Title: {selectedDataset.title}</p>
              <p>Type: {selectedDataset.type}</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Query Management</h3>
        <div className="query-editor">
          <div className="form-group">
            <label>Query:</label>
            <input 
              type="text" 
              value={query.query} 
              onChange={handleQueryChange} 
              placeholder="Enter query"
            />
          </div>
          
          <div className="form-group">
            <label>Language:</label>
            <select value={query.language} onChange={handleLanguageChange}>
              <option value="kuery">KQL</option>
              <option value="lucene">Lucene</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="section">
        <h3>Filters</h3>
        <div className="filters">
          <button onClick={handleAddFilter}>Add Example Filter</button>
          
          {filters.length > 0 ? (
            <ul className="filter-list">
              {filters.map((filter, index) => (
                <li key={index} className="filter-item">
                  <span>{filter.field} {filter.operator} {filter.value}</span>
                  <button onClick={() => handleRemoveFilter(index)}>Remove</button>
                </li>
              ))}
            </ul>
          ) : (
            <p>No filters applied</p>
          )}
        </div>
      </div>
      
      <div className="section">
        <h3>Time Range</h3>
        <div className="time-range">
          <div className="form-group">
            <label>From:</label>
            <input 
              type="text" 
              value={timeRange.from} 
              onChange={(e) => handleTimeRangeChange(e.target.value, timeRange.to)} 
              placeholder="e.g., now-15m"
            />
          </div>
          
          <div className="form-group">
            <label>To:</label>
            <input 
              type="text" 
              value={timeRange.to} 
              onChange={(e) => handleTimeRangeChange(timeRange.from, e.target.value)} 
              placeholder="e.g., now"
            />
          </div>
          
          <div className="presets">
            <button onClick={() => handleTimeRangeChange('now-15m', 'now')}>Last 15 minutes</button>
            <button onClick={() => handleTimeRangeChange('now-1h', 'now')}>Last hour</button>
            <button onClick={() => handleTimeRangeChange('now-24h', 'now')}>Last 24 hours</button>
          </div>
        </div>
      </div>
      
      <div className="section">
        <h3>Search</h3>
        <div className="search-actions">
          <button onClick={executeSearch} disabled={isLoading}>
            {isLoading ? 'Searching...' : 'Execute Search'}
          </button>
        </div>
        
        {error && (
          <div className="error">
            <h4>Error</h4>
            <p>{error.message}</p>
          </div>
        )}
        
        {results && (
          <div className="results">
            <h4>Search Results</h4>
            <p>Total hits: {results.hits.total}</p>
            <p>Took: {results.took}ms</p>
            
            {results.hits.hits.length > 0 ? (
              <ul className="result-list">
                {results.hits.hits.map((hit) => (
                  <li key={hit._id} className="result-item">
                    <pre>{JSON.stringify(hit._source, null, 2)}</pre>
                  </li>
                ))}
              </ul>
            ) : (
              <p>No results found</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DataHooksExample;