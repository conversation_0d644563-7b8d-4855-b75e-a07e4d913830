import './App.css'
import Router from './router'
import ErrorBoundary from './components/common/ErrorBoundary'
import { ToastProvider } from './context/ToastContext'
import { ErrorProvider } from './context/ErrorContext'
import ToastContainer from './components/common/ToastContainer'

/**
 * Main App component.
 * 
 * This component has been updated to use the Router component
 * instead of directly rendering the CyberSecurityDashboard.
 * The Router will handle navigation and rendering the appropriate components.
 * 
 * It also includes error handling and toast notifications.
 */
function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <ErrorProvider>
          <Router />
          <ToastContainer position="top-right" maxToasts={5} />
        </ErrorProvider>
      </ToastProvider>
    </ErrorBoundary>
  )
}

export default App
