import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BaseSearchInterceptor } from './base-search-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../interfaces';

/**
 * A sample search interceptor that demonstrates how to transform search requests
 * and responses in the interceptor chain.
 */
export class SampleSearchInterceptor extends BaseSearchInterceptor {
  /**
   * Processes the search request and transforms it before passing it to the next interceptor.
   * @param request The search request to process
   * @param options Options for the search execution
   * @returns An Observable of the search response
   */
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    // Clone the request to avoid modifying the original
    const modifiedRequest: SearchRequest = { ...request };
    
    // Add a sample transformation to the request
    // For example, we could add a default size if none is specified
    if (!modifiedRequest.size) {
      modifiedRequest.size = 20;
    }
    
    // Pass the modified request to the next interceptor or execute the search
    const response$ = super.search(modifiedRequest, options);
    
    // Transform the response if needed
    return response$.pipe(
      map(response => {
        // Add a sample transformation to the response
        // For example, we could add a custom field to the response
        return {
          ...response,
          customField: 'This was added by the SampleSearchInterceptor'
        } as SearchResponse;
      })
    );
  }
}