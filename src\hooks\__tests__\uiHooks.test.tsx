import { renderHook, act } from '@testing-library/react-hooks';
import { useUiComponent, useFieldFormat, useAutocomplete, useLanguageAutocomplete, useUiComponentWithState } from '../uiHooks';
import { DataPluginProvider } from '../../context/DataPluginContext';
import React from 'react';
import { DataPlugin } from '../../data/plugin/data-plugin';

// Mock component for testing
const MockComponent: React.FC = () => <div>Mock Component</div>;

// Mock the DataPlugin
jest.mock('../../data/plugin/data-plugin', () => {
  // Mock services
  const mockUiService = {
    getComponent: jest.fn().mockImplementation((id) => {
      if (id === 'testComponent') {
        return MockComponent;
      }
      return undefined;
    }),
    registerComponent: jest.fn()
  };
  
  const mockFieldFormatsService = {
    getDefaultFormat: jest.fn().mockReturnValue({
      id: 'default',
      title: 'Default Format',
      format: jest.fn((value) => `Formatted: ${value}`)
    }),
    getFormat: jest.fn().mockImplementation((fieldType, formatId) => {
      if (formatId === 'custom') {
        return {
          id: 'custom',
          title: 'Custom Format',
          format: jest.fn((value) => `Custom: ${value}`)
        };
      }
      return undefined;
    }),
    registerFormat: jest.fn()
  };
  
  const mockAutocompleteService = {
    getProvider: jest.fn().mockImplementation((type) => {
      if (type === 'test') {
        return {
          getCompletions: jest.fn().mockResolvedValue([
            { value: 'completion1', score: 1 },
            { value: 'completion2', score: 0.8 }
          ])
        };
      }
      return undefined;
    }),
    registerProvider: jest.fn()
  };
  
  const mockLanguageService = {
    getLanguage: jest.fn().mockImplementation((language) => {
      if (language === 'kuery') {
        return {
          id: 'kuery',
          name: 'KQL',
          description: 'Kibana Query Language',
          syntax: {
            highlight: jest.fn((query) => `<highlighted>${query}</highlighted>`),
            validate: jest.fn((query) => ({ valid: query.length > 0, error: query.length > 0 ? undefined : 'Empty query' }))
          },
          autocomplete: {
            getCompletions: jest.fn().mockResolvedValue([
              { value: 'field:', score: 1 },
              { value: 'value', score: 0.8 }
            ])
          }
        };
      }
      return undefined;
    }),
    getLanguages: jest.fn().mockReturnValue([
      { id: 'kuery', name: 'KQL' },
      { id: 'lucene', name: 'Lucene' }
    ]),
    registerLanguage: jest.fn()
  };
  
  const mockQueryService = {
    queryString: {
      getQuery: jest.fn().mockReturnValue({ query: '', language: 'kuery' }),
      setQuery: jest.fn(),
      getUpdates$: jest.fn().mockReturnValue({ subscribe: jest.fn().mockReturnValue({ unsubscribe: jest.fn() }) })
    },
    getLanguageService: jest.fn().mockReturnValue(mockLanguageService)
  };
  
  const mockDataPlugin = {
    initialize: jest.fn().mockResolvedValue(undefined),
    getUiService: jest.fn().mockReturnValue(mockUiService),
    getFieldFormatsService: jest.fn().mockReturnValue(mockFieldFormatsService),
    getAutocompleteService: jest.fn().mockReturnValue(mockAutocompleteService),
    getQueryService: jest.fn().mockReturnValue(mockQueryService)
  };
  
  return {
    DataPlugin: {
      getInstance: jest.fn().mockReturnValue(mockDataPlugin),
    },
  };
});

// Wrapper component for hooks
const wrapper = ({ children }: { children: React.ReactNode }) => (
  <DataPluginProvider>{children}</DataPluginProvider>
);

describe('UI Hooks', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('useUiComponent', () => {
    it('should return the component when it exists', () => {
      const { result } = renderHook(() => useUiComponent('testComponent'), { wrapper });
      
      // Check that the component was returned
      expect(result.current).toBe(MockComponent);
      
      // Check that getComponent was called with the correct ID
      const uiService = DataPlugin.getInstance().getUiService();
      expect(uiService.getComponent).toHaveBeenCalledWith('testComponent');
    });
    
    it('should return undefined when the component does not exist', () => {
      const { result } = renderHook(() => useUiComponent('nonExistentComponent'), { wrapper });
      
      // Check that undefined was returned
      expect(result.current).toBeUndefined();
    });
  });
  
  describe('useFieldFormat', () => {
    it('should return the default format when no formatId is provided', () => {
      const { result } = renderHook(() => useFieldFormat('string'), { wrapper });
      
      // Check that the default format was returned
      expect(result.current.formatId).toBe('default');
      
      // Format a value
      const formatted = result.current.formatValue('test');
      expect(formatted).toBe('Formatted: test');
      
      // Check that getDefaultFormat was called with the correct field type
      const fieldFormatsService = DataPlugin.getInstance().getFieldFormatsService();
      expect(fieldFormatsService.getDefaultFormat).toHaveBeenCalledWith('string');
    });
    
    it('should return the specified format when formatId is provided', () => {
      const { result } = renderHook(() => useFieldFormat('string', 'custom'), { wrapper });
      
      // Check that the custom format was returned
      expect(result.current.formatId).toBe('custom');
      
      // Format a value
      const formatted = result.current.formatValue('test');
      expect(formatted).toBe('Custom: test');
      
      // Check that getFormat was called with the correct parameters
      const fieldFormatsService = DataPlugin.getInstance().getFieldFormatsService();
      expect(fieldFormatsService.getFormat).toHaveBeenCalledWith('string', 'custom');
    });
  });
  
  describe('useAutocomplete', () => {
    it('should return completions when the provider exists', async () => {
      const { result } = renderHook(() => useAutocomplete('test'), { wrapper });
      
      // Check that the provider exists
      expect(result.current.hasProvider).toBe(true);
      
      // Get completions
      const completions = await result.current.getCompletions({ query: 'test' });
      expect(completions).toEqual([
        { value: 'completion1', score: 1 },
        { value: 'completion2', score: 0.8 }
      ]);
      
      // Check that getProvider was called with the correct type
      const autocompleteService = DataPlugin.getInstance().getAutocompleteService();
      expect(autocompleteService.getProvider).toHaveBeenCalledWith('test');
    });
    
    it('should return empty array when the provider does not exist', async () => {
      const { result } = renderHook(() => useAutocomplete('nonExistent'), { wrapper });
      
      // Check that the provider does not exist
      expect(result.current.hasProvider).toBe(false);
      
      // Get completions
      const completions = await result.current.getCompletions({ query: 'test' });
      expect(completions).toEqual([]);
    });
  });
  
  describe('useLanguageAutocomplete', () => {
    it('should return language-specific functions when the language exists', async () => {
      const { result } = renderHook(() => useLanguageAutocomplete('kuery'), { wrapper });
      
      // Check that autocomplete is available
      expect(result.current.hasAutocomplete).toBe(true);
      
      // Get completions
      const completions = await result.current.getCompletions('test', 0);
      expect(completions).toEqual([
        { value: 'field:', score: 1 },
        { value: 'value', score: 0.8 }
      ]);
      
      // Validate query
      const validation = result.current.validateQuery('test');
      expect(validation).toEqual({ valid: true });
      
      // Highlight query
      const highlighted = result.current.highlightQuery('test');
      expect(highlighted).toBe('<highlighted>test</highlighted>');
      
      // Check that getLanguage was called with the correct language
      const queryService = DataPlugin.getInstance().getQueryService();
      const languageService = queryService.getLanguageService();
      expect(languageService.getLanguage).toHaveBeenCalledWith('kuery');
    });
    
    it('should return default functions when the language does not exist', async () => {
      const { result } = renderHook(() => useLanguageAutocomplete('nonExistent'), { wrapper });
      
      // Check that autocomplete is not available
      expect(result.current.hasAutocomplete).toBe(false);
      
      // Get completions
      const completions = await result.current.getCompletions('test', 0);
      expect(completions).toEqual([]);
      
      // Validate query
      const validation = result.current.validateQuery('test');
      expect(validation).toEqual({ valid: true });
      
      // Highlight query
      const highlighted = result.current.highlightQuery('test');
      expect(highlighted).toBe('test');
    });
  });
  
  describe('useUiComponentWithState', () => {
    it('should return the component and state management functions', () => {
      const initialProps = { prop1: 'value1' };
      const { result } = renderHook(() => useUiComponentWithState('testComponent', initialProps), { wrapper });
      
      // Check that the component was returned
      expect(result.current.component).toBe(MockComponent);
      
      // Check that the initial props were set
      expect(result.current.props).toEqual({ prop1: 'value1' });
      
      // Update props
      act(() => {
        result.current.updateProps({ prop2: 'value2' });
      });
      
      // Check that the props were updated
      expect(result.current.props).toEqual({ prop1: 'value1', prop2: 'value2' });
    });
  });
});