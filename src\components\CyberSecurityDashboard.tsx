import React, { useState, useEffect } from 'react';
import { ExploreNavItem, ExploreDropdown } from './ExploreNavigation';
import './ExploreNavigation/ExploreNavigation.css';
import {
  DiscoverPage,
  DashboardsPage,
  VisualizePage,
  ReportingPage,
  AlertingPage,
  MapsPage,
  NotificationsPage
} from './ExplorePages';

const CyberSecurityDashboard: React.FC = () => {
  const [activeNav, setActiveNav] = useState('dashboard');
  const [collapsed, setCollapsed] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [exploreExpanded, setExploreExpanded] = useState(false);
  const [activeExploreItem, setActiveExploreItem] = useState<string | undefined>();
  const [animationPhase, setAnimationPhase] = useState(0);
  const [securityScore] = useState(19);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanComplete, setScanComplete] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // Animation effect for the sidebar background
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationPhase(prev => (prev + 1) % 100);
    }, 150);
    return () => clearInterval(interval);
  }, []);

  // Simulate security scan progress
  useEffect(() => {
    if (scanProgress < 100 && !scanComplete) {
      const timer = setTimeout(() => {
        setScanProgress(prev => {
          const newProgress = prev + 1;
          if (newProgress >= 100) {
            setScanComplete(true);
          }
          return newProgress;
        });
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [scanProgress, scanComplete]);

  // Update current time
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Add a constant to use for icon properties
  const iconProps = {
    style: { pointerEvents: "none" as const }
  };

  // SVG icons that match the image
  const ShieldIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    </svg>
  );

  const HomeIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
      <polyline points="9 22 9 12 15 12 15 22" />
    </svg>
  );

  const GridIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="3" y="3" width="7" height="7" />
      <rect x="14" y="3" width="7" height="7" />
      <rect x="14" y="14" width="7" height="7" />
      <rect x="3" y="14" width="7" height="7" />
    </svg>
  );

  const ClockIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );

  const FileIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14 2 14 8 20 8" />
    </svg>
  );

  const UserIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    </svg>
  );

  const SettingsIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="3" />
      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
    </svg>
  );

  // Additional icons for the dashboard
  const AlertIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
      <line x1="12" y1="9" x2="12" y2="13"></line>
      <line x1="12" y1="17" x2="12.01" y2="17"></line>
    </svg>
  );

  const CheckIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <polyline points="20 6 9 17 4 12"></polyline>
    </svg>
  );

  const ServerIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
      <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
      <line x1="6" y1="6" x2="6.01" y2="6"></line>
      <line x1="6" y1="18" x2="6.01" y2="18"></line>
    </svg>
  );

  const GlobeIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="2" y1="12" x2="22" y2="12"></line>
      <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
    </svg>
  );

  const LockIcon = () => (
    <svg
      {...iconProps}
      viewBox="0 0 24 24"
      width="20"
      height="20"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
    </svg>
  );

  // Navigation item styling function
  const getNavItemStyle = (itemId: string) => {
    return {
      width: collapsed ? '36px' : '80%',
      height: collapsed ? '36px' : '40px',
      display: 'flex',
      justifyContent: collapsed ? 'center' : 'flex-start',
      alignItems: 'center',
      borderRadius: '8px',
      padding: collapsed ? '0' : '0 12px',
      cursor: 'pointer',
      color: activeNav === itemId ? '#00e5ff' : 'white',
      backgroundColor: activeNav === itemId ? 'rgba(0, 229, 255, 0.15)' : 'rgba(255, 255, 255, 0.03)',
      border: activeNav === itemId ? '1px solid rgba(0, 229, 255, 0.5)' : '1px solid rgba(255, 255, 255, 0.03)',
      boxShadow: activeNav === itemId ? '0 0 15px rgba(0, 229, 255, 0.5)' : 'none',
      transition: 'all 0.3s ease',
      marginBottom: '15px',
      backdropFilter: 'blur(5px)',
      WebkitBackdropFilter: 'blur(5px)',
      position: 'relative' as const,
      zIndex: 5,
    };
  };

  // Handle navigation click with a dedicated function to improve responsiveness
  const handleNavClick = (navId: string) => {
    setActiveNav(navId);
    setExploreExpanded(false);
    setActiveExploreItem(undefined);
  };

  // Handle explore navigation toggle
  const handleExploreToggle = () => {
    setActiveNav('explore');
    setExploreExpanded(!exploreExpanded);
  };

  // Handle explore sub-item click
  const handleExploreItemClick = (itemId: string) => {
    setActiveExploreItem(itemId);
    setActiveNav('explore');
    // Here you would typically handle routing to the specific explore page
    console.log(`Navigating to explore/${itemId}`);
  };

  // Render main content based on active navigation
  const renderMainContent = () => {
    if (activeNav === 'explore' && activeExploreItem) {
      switch (activeExploreItem) {
        case 'discover':
          return <DiscoverPage />;
        case 'dashboards':
          return <DashboardsPage />;
        case 'visualize':
          return <VisualizePage />;
        case 'reporting':
          return <ReportingPage />;
        case 'alerting':
          return <AlertingPage />;
        case 'maps':
          return <MapsPage />;
        case 'notifications':
          return <NotificationsPage />;
        default:
          return renderDashboardContent();
      }
    }
    
    // Default dashboard content for other navigation items
    return renderDashboardContent();
  };

  // Render the default dashboard content
  const renderDashboardContent = () => (
    <>
      {/* Header with time and welcome message */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
        position: 'relative',
        zIndex: 1,
      }}>
        <div>
          <h1 style={{ 
            fontSize: '28px', 
            fontWeight: 600, 
            marginBottom: '8px',
            background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
          }}>
            Security Dashboard
          </h1>
          <p style={{ 
            fontSize: '14px', 
            color: 'rgba(255, 255, 255, 0.7)',
            margin: 0,
          }}>
            Welcome back, Security Admin
          </p>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
        }}>
          <div style={{
            padding: '8px 16px',
            borderRadius: '8px',
            background: 'rgba(0, 229, 255, 0.1)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            fontSize: '14px',
            color: '#00e5ff',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <ClockIcon />
            {formatTime(currentTime)}
          </div>
        </div>
      </div>

      {/* Dashboard Grid Layout */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '20px',
        position: 'relative',
        zIndex: 1,
      }}>
        {/* Security Score Card */}
        <div style={{ gridColumn: '1 / 2', gridRow: '1 / 3' }}>
          <DashboardCard 
            title="SECURITY SCORE" 
            height={360}
            glowColor={securityScore < 30 ? 'rgba(0, 229, 255, 0.5)' : 'rgba(255, 99, 132, 0.5)'}
            glowIntensity={0.2}
          >
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 'calc(100% - 40px)',
            }}>
              <div style={{
                position: 'relative',
                width: '180px',
                height: '180px',
                marginBottom: '20px',
              }}>
                {/* Background circle */}
                <svg width="180" height="180" viewBox="0 0 180 180">
                  <circle
                    cx="90"
                    cy="90"
                    r="80"
                    fill="none"
                    stroke="rgba(255, 255, 255, 0.1)"
                    strokeWidth="12"
                  />
                  {/* Progress circle */}
                  <circle
                    cx="90"
                    cy="90"
                    r="80"
                    fill="none"
                    stroke="#00e5ff"
                    strokeWidth="12"
                    strokeDasharray={2 * Math.PI * 80}
                    strokeDashoffset={2 * Math.PI * 80 * (1 - securityScore / 100)}
                    strokeLinecap="round"
                    transform="rotate(-90 90 90)"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(0, 229, 255, 0.8))',
                    }}
                  />
                </svg>
                {/* Score text */}
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                }}>
                  <div style={{
                    fontSize: '48px',
                    fontWeight: 'bold',
                    color: '#00e5ff',
                    textShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
                    lineHeight: 1,
                  }}>
                    {securityScore}
                  </div>
                  <div style={{
                    fontSize: '16px',
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    / 100
                  </div>
                </div>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                borderRadius: '20px',
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.2)',
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: '#00e5ff',
                  boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                }} />
                <span style={{ fontSize: '14px', color: '#00e5ff' }}>
                  4% below industry average
                </span>
              </div>
            </div>
          </DashboardCard>
        </div>

        {/* Additional dashboard content would continue here... */}
        <div style={{ gridColumn: '2 / 4' }}>
          <DashboardCard title="SYSTEM STATUS">
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '200px',
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '16px',
            }}>
              Dashboard content continues here...
            </div>
          </DashboardCard>
        </div>
      </div>
    </>
  );

  // Card component for dashboard
  const DashboardCard = ({ 
    title, 
    children, 
    height = 'auto', 
    glowColor = 'rgba(0, 229, 255, 0.5)',
    glowIntensity = 0.1
  }: { 
    title: string; 
    children: React.ReactNode; 
    height?: string | number;
    glowColor?: string;
    glowIntensity?: number;
  }) => (
    <div style={{
      position: 'relative',
      width: '100%',
      height,
      borderRadius: '12px',
      padding: '16px',
      boxSizing: 'border-box',
      background: 'rgba(16, 24, 45, 0.7)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(0, 229, 255, 0.2)',
      boxShadow: `0 0 20px ${glowColor.replace('0.5', String(glowIntensity))}`,
      overflow: 'hidden',
      marginBottom: '20px',
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        background: `linear-gradient(90deg, transparent, ${glowColor}, transparent)`,
        opacity: 0.8,
      }} />
      <h2 style={{ 
        fontSize: '16px', 
        fontWeight: 500, 
        marginTop: '4px',
        marginBottom: '16px',
        color: 'rgba(255, 255, 255, 0.9)',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
      }}>
        {title}
      </h2>
      {children}
    </div>
  );

  // Format time for display
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit',
      hour12: false 
    });
  };

  // Generate random data for the network activity chart
  const generateNetworkData = () => {
    return Array.from({ length: 24 }, () => Math.floor(Math.random() * 50) + 10);
  };

  // Network activity data
  const networkData = generateNetworkData();

  // Threat data for visualization
  const threatData = [
    { type: 'Malware', count: 3, color: 'rgba(255, 99, 132, 0.8)' },
    { type: 'Phishing', count: 7, color: 'rgba(255, 159, 64, 0.8)' },
    { type: 'Unauthorized Access', count: 2, color: 'rgba(255, 205, 86, 0.8)' },
    { type: 'Data Breach', count: 0, color: 'rgba(75, 192, 192, 0.8)' },
    { type: 'DDoS', count: 1, color: 'rgba(153, 102, 255, 0.8)' }
  ];

  // Protected assets data
  const protectedAssets = [
    { name: 'Web Servers', count: 12, protected: 12 },
    { name: 'Databases', count: 8, protected: 7 },
    { name: 'User Endpoints', count: 156, protected: 143 },
    { name: 'Cloud Services', count: 23, protected: 23 },
    { name: 'IoT Devices', count: 42, protected: 38 }
  ];

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      background: `linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)`,
      color: 'white',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
      position: 'relative',
    }}>
      {/* Navigation Sidebar - with enhanced styling */}
      <div style={{
        position: 'relative',
        width: collapsed ? '80px' : '200px',
        background: `linear-gradient(135deg, 
                    rgba(16, 24, 45, 0.95) 0%, 
                    rgba(25, 35, 60, 0.95) 50%,
                    rgba(16, 24, 45, 0.95) 100%)`,
        borderRight: '1px solid rgba(0, 229, 255, 0.1)',
        padding: '16px 0',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        boxSizing: 'border-box',
        transition: 'all 0.3s ease',
        boxShadow: '0 0 25px rgba(0, 229, 255, 0.15), 0 0 50px rgba(0, 229, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        overflow: 'visible',
      }}>
        {/* Animated security grid background effect */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          backgroundImage: `
            radial-gradient(circle at ${50 + Math.sin(animationPhase * 0.05) * 10}% ${50 + Math.cos(animationPhase * 0.05) * 10}%, 
              rgba(0, 229, 255, 0.15) 0%, 
              transparent 50%),
            linear-gradient(${animationPhase * 3.6}deg, rgba(0, 229, 255, 0.03) 25%, transparent 25%, transparent 50%, 
              rgba(0, 229, 255, 0.03) 50%, rgba(0, 229, 255, 0.03) 75%, transparent 75%, transparent)
          `,
          backgroundSize: '100% 100%, 8px 8px',
          opacity: 0.4,
          zIndex: 0,
        }} />

        {/* Hexagon pattern overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          pointerEvents: 'none',
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 5 L55 20 L55 50 L30 65 L5 50 L5 20 Z' stroke='rgba(0, 229, 255, 0.1)' fill='none' stroke-width='0.5'/%3E%3C/svg%3E")`,
          backgroundSize: '30px 30px',
          opacity: 0.2,
          zIndex: 0,
        }} />

        {/* Logo/Shield in the top area */}
        <div 
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            marginBottom: '32px',
            padding: '0 16px',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              width: '48px',
              height: '48px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              borderRadius: '12px',
              color: '#00e5ff',
              background: 'rgba(0, 229, 255, 0.1)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.3)',
              transition: 'all 0.2s ease',
              marginBottom: '10px',
              backdropFilter: 'blur(5px)',
              WebkitBackdropFilter: 'blur(5px)',
            }}
          >
            <ShieldIcon />
          </div>
          {!collapsed && (
            <span style={{ 
              fontWeight: 'bold',
              fontSize: '16px',
              textAlign: 'center',
              background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              textShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
            }}>
              GuardBear
            </span>
          )}
        </div>

        {/* Main Navigation Section - Now centered in the column */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '12px',
          flex: 1,
          justifyContent: 'center',
          position: 'relative',
          zIndex: 1,
          width: '100%',
        }}>
          {/* Dashboard/Home Icon (selected in the image) */}
          <div 
            onClick={() => handleNavClick('dashboard')}
            style={getNavItemStyle('dashboard')}
          >
            <HomeIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Dashboard
              </span>
            )}
          </div>

          {/* Explore Navigation */}
          <div style={{ position: 'relative', width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <ExploreNavItem
              isCollapsed={collapsed}
              isExpanded={exploreExpanded}
              onToggle={handleExploreToggle}
              activeSubItem={activeNav === 'explore' ? activeExploreItem : undefined}
              isActive={activeNav === 'explore'}
            />
            {activeNav === 'explore' && (
              <ExploreDropdown
                isExpanded={exploreExpanded}
                isCollapsed={collapsed}
                activeItem={activeNav === 'explore' ? activeExploreItem : undefined}
                onItemClick={handleExploreItemClick}
              />
            )}
          </div>

          {/* Grid Icon */}
          <div 
            onClick={() => handleNavClick('grid')}
            style={getNavItemStyle('grid')}
          >
            <GridIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Grid View
              </span>
            )}
          </div>

          {/* Clock/History Icon */}
          <div 
            onClick={() => handleNavClick('history')}
            style={getNavItemStyle('history')}
          >
            <ClockIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                History
              </span>
            )}
          </div>

          {/* Files/Document Icon */}
          <div 
            onClick={() => handleNavClick('files')}
            style={getNavItemStyle('files')}
          >
            <FileIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Files
              </span>
            )}
          </div>

          {/* User/Profile Icon */}
          <div 
            onClick={() => handleNavClick('profile')}
            style={getNavItemStyle('profile')}
          >
            <UserIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Profile
              </span>
            )}
          </div>
        </div>

        {/* Bottom Section with Settings and User Profile */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          marginTop: 'auto',
          paddingTop: '15px',
          position: 'relative',
          zIndex: 1,
          width: '100%',
        }}>
          {/* Settings Gear */}
          <div 
            onClick={() => handleNavClick('settings')}
            style={getNavItemStyle('settings')}
          >
            <SettingsIcon />
            {!collapsed && (
              <span style={{ 
                marginLeft: '10px',
                fontSize: '14px',
                display: 'inline-block',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1
              }}>
                Settings
              </span>
            )}
          </div>

          {/* User Profile at bottom */}
          <div style={{
            width: '32px',
            height: '32px',
            overflow: 'hidden',
            borderRadius: '50%',
            marginBottom: '10px',
            border: '2px solid #00e5ff',
            cursor: 'pointer',
            boxShadow: '0 0 5px rgba(0, 229, 255, 0.5)'
          }}>
            <img 
              src="/avatar-placeholder.png" 
              alt="User Profile" 
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              onError={(e) => {
                // Fallback when image doesn't load
                e.currentTarget.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2300e5ff"><path d="M12 12a5 5 0 1 0 0-10 5 5 0 0 0 0 10z" /><path d="M12 14c-6.1 0-10 4-10 8h20c0-4-3.9-8-10-8z" /></svg>';
              }}
            />
          </div>

          {!collapsed && (
            <div style={{
              textAlign: 'center',
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.7)',
              marginBottom: '5px'
            }}>
              Security Admin
            </div>
          )}

          {/* Timestamp visible at bottom of the nav */}
          <div style={{ 
            fontSize: '10px', 
            color: 'rgba(255, 255, 255, 0.4)',
            marginTop: '5px' 
          }}>
            03:31
          </div>
        </div>
      </div>

      {/* Toggle Button - Moved outside the sidebar for better clickability */}
      <div 
        onClick={toggleSidebar}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        style={{
          position: 'absolute',
          top: '18px',
          left: collapsed ? '68px' : '188px', // Position relative to left instead of right
          zIndex: 1000, // Higher z-index to ensure it's above everything
          width: '24px', // Slightly larger
          height: '24px', // Slightly larger
          background: isHovering ? 'rgba(0, 229, 255, 0.4)' : 'rgba(0, 229, 255, 0.2)',
          border: '1px solid rgba(0, 229, 255, 0.5)',
          color: isHovering ? 'white' : 'rgba(255, 255, 255, 0.8)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          transition: 'all 0.3s ease', // Match the sidebar transition duration
          boxShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
        }}
      >
        {collapsed ? (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ width: '16px', height: '16px', pointerEvents: 'none' }}>
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ width: '16px', height: '16px', pointerEvents: 'none' }}>
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        )}
      </div>

      {/* Main Content Area */}
      <div style={{
        flex: 1,
        padding: '24px',
        boxSizing: 'border-box',
        overflowY: 'auto',
        position: 'relative',
      }}>
        {/* Background grid pattern */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            linear-gradient(rgba(0, 229, 255, 0.05) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 229, 255, 0.05) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px',
          pointerEvents: 'none',
          opacity: 0.3,
          zIndex: 0,
        }} />

        {/* Render main content based on navigation */}
        {renderMainContent()}

        {/* Dashboard Grid Layout */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '20px',
          position: 'relative',
          zIndex: 1,
        }}>
          {/* Security Score Card */}
          <div style={{ gridColumn: '1 / 2', gridRow: '1 / 3' }}>
            <DashboardCard 
              title="SECURITY SCORE" 
              height={360}
              glowColor={securityScore < 30 ? 'rgba(0, 229, 255, 0.5)' : 'rgba(255, 99, 132, 0.5)'}
              glowIntensity={0.2}
            >
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: 'calc(100% - 40px)',
              }}>
                <div style={{
                  position: 'relative',
                  width: '180px',
                  height: '180px',
                  marginBottom: '20px',
                }}>
                  {/* Background circle */}
                  <svg width="180" height="180" viewBox="0 0 180 180">
                    <circle
                      cx="90"
                      cy="90"
                      r="80"
                      fill="none"
                      stroke="rgba(255, 255, 255, 0.1)"
                      strokeWidth="12"
                    />
                    {/* Progress circle */}
                    <circle
                      cx="90"
                      cy="90"
                      r="80"
                      fill="none"
                      stroke="#00e5ff"
                      strokeWidth="12"
                      strokeDasharray={2 * Math.PI * 80}
                      strokeDashoffset={2 * Math.PI * 80 * (1 - securityScore / 100)}
                      strokeLinecap="round"
                      transform="rotate(-90 90 90)"
                      style={{
                        filter: 'drop-shadow(0 0 8px rgba(0, 229, 255, 0.8))',
                      }}
                    />
                  </svg>
                  {/* Score text */}
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    textAlign: 'center',
                  }}>
                    <div style={{
                      fontSize: '48px',
                      fontWeight: 'bold',
                      color: '#00e5ff',
                      textShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
                      lineHeight: 1,
                    }}>
                      {securityScore}
                    </div>
                    <div style={{
                      fontSize: '16px',
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      / 100
                    </div>
                  </div>
                </div>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '8px 16px',
                  borderRadius: '20px',
                  background: 'rgba(0, 229, 255, 0.1)',
                  border: '1px solid rgba(0, 229, 255, 0.2)',
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <span style={{ fontSize: '14px', color: '#00e5ff' }}>
                    4% below industry average
                  </span>
                </div>
              </div>
            </DashboardCard>
          </div>

          {/* Security Scan Status */}
          <div style={{ gridColumn: '2 / 4' }}>
            <DashboardCard title="SECURITY SCAN STATUS">
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}>
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '8px',
                      background: 'rgba(0, 229, 255, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#00e5ff',
                    }}>
                      <ShieldIcon />
                    </div>
                    <div>
                      <div style={{ fontSize: '14px', fontWeight: 500 }}>
                        {scanComplete ? 'Scan Complete' : 'Scanning System'}
                      </div>
                      <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>
                        {scanComplete ? 'Last scan: Just now' : 'Analyzing security vulnerabilities...'}
                      </div>
                    </div>
                  </div>
                  <div style={{
                    fontSize: '14px',
                    color: '#00e5ff',
                  }}>
                    {scanComplete ? '100%' : `${scanProgress}%`}
                  </div>
                </div>

                {/* Progress bar */}
                <div style={{
                  width: '100%',
                  height: '6px',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '3px',
                  overflow: 'hidden',
                }}>
                  <div style={{
                    width: `${scanProgress}%`,
                    height: '100%',
                    background: 'linear-gradient(90deg, #00e5ff, #0088ff)',
                    borderRadius: '3px',
                    transition: 'width 0.3s ease',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.5)',
                  }} />
                </div>

                {/* Scan details */}
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '12px',
                  marginTop: '8px',
                }}>
                  {[
                    { label: 'Endpoints Scanned', value: '156/156', icon: <ServerIcon /> },
                    { label: 'Threats Detected', value: '13', icon: <AlertIcon /> },
                    { label: 'Vulnerabilities Fixed', value: '7', icon: <CheckIcon /> },
                  ].map((item, index) => (
                    <div key={index} style={{
                      padding: '12px',
                      borderRadius: '8px',
                      background: 'rgba(255, 255, 255, 0.05)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                    }}>
                      <div style={{
                        width: '32px',
                        height: '32px',
                        borderRadius: '8px',
                        background: 'rgba(0, 229, 255, 0.1)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#00e5ff',
                      }}>
                        {item.icon}
                      </div>
                      <div>
                        <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>
                          {item.label}
                        </div>
                        <div style={{ fontSize: '16px', fontWeight: 500 }}>
                          {item.value}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </DashboardCard>
          </div>

          {/* Threat Analysis */}
          <div style={{ gridColumn: '2 / 3', gridRow: '2 / 3' }}>
            <DashboardCard 
              title="THREAT ANALYSIS" 
              height={200}
              glowColor="rgba(255, 99, 132, 0.5)"
              glowIntensity={0.1}
            >
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                height: 'calc(100% - 30px)',
              }}>
                {threatData.map((threat, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '8px',
                  }}>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}>
                      <div style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: threat.color,
                        boxShadow: threat.count > 0 ? `0 0 8px ${threat.color}` : 'none',
                      }} />
                      <span style={{ fontSize: '13px' }}>{threat.type}</span>
                    </div>
                    <span style={{ 
                      fontSize: '13px', 
                      fontWeight: 500,
                      color: threat.count > 0 ? 'white' : 'rgba(255, 255, 255, 0.5)',
                    }}>
                      {threat.count}
                    </span>
                  </div>
                ))}
              </div>
            </DashboardCard>
          </div>

          {/* Network Activity */}
          <div style={{ gridColumn: '3 / 4', gridRow: '2 / 3' }}>
            <DashboardCard 
              title="NETWORK ACTIVITY" 
              height={200}
              glowColor="rgba(0, 229, 255, 0.5)"
              glowIntensity={0.1}
            >
              <div style={{
                height: 'calc(100% - 30px)',
                display: 'flex',
                alignItems: 'flex-end',
                gap: '2px',
              }}>
                {networkData.map((value, index) => (
                  <div
                    key={index}
                    style={{
                      flex: 1,
                      height: `${value}%`,
                      backgroundColor: index % 2 === 0 ? 'rgba(0, 229, 255, 0.5)' : 'rgba(0, 229, 255, 0.3)',
                      borderRadius: '2px 2px 0 0',
                      position: 'relative',
                    }}
                  >
                    {index % 4 === 0 && (
                      <div style={{
                        position: 'absolute',
                        bottom: '-20px',
                        left: '50%',
                        transform: 'translateX(-50%)',
                        fontSize: '10px',
                        color: 'rgba(255, 255, 255, 0.5)',
                      }}>
                        {index}h
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </DashboardCard>
          </div>

          {/* Protected Assets */}
          <div style={{ gridColumn: '1 / 4' }}>
            <DashboardCard title="PROTECTED ASSETS">
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(5, 1fr)',
                gap: '16px',
              }}>
                {protectedAssets.map((asset, index) => (
                  <div key={index} style={{
                    padding: '16px',
                    borderRadius: '8px',
                    background: 'rgba(255, 255, 255, 0.05)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '12px',
                  }}>
                    <div style={{
                      width: '48px',
                      height: '48px',
                      borderRadius: '12px',
                      background: 'rgba(0, 229, 255, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: '#00e5ff',
                    }}>
                      {index === 0 ? <ServerIcon /> : 
                       index === 1 ? <FileIcon /> : 
                       index === 2 ? <UserIcon /> : 
                       index === 3 ? <GlobeIcon /> : <LockIcon />}
                    </div>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '14px', fontWeight: 500 }}>
                        {asset.name}
                      </div>
                      <div style={{ 
                        fontSize: '20px', 
                        fontWeight: 600,
                        color: '#00e5ff',
                        marginTop: '4px',
                      }}>
                        {asset.protected}/{asset.count}
                      </div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: 'rgba(255, 255, 255, 0.5)',
                        marginTop: '4px',
                      }}>
                        {Math.round((asset.protected / asset.count) * 100)}% Protected
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </DashboardCard>
          </div>

          {/* Recent Activity Log */}
          <div style={{ gridColumn: '1 / 4' }}>
            <DashboardCard title="RECENT ACTIVITY LOG">
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}>
                {[
                  { time: '03:28', event: 'Malware attempt blocked', severity: 'high', ip: '*************' },
                  { time: '03:15', event: 'User authentication successful', severity: 'info', ip: '*********' },
                  { time: '02:47', event: 'Firewall rule updated', severity: 'info', ip: 'System' },
                  { time: '02:32', event: 'Suspicious login attempt', severity: 'medium', ip: '*************' },
                  { time: '01:15', event: 'System scan completed', severity: 'info', ip: 'System' },
                ].map((log, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '12px',
                    borderRadius: '8px',
                    background: 'rgba(255, 255, 255, 0.05)',
                    gap: '16px',
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: log.severity === 'high' ? '#ff4d6d' : 
                                      log.severity === 'medium' ? '#ffaa00' : '#00e5ff',
                      boxShadow: `0 0 8px ${log.severity === 'high' ? 'rgba(255, 77, 109, 0.8)' : 
                                          log.severity === 'medium' ? 'rgba(255, 170, 0, 0.8)' : 
                                          'rgba(0, 229, 255, 0.8)'}`,
                    }} />
                    <div style={{ width: '60px', fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                      {log.time}
                    </div>
                    <div style={{ flex: 1, fontSize: '14px' }}>
                      {log.event}
                    </div>
                    <div style={{ 
                      fontSize: '13px', 
                      color: 'rgba(255, 255, 255, 0.5)',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      background: 'rgba(255, 255, 255, 0.1)',
                    }}>
                      {log.ip}
                    </div>
                  </div>
                ))}
              </div>
            </DashboardCard>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CyberSecurityDashboard; 