import React from 'react';

/**
 * Visualize page component.
 * 
 * This component renders the Visualize page content from the Explore section.
 */
const Visualize: React.FC = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Visualize
      </h1>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(2, 1fr)',
        gap: '24px',
        marginTop: '24px',
      }}>
        {/* Visualization options */}
        <div style={{
          borderRadius: '12px',
          padding: '20px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        }}>
          <h3 style={{ 
            fontSize: '18px', 
            fontWeight: 500, 
            marginBottom: '16px',
            color: '#00e5ff',
          }}>
            Visualization Types
          </h3>
          
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
          }}>
            {['Line Chart', 'Bar Chart', 'Pie Chart', 'Area Chart', 'Scatter Plot', 'Heatmap', 'Network Graph'].map((type, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 16px',
                borderRadius: '8px',
                background: 'rgba(0, 229, 255, 0.05)',
                border: '1px solid rgba(0, 229, 255, 0.1)',
                cursor: 'pointer',
              }}>
                <span style={{ color: 'white' }}>{type}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Preview area */}
        <div style={{
          borderRadius: '12px',
          padding: '20px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '400px',
        }}>
          <div style={{
            fontSize: '16px',
            color: 'rgba(255, 255, 255, 0.7)',
            textAlign: 'center',
          }}>
            Select a visualization type to preview
          </div>
        </div>
      </div>
    </div>
  );
};

export default Visualize;