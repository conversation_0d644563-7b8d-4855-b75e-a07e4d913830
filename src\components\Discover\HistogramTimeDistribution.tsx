import React, { useState, useRef } from 'react';
import { useDiscoverTime } from '../../hooks';

interface HistogramTimeDistributionProps {
  height?: number;
}

/**
 * Component to display time-based distribution of logs in the histogram
 */
const HistogramTimeDistribution: React.FC<HistogramTimeDistributionProps> = ({ 
  height = 150 
}) => {
  const { 
    groupedData, 
    timeInterval, 
    formatDate,
    setCustomTimeRange
  } = useDiscoverTime();
  
  const [hoveredBarIndex, setHoveredBarIndex] = useState<number | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const chartRef = useRef<HTMLDivElement>(null);
  
  // Format count with thousands separator
  const formatCount = (count: number): string => {
    return new Intl.NumberFormat().format(count);
  };
  
  // Find the maximum count for scaling
  const maxCount = Math.max(...groupedData.map(item => item.count), 1);
  
  // Get interval label
  const getIntervalLabel = (): string => {
    switch (timeInterval) {
      case 'minute': return 'minute';
      case 'hour': return 'hour';
      case 'day': return 'day';
      case 'week': return 'week';
      default: return 'interval';
    }
  };
  
  // Calculate time range for each bar
  const barTimeRanges = groupedData.map((item, index, array) => {
    const start = item.timestamp;
    let end;
    
    if (index < array.length - 1) {
      end = array[index + 1].timestamp;
    } else {
      // For the last bar, estimate the end time based on the interval
      const msPerBar = index > 0 
        ? array[index].timestamp.getTime() - array[index - 1].timestamp.getTime()
        : 3600000; // Default to 1 hour if we can't calculate
      end = new Date(start.getTime() + msPerBar);
    }
    
    return { start, end };
  });
  
  // Handle bar hover
  const handleBarHover = (index: number, e: React.MouseEvent) => {
    setHoveredBarIndex(index);
    
    if (chartRef.current) {
      const rect = chartRef.current.getBoundingClientRect();
      setTooltipPosition({ 
        x: e.clientX - rect.left,
        y: e.clientY - rect.top - 10
      });
    }
  };
  
  // Handle bar click to filter to that time period
  const handleBarClick = (index: number) => {
    if (index >= 0 && index < barTimeRanges.length) {
      const { start, end } = barTimeRanges[index];
      setCustomTimeRange(start, end);
    }
  };
  
  // Get color for bar based on count relative to max
  const getBarColor = (count: number, isHovered: boolean): string => {
    const ratio = count / maxCount;
    const intensity = Math.min(0.3 + ratio * 0.7, 1);
    
    return isHovered 
      ? `rgba(0, 229, 255, ${intensity})` 
      : `rgba(0, 229, 255, ${intensity * 0.8})`;
  };
  
  return (
    <div 
      ref={chartRef}
      style={{
        height: `${height}px`,
        position: 'relative',
        marginTop: '16px',
        marginBottom: '24px',
      }}
    >
      {/* Y-axis labels */}
      <div style={{
        position: 'absolute',
        left: 0,
        top: 0,
        bottom: '20px',
        width: '40px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: '10px',
        textAlign: 'right',
        paddingRight: '8px',
      }}>
        <div>{formatCount(maxCount)}</div>
        <div>{formatCount(Math.floor(maxCount * 0.75))}</div>
        <div>{formatCount(Math.floor(maxCount * 0.5))}</div>
        <div>{formatCount(Math.floor(maxCount * 0.25))}</div>
        <div>0</div>
      </div>
      
      {/* Y-axis grid lines */}
      <div style={{
        position: 'absolute',
        left: '40px',
        top: 0,
        right: 0,
        bottom: '20px',
        pointerEvents: 'none',
      }}>
        {[0.25, 0.5, 0.75, 1].map((ratio) => (
          <div 
            key={ratio}
            style={{
              position: 'absolute',
              top: `${(1 - ratio) * 100}%`,
              left: 0,
              right: 0,
              height: '1px',
              background: 'rgba(255, 255, 255, 0.1)',
            }}
          />
        ))}
      </div>
      
      {/* Chart bars */}
      <div style={{
        position: 'absolute',
        left: '40px',
        right: 0,
        top: 0,
        bottom: '20px',
        display: 'flex',
        alignItems: 'flex-end',
      }}>
        {groupedData.map((item, index) => {
          const height = (item.count / maxCount) * 100;
          const isHovered = hoveredBarIndex === index;
          
          return (
            <div 
              key={index}
              style={{
                flexGrow: 1,
                height: `${height}%`,
                background: getBarColor(item.count, isHovered),
                margin: '0 1px',
                position: 'relative',
                minWidth: '3px',
                maxWidth: '30px',
                borderRadius: '2px 2px 0 0',
                transition: 'background-color 0.2s ease',
                cursor: 'pointer',
                boxShadow: isHovered ? '0 0 8px rgba(0, 229, 255, 0.5)' : 'none',
              }}
              onMouseEnter={(e) => handleBarHover(index, e)}
              onMouseLeave={() => setHoveredBarIndex(null)}
              onClick={() => handleBarClick(index)}
              title={`${formatDate(item.timestamp)}: ${formatCount(item.count)} logs`}
            />
          );
        })}
        
        {/* X-axis line */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: '1px',
          background: 'rgba(255, 255, 255, 0.3)',
        }} />
      </div>
      
      {/* X-axis labels */}
      <div style={{
        position: 'absolute',
        left: '40px',
        right: 0,
        bottom: 0,
        height: '20px',
        display: 'flex',
        justifyContent: 'space-between',
        color: 'rgba(255, 255, 255, 0.5)',
        fontSize: '10px',
      }}>
        {groupedData.length > 0 && (
          <>
            <div>{formatDate(groupedData[0].timestamp, false)}</div>
            {groupedData.length > 4 && (
              <>
                <div>{formatDate(groupedData[Math.floor(groupedData.length / 4)].timestamp, false)}</div>
                <div>{formatDate(groupedData[Math.floor(groupedData.length / 2)].timestamp, false)}</div>
                <div>{formatDate(groupedData[Math.floor(groupedData.length * 3 / 4)].timestamp, false)}</div>
              </>
            )}
            <div>{formatDate(groupedData[groupedData.length - 1].timestamp, false)}</div>
          </>
        )}
      </div>
      
      {/* Tooltip */}
      {hoveredBarIndex !== null && groupedData[hoveredBarIndex] && (
        <div style={{
          position: 'absolute',
          top: tooltipPosition.y - 60,
          left: tooltipPosition.x,
          transform: 'translateX(-50%)',
          background: 'rgba(0, 0, 0, 0.85)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '8px 12px',
          color: 'white',
          fontSize: '12px',
          zIndex: 1000,
          pointerEvents: 'none',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
          minWidth: '150px',
          textAlign: 'center',
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '4px', borderBottom: '1px solid rgba(0, 229, 255, 0.2)', paddingBottom: '4px' }}>
            {formatDate(groupedData[hoveredBarIndex].timestamp)}
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            <span>Logs:</span>
            <span style={{ color: 'rgba(0, 229, 255, 0.9)', fontWeight: 'bold' }}>
              {formatCount(groupedData[hoveredBarIndex].count)}
            </span>
          </div>
          <div style={{ fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)', marginTop: '4px' }}>
            Click to filter to this {getIntervalLabel()}
          </div>
        </div>
      )}
    </div>
  );
};

export default HistogramTimeDistribution;