import React, { useState } from 'react';
import { useDiscover } from '../../context/DiscoverContext';
import { useDiscoverSearch } from '../../hooks';
import { DiscoverUtils } from '../../utils/discoverUtils';

/**
 * Component to display rule group distribution in the histogram
 */
const HistogramRuleDistribution: React.FC = () => {
  const { state } = useDiscover();
  const { filteredData } = state;
  const { addFilter } = useDiscoverSearch();
  const [expanded, setExpanded] = useState(false);
  const [hoveredGroup, setHoveredGroup] = useState<string | null>(null);
  
  // Calculate rule group distribution
  const ruleGroupDistribution = DiscoverUtils.calculateRuleGroupDistribution(filteredData);
  
  // Sort groups by count
  const sortedGroups = Object.entries(ruleGroupDistribution)
    .sort((a, b) => b[1] - a[1])
    .slice(0, expanded ? undefined : 5); // Show top 5 if not expanded
  
  // Format count with thousands separator
  const formatCount = (count: number): string => {
    return new Intl.NumberFormat().format(count);
  };
  
  // Get total count
  const totalCount = Object.values(ruleGroupDistribution).reduce((sum, count) => sum + count, 0);
  
  // Get percentage
  const getPercentage = (count: number): string => {
    if (totalCount === 0) return '0%';
    return `${Math.round((count / totalCount) * 100)}%`;
  };
  
  // Get color for rule group (generate consistent colors based on group name)
  const getGroupColor = (groupName: string): string => {
    // Simple hash function to generate a consistent hue for each group name
    let hash = 0;
    for (let i = 0; i < groupName.length; i++) {
      hash = groupName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Use the hash to generate a hue between 0 and 360
    const hue = hash % 360;
    
    // Return an HSL color with fixed saturation and lightness
    return `hsla(${hue}, 70%, 60%, 0.8)`;
  };
  
  if (sortedGroups.length === 0) {
    return null;
  }
  
  return (
    <div style={{
      marginTop: '16px',
      padding: '12px',
      background: 'rgba(0, 0, 0, 0.2)',
      borderRadius: '4px',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        <h4 style={{ 
          margin: 0, 
          color: 'white',
          fontSize: '14px',
        }}>
          Rule Groups
        </h4>
        
        {Object.keys(ruleGroupDistribution).length > 5 && (
          <button
            onClick={() => setExpanded(!expanded)}
            style={{
              background: 'transparent',
              border: 'none',
              color: '#00e5ff',
              cursor: 'pointer',
              fontSize: '12px',
              padding: '4px',
            }}
          >
            {expanded ? 'Show less' : 'Show all'}
          </button>
        )}
      </div>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {sortedGroups.map(([group, count]) => {
          const isHovered = hoveredGroup === group;
          return (
            <div 
              key={group} 
              style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                gap: '4px',
                cursor: 'pointer',
                padding: '4px',
                borderRadius: '4px',
                background: isHovered ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                transition: 'background-color 0.2s ease'
              }}
              onClick={() => addFilter('rule.groups', group, 'is')}
              onMouseEnter={() => setHoveredGroup(group)}
              onMouseLeave={() => setHoveredGroup(null)}
              title={`Click to filter for ${group}`}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    background: getGroupColor(group),
                    boxShadow: isHovered ? '0 0 5px rgba(0, 229, 255, 0.5)' : 'none'
                  }} />
                  <span style={{ 
                    color: isHovered ? '#00e5ff' : 'white', 
                    fontSize: '12px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '200px',
                    transition: 'color 0.2s ease'
                  }}>
                    {group}
                  </span>
                </div>
                <div style={{ 
                  color: isHovered ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)', 
                  fontSize: '12px', 
                  whiteSpace: 'nowrap',
                  transition: 'color 0.2s ease'
                }}>
                  {formatCount(count)} ({getPercentage(count)})
                </div>
              </div>
              
              <div style={{ height: '6px', width: '100%', background: 'rgba(0, 0, 0, 0.3)', borderRadius: '3px', overflow: 'hidden' }}>
                <div
                  style={{
                    height: '100%',
                    width: `${(count / totalCount) * 100}%`,
                    background: isHovered ? `${getGroupColor(group).replace('0.8', '1')}` : getGroupColor(group),
                    borderRadius: '3px',
                    boxShadow: isHovered ? '0 0 8px rgba(0, 229, 255, 0.5)' : 'none',
                    transition: 'all 0.2s ease'
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default HistogramRuleDistribution;