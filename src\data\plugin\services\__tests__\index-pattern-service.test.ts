import { IndexPatternService } from '../index-pattern-service';
import { Dataset, DatasetField, IndexPatternSpec } from '../../interfaces';

describe('IndexPatternService', () => {
  let service: IndexPatternService;
  
  beforeEach(() => {
    service = new IndexPatternService();
  });
  
  test('should register and retrieve patterns', () => {
    const pattern: IndexPatternSpec = {
      id: 'test-pattern',
      title: 'Test Pattern',
      fields: [
        { name: 'field1', type: 'string', searchable: true, aggregatable: true },
        { name: 'field2', type: 'number', searchable: true, aggregatable: true }
      ]
    };
    
    service.register(pattern);
    
    const retrieved = service.get('test-pattern');
    expect(retrieved).toBe(pattern);
    
    const all = service.getAll();
    expect(all).toContain(pattern);
  });
  
  test('should create temporary patterns from datasets', () => {
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample',
      timeFieldName: '@timestamp'
    };
    
    const fields: DatasetField[] = [
      { name: '@timestamp', type: 'date', searchable: true, aggregatable: true },
      { name: 'message', type: 'string', searchable: true, aggregatable: false },
      { name: 'level', type: 'string', searchable: true, aggregatable: true }
    ];
    
    const pattern = service.createTemporaryPattern(dataset, fields);
    
    expect(pattern).toHaveProperty('id', 'temp_test-dataset');
    expect(pattern).toHaveProperty('title', 'Test Dataset');
    expect(pattern).toHaveProperty('timeFieldName', '@timestamp');
    expect(pattern).toHaveProperty('fields', fields);
    
    // The pattern should be cached
    const cached = service.get('temp_test-dataset');
    expect(cached).toBe(pattern);
  });
  
  test('should get or create patterns', async () => {
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample',
      timeFieldName: '@timestamp'
    };
    
    const fields: DatasetField[] = [
      { name: '@timestamp', type: 'date', searchable: true, aggregatable: true },
      { name: 'message', type: 'string', searchable: true, aggregatable: false }
    ];
    
    // First call should create the pattern
    const pattern1 = await service.getOrCreatePattern(dataset, fields);
    expect(pattern1).toHaveProperty('id', 'temp_test-dataset');
    
    // Second call should return the cached pattern
    const pattern2 = await service.getOrCreatePattern(dataset);
    expect(pattern2).toBe(pattern1);
    
    // Clear the cache
    service.clearCache();
    
    // Should throw an error if no fields are provided and no cached pattern exists
    await expect(service.getOrCreatePattern(dataset)).rejects.toThrow();
  });
  
  test('should handle cache operations', () => {
    const dataset: Dataset = {
      id: 'test-dataset',
      title: 'Test Dataset',
      type: 'sample'
    };
    
    const fields: DatasetField[] = [
      { name: 'field1', type: 'string', searchable: true, aggregatable: true }
    ];
    
    // Create a pattern and cache it
    const pattern = service.createTemporaryPattern(dataset, fields);
    expect(service.get('temp_test-dataset')).toBe(pattern);
    
    // Remove from cache
    service.removeFromCache('temp_test-dataset');
    expect(service.get('temp_test-dataset')).toBeUndefined();
    
    // Create again
    const pattern2 = service.createTemporaryPattern(dataset, fields);
    expect(service.get('temp_test-dataset')).toBe(pattern2);
    
    // Clear cache
    service.clearCache();
    expect(service.get('temp_test-dataset')).toBeUndefined();
  });
});