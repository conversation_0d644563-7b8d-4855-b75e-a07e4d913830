import { LanguageServiceImpl } from '../language-service';
import { LanguageConfig } from '../../interfaces';

describe('LanguageService', () => {
  let languageService: LanguageServiceImpl;
  let mockLanguageConfig: LanguageConfig;

  beforeEach(() => {
    // Create a new instance of LanguageService for each test
    languageService = new LanguageServiceImpl();

    // Create a mock language configuration
    mockLanguageConfig = {
      id: 'test-language',
      name: 'Test Language',
      description: 'A test query language',
      syntax: {
        highlight: jest.fn().mockImplementation((query) => `<highlighted>${query}</highlighted>`),
        validate: jest.fn().mockImplementation((query) => ({ 
          valid: query.length > 0,
          error: query.length > 0 ? undefined : 'Query cannot be empty'
        }))
      },
      autocomplete: {
        getCompletions: jest.fn().mockResolvedValue([
          { value: 'test', score: 1, meta: 'keyword' },
          { value: 'example', score: 0.8, meta: 'keyword' }
        ])
      }
    };
  });

  describe('Language Registration', () => {
    it('should register a language', () => {
      languageService.registerLanguage(mockLanguageConfig);
      const retrievedLanguage = languageService.getLanguage('test-language');
      expect(retrievedLanguage).toBe(mockLanguageConfig);
    });

    it('should throw an error when registering an invalid language', () => {
      expect(() => {
        languageService.registerLanguage(undefined as unknown as LanguageConfig);
      }).toThrow('Invalid language configuration');
    });

    it('should get all registered languages', () => {
      languageService.registerLanguage(mockLanguageConfig);
      const languages = languageService.getLanguages();
      expect(languages).toHaveLength(1);
      expect(languages[0]).toBe(mockLanguageConfig);
    });

    it('should remove a language', () => {
      languageService.registerLanguage(mockLanguageConfig);
      languageService.removeLanguage('test-language');
      const languages = languageService.getLanguages();
      expect(languages).toHaveLength(0);
    });
  });

  describe('Query Validation', () => {
    beforeEach(() => {
      languageService.registerLanguage(mockLanguageConfig);
    });

    it('should validate a valid query', () => {
      const result = languageService.validateQuery('valid query', 'test-language');
      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
      expect(mockLanguageConfig.syntax.validate).toHaveBeenCalledWith('valid query');
    });

    it('should validate an invalid query', () => {
      const result = languageService.validateQuery('', 'test-language');
      expect(result.valid).toBe(false);
      expect(result.error).toBe('Query cannot be empty');
    });

    it('should return invalid for unknown language', () => {
      const result = languageService.validateQuery('query', 'unknown-language');
      expect(result.valid).toBe(false);
      expect(result.error).toBe("Language 'unknown-language' not found");
    });
  });

  describe('Query Highlighting', () => {
    beforeEach(() => {
      languageService.registerLanguage(mockLanguageConfig);
    });

    it('should highlight a query', () => {
      const result = languageService.highlightQuery('test query', 'test-language');
      expect(result).toBe('<highlighted>test query</highlighted>');
      expect(mockLanguageConfig.syntax.highlight).toHaveBeenCalledWith('test query');
    });

    it('should return original query for unknown language', () => {
      const result = languageService.highlightQuery('test query', 'unknown-language');
      expect(result).toBe('test query');
    });
  });

  describe('Query Autocompletion', () => {
    beforeEach(() => {
      languageService.registerLanguage(mockLanguageConfig);
    });

    it('should get completions for a query', async () => {
      const completions = await languageService.getCompletions('test', 4, 'test-language');
      expect(completions).toHaveLength(2);
      expect(completions[0].value).toBe('test');
      expect(completions[1].value).toBe('example');
      expect(mockLanguageConfig.autocomplete?.getCompletions).toHaveBeenCalledWith('test', 4);
    });

    it('should return empty array for unknown language', async () => {
      const completions = await languageService.getCompletions('test', 4, 'unknown-language');
      expect(completions).toHaveLength(0);
    });

    it('should return empty array for language without autocomplete', async () => {
      const languageWithoutAutocomplete: LanguageConfig = {
        id: 'basic-language',
        name: 'Basic Language',
        description: 'A basic language without autocomplete',
        syntax: {
          highlight: jest.fn(),
          validate: jest.fn().mockReturnValue({ valid: true })
        }
      };
      
      languageService.registerLanguage(languageWithoutAutocomplete);
      const completions = await languageService.getCompletions('test', 4, 'basic-language');
      expect(completions).toHaveLength(0);
    });

    it('should check if a language supports autocomplete', () => {
      expect(languageService.supportsAutocomplete('test-language')).toBe(true);
      
      const languageWithoutAutocomplete: LanguageConfig = {
        id: 'basic-language',
        name: 'Basic Language',
        description: 'A basic language without autocomplete',
        syntax: {
          highlight: jest.fn(),
          validate: jest.fn()
        }
      };
      
      languageService.registerLanguage(languageWithoutAutocomplete);
      expect(languageService.supportsAutocomplete('basic-language')).toBe(false);
    });

    it('should handle errors in autocomplete', async () => {
      const errorLanguage: LanguageConfig = {
        id: 'error-language',
        name: 'Error Language',
        description: 'A language that throws errors',
        syntax: {
          highlight: jest.fn(),
          validate: jest.fn().mockReturnValue({ valid: true })
        },
        autocomplete: {
          getCompletions: jest.fn().mockRejectedValue(new Error('Autocomplete error'))
        }
      };
      
      languageService.registerLanguage(errorLanguage);
      const completions = await languageService.getCompletions('test', 4, 'error-language');
      expect(completions).toHaveLength(0);
    });
  });
});