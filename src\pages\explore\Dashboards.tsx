import React from 'react';

/**
 * Dashboards page component.
 * 
 * This component renders the Dashboards page content from the Explore section.
 */
const Dashboards: React.FC = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Dashboards
      </h1>
      
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
        gap: '20px',
        marginTop: '24px',
      }}>
        {/* Dashboard cards */}
        {['Security Overview', 'Network Traffic', 'Threat Intelligence', 'Compliance', 'User Activity'].map((dashboard, index) => (
          <div key={index} style={{
            borderRadius: '12px',
            padding: '16px',
            background: 'rgba(16, 24, 45, 0.7)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
          }}>
            <h3 style={{ 
              fontSize: '18px', 
              fontWeight: 500, 
              marginBottom: '12px',
              color: '#00e5ff',
            }}>
              {dashboard}
            </h3>
            <p style={{ 
              fontSize: '14px', 
              color: 'rgba(255, 255, 255, 0.7)',
              marginBottom: '16px',
            }}>
              Last updated: Today
            </p>
            <div style={{
              display: 'flex',
              justifyContent: 'flex-end',
            }}>
              <button style={{
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '6px 12px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                View
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Dashboards;