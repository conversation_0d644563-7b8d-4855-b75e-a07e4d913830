import { LanguageConfig, Completion } from '../interfaces';

/**
 * A simple query language configuration for demonstration purposes
 * This language supports basic field:value syntax with AND/OR operators
 */
export const simpleQueryLanguage: LanguageConfig = {
  id: 'simple',
  name: 'Simple Query',
  description: 'A simple field:value query language with AND/OR operators',
  
  syntax: {
    /**
     * Highlights the query syntax
     * @param query The query string to highlight
     * @returns Highlighted HTML string
     */
    highlight: (query: string): string => {
      // Simple syntax highlighting
      return query
        // Highlight field names (before colon)
        .replace(/(\w+):/g, '<span class="field">$1</span>:')
        // Highlight operators
        .replace(/\b(AND|OR)\b/g, '<span class="operator">$1</span>')
        // Highlight quoted strings
        .replace(/"([^"]*)"/g, '"<span class="string">$1</span>"')
        // Highlight parentheses
        .replace(/(\(|\))/g, '<span class="paren">$1</span>');
    },
    
    /**
     * Validates the query syntax
     * @param query The query string to validate
     * @returns Validation result with valid flag and optional error message
     */
    validate: (query: string): { valid: boolean; error?: string } => {
      if (!query || query.trim() === '') {
        return { valid: true }; // Empty queries are valid (match all)
      }
      
      // Check for unbalanced quotes
      const quoteCount = (query.match(/"/g) || []).length;
      if (quoteCount % 2 !== 0) {
        return { valid: false, error: 'Unbalanced quotes in query' };
      }
      
      // Check for unbalanced parentheses
      const openParenCount = (query.match(/\(/g) || []).length;
      const closeParenCount = (query.match(/\)/g) || []).length;
      if (openParenCount !== closeParenCount) {
        return { valid: false, error: 'Unbalanced parentheses in query' };
      }
      
      // Check for invalid operators
      const operatorRegex = /\b(?:AND|OR)\b/g;
      let match;
      let lastIndex = 0;
      
      while ((match = operatorRegex.exec(query)) !== null) {
        // Check if operator is at the beginning
        if (match.index === 0) {
          return { valid: false, error: `Query cannot start with operator '${match[0]}'` };
        }
        
        // Check if operator is at the end
        if (match.index + match[0].length === query.length) {
          return { valid: false, error: `Query cannot end with operator '${match[0]}'` };
        }
        
        // Check for consecutive operators
        const textBetween = query.substring(lastIndex, match.index).trim();
        if (textBetween.match(/\b(?:AND|OR)\b$/)) {
          return { valid: false, error: 'Cannot have consecutive operators' };
        }
        
        lastIndex = match.index + match[0].length;
      }
      
      return { valid: true };
    }
  },
  
  autocomplete: {
    /**
     * Gets autocompletion suggestions for the query
     * @param query The current query string
     * @param position The cursor position in the query
     * @returns Promise resolving to an array of completion suggestions
     */
    getCompletions: async (query: string, position: number): Promise<Completion[]> => {
      // Get the current token being typed
      const beforeCursor = query.substring(0, position);
      const afterCursor = query.substring(position);
      
      // Check if we're in the middle of a word
      const wordRegex = /[\w.]*$/;
      const match = beforeCursor.match(wordRegex);
      
      if (!match) {
        return [];
      }
      
      const currentWord = match[0];
      
      // If we're at the beginning of a query or after an operator, suggest fields
      if (beforeCursor.trim() === currentWord || 
          beforeCursor.trim().endsWith('AND ') || 
          beforeCursor.trim().endsWith('OR ') ||
          beforeCursor.trim().endsWith('(')) {
        
        // Sample fields for demonstration
        const fields = [
          'timestamp', 'message', 'level', 'host', 'source', 'user', 'status', 'method', 'path'
        ];
        
        return fields
          .filter(field => field.startsWith(currentWord))
          .map(field => ({
            value: field + ':',
            score: 1,
            meta: 'field'
          }));
      }
      
      // If we're after a colon, suggest values
      if (beforeCursor.includes(':') && !afterCursor.includes(' ')) {
        const fieldMatch = beforeCursor.match(/(\w+):[^:]*$/);
        if (fieldMatch) {
          const field = fieldMatch[1];
          
          // Sample values based on field type (for demonstration)
          let values: string[] = [];
          
          switch (field) {
            case 'level':
              values = ['info', 'warn', 'error', 'debug'];
              break;
            case 'method':
              values = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
              break;
            case 'status':
              values = ['200', '201', '400', '401', '403', '404', '500'];
              break;
            default:
              return []; // No suggestions for other fields
          }
          
          const valuePrefix = beforeCursor.substring(beforeCursor.lastIndexOf(':') + 1);
          
          return values
            .filter(value => value.startsWith(valuePrefix))
            .map(value => ({
              value,
              score: 1,
              meta: 'value'
            }));
        }
      }
      
      // If we're at a space, suggest operators
      if (beforeCursor.trim() !== '' && beforeCursor.endsWith(' ')) {
        return [
          { value: 'AND', score: 1, meta: 'operator' },
          { value: 'OR', score: 0.9, meta: 'operator' }
        ];
      }
      
      return [];
    }
  }
};