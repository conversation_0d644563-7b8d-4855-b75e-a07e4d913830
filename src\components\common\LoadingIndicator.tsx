import React, { useState, useEffect } from 'react';

/**
 * Loading indicator component.
 * 
 * This component displays a loading animation while content is being loaded.
 * It uses a pulsing effect with the application's primary color.
 */
const LoadingIndicator: React.FC = () => {
  const [dots, setDots] = useState(0);
  
  // Animate the dots
  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => (prev + 1) % 4);
    }, 300);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      width: '100vw',
      background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
    }}>
      {/* Spinner */}
      <div style={{
        position: 'relative',
        width: '60px',
        height: '60px',
        marginBottom: '20px',
      }}>
        {/* Outer circle */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          border: '3px solid rgba(0, 229, 255, 0.1)',
          borderTopColor: '#00e5ff',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
        }} />
        
        {/* Inner circle */}
        <div style={{
          position: 'absolute',
          top: '15px',
          left: '15px',
          width: '30px',
          height: '30px',
          border: '3px solid rgba(0, 229, 255, 0.1)',
          borderTopColor: '#00e5ff',
          borderRadius: '50%',
          animation: 'spin 0.8s linear infinite reverse',
        }} />
        
        {/* Center dot */}
        <div style={{
          position: 'absolute',
          top: '27px',
          left: '27px',
          width: '6px',
          height: '6px',
          backgroundColor: '#00e5ff',
          borderRadius: '50%',
          boxShadow: '0 0 10px rgba(0, 229, 255, 0.8)',
          animation: 'pulse 1.5s ease-in-out infinite',
        }} />
      </div>
      
      {/* Loading text */}
      <div style={{
        fontSize: '18px',
        fontWeight: 500,
        color: '#00e5ff',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Loading{'.'.repeat(dots)}
      </div>
      
      {/* CSS animations */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          
          @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.5); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingIndicator;