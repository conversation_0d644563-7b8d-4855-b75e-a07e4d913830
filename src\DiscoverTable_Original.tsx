import React, { useState, useRef, useEffect, useCallback, useMemo, KeyboardEvent } from 'react';
import { useDiscoverLogs, useDiscoverFields } from '../../hooks';
import { LogEntry } from '../../types/discover';
import PaginationControls from './PaginationControls';

// ============================================================================
// CONSOLIDATED TABLE COMPONENTS
// ============================================================================

/**
 * Props for the TableColumnResizer component
 */
interface TableColumnResizerProps {
  index: number;
  onResize: (index: number, width: number) => void;
}

/**
 * Component for resizing table columns
 */
const TableColumnResizer: React.FC<TableColumnResizerProps> = ({ index, onResize }) => {
  const [isDragging, setIsDragging] = useState(false);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);
  const columnRef = useRef<HTMLElement | null>(null);

  // Handle mouse down to start resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Find the parent column element
    const column = e.currentTarget.parentElement as HTMLElement;
    columnRef.current = column;

    // Store initial values
    startXRef.current = e.clientX;
    startWidthRef.current = column.offsetWidth;

    // Start dragging
    setIsDragging(true);

    // Add event listeners for dragging
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Handle mouse move during resizing
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !columnRef.current) return;

    // Calculate new width
    const deltaX = e.clientX - startXRef.current;
    const newWidth = Math.max(50, startWidthRef.current + deltaX);

    // Apply new width
    columnRef.current.style.width = `${newWidth}px`;
    columnRef.current.style.minWidth = `${newWidth}px`;
    columnRef.current.style.maxWidth = `${newWidth}px`;
    columnRef.current.style.flexGrow = '0';

    // Notify parent component
    onResize(index, newWidth);
  };

  // Handle mouse up to stop resizing
  const handleMouseUp = () => {
    setIsDragging(false);

    // Remove event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, []);

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: '8px',
        height: '100%',
        cursor: 'col-resize',
        zIndex: 1,
      }}
      onMouseDown={handleMouseDown}
    />
  );
};

/**
 * Props for the PageSizeSelector component
 */
interface PageSizeSelectorProps {
  /**
   * Current page size
   */
  pageSize: number;

  /**
   * Function to change the page size
   */
  onPageSizeChange: (size: number) => void;

  /**
   * Available page size options
   */
  options?: number[];

  /**
   * Whether there are no results to display
   */
  noResults?: boolean;
}

/**
 * Component for selecting the number of items to display per page
 */
const PageSizeSelector: React.FC<PageSizeSelectorProps> = ({
  pageSize,
  onPageSizeChange,
  options = [10, 25, 50, 100],
  noResults = false,
}) => {
  // State for hover effect
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Handle change event from the select element
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = parseInt(e.target.value, 10);
    if (!isNaN(newSize) && newSize > 0) {
      onPageSizeChange(newSize);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.currentTarget.click();
    }
  };

  // Container styles
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '8px 16px',
    backgroundColor: 'rgba(10, 14, 23, 0.6)',
    borderRadius: '4px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
  };

  // Label styles
  const labelStyle: React.CSSProperties = {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: '14px',
    fontFamily: 'monospace',
    userSelect: 'none',
  };

  // Select styles
  const selectStyle: React.CSSProperties = {
    padding: '6px 28px 6px 10px',
    borderRadius: '4px',
    border: `1px solid ${isHovered || isFocused ? 'rgba(0, 229, 255, 0.5)' : 'rgba(0, 229, 255, 0.3)'}`,
    background: 'rgba(10, 14, 23, 0.8)',
    color: 'rgba(0, 229, 255, 0.8)',
    fontSize: '14px',
    fontFamily: 'monospace',
    cursor: 'pointer',
    outline: 'none',
    appearance: 'none',
    WebkitAppearance: 'none',
    MozAppearance: 'none',
    backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2300e5ff%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E")',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'right 8px center',
    backgroundSize: '10px auto',
    transition: 'all 0.2s ease',
    boxShadow: isFocused ? '0 0 0 2px rgba(0, 229, 255, 0.2)' : 'none',
  };

  // Option styles for the dropdown
  const getOptionStyle = (isSelected: boolean): React.CSSProperties => ({
    padding: '6px 10px',
    backgroundColor: isSelected ? 'rgba(0, 229, 255, 0.1)' : 'rgba(10, 14, 23, 0.95)',
    color: isSelected ? 'rgba(0, 229, 255, 0.9)' : 'rgba(255, 255, 255, 0.8)',
  });

  return (
    <div style={containerStyle} role="group" aria-label="Page size selector">
      <label
        htmlFor="page-size-select"
        style={labelStyle}
      >
        Show
      </label>
      <select
        id="page-size-select"
        value={pageSize}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        style={{
          ...selectStyle,
          opacity: noResults ? 0.5 : 1,
          cursor: noResults ? 'not-allowed' : 'pointer',
        }}
        aria-label="Items per page"
        disabled={noResults}
      >
        {options.map(option => (
          <option
            key={option}
            value={option}
            style={getOptionStyle(option === pageSize)}
          >
            {option}
          </option>
        ))}
      </select>
      <span style={labelStyle}>
        entries
      </span>
    </div>
  );
};

/**
 * Props for the LogEntryRow component
 */
interface LogEntryRowProps {
  log: LogEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
  selectedFields: string[];
  columnWidths?: Record<string, number>;
}

/**
 * Component for displaying a single log entry row in the table
 */
const LogEntryRow: React.FC<LogEntryRowProps> = ({
  log,
  isExpanded,
  onToggleExpand,
  selectedFields,
  columnWidths = {}
}) => {
  const { getFieldValue, formatFieldValue, getLogLevelColor } = useDiscoverLogs();

  return (
    <div
      role="row"
      aria-expanded={isExpanded}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onToggleExpand();
        }
      }}
      style={{
        display: 'flex',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        padding: '8px 16px',
        color: 'white',
        fontSize: '14px',
        cursor: 'pointer',
        background: isExpanded
          ? 'rgba(0, 229, 255, 0.05)'
          : 'transparent',
        transition: 'background-color 0.2s',
        alignItems: 'center',
        outline: 'none',
      }}
      onClick={onToggleExpand}
    >
      <div style={{ width: '30px', display: 'flex', alignItems: 'center' }}>
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#00e5ff"
          strokeWidth="2"
          style={{
            transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
            transition: 'transform 0.2s',
          }}
        >
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </div>

      {selectedFields.map(field => {
        const value = getFieldValue(log, field);

        // Special styling for level field
        if (field === 'level') {
          const levelColor = getLogLevelColor(value as any);
          return (
            <div
              key={field}
              style={{
                flexGrow: 1,
                padding: '0 8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                color: levelColor,
              }}
            >
              {formatFieldValue(value)}
            </div>
          );
        }

        return (
          <div
            key={field}
            style={{
              flexGrow: columnWidths[field] ? 0 : (field === 'message' ? 3 : 1),
              padding: '0 8px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
            }}
          >
            {formatFieldValue(value)}
          </div>
        );
      })}
    </div>
  );
};

/**
 * Props for the LogEntryDetails component
 */
interface LogEntryDetailsProps {
  log: LogEntry;
  onClose?: () => void;
}

/**
 * Component for displaying detailed information about a log entry
 */
const LogEntryDetails: React.FC<LogEntryDetailsProps> = ({ log, onClose }) => {
  const { formatFieldValue } = useDiscoverLogs();
  const [activeTab, setActiveTab] = useState<'json' | 'table'>('table');

  // Format JSON for display
  const formattedJson = JSON.stringify(log, null, 2);

  // Get all fields from the log entry
  const getLogFields = () => {
    const fields: Array<{ key: string; value: any }> = [];

    // Add base fields
    fields.push({ key: 'timestamp', value: log.timestamp });
    fields.push({ key: 'level', value: log.level });
    fields.push({ key: 'source', value: log.source });
    fields.push({ key: 'message', value: log.message });
    fields.push({ key: 'location', value: log.location });

    // Add agent fields
    fields.push({ key: 'agent.id', value: log.agent.id });
    fields.push({ key: 'agent.name', value: log.agent.name });
    fields.push({ key: 'agent.ip', value: log.agent.ip });

    // Add rule fields
    fields.push({ key: 'rule.id', value: log.rule.id });
    fields.push({ key: 'rule.description', value: log.rule.description });
    fields.push({ key: 'rule.level', value: log.rule.level });
    fields.push({ key: 'rule.groups', value: log.rule.groups });

    // Add decoder fields
    fields.push({ key: 'decoder.name', value: log.decoder.name });

    // Add data fields
    Object.entries(log.data).forEach(([key, value]) => {
      fields.push({ key: `data.${key}`, value });
    });

    return fields;
  };

  const fields = getLogFields();

  return (
    <div
      role="region"
      aria-label="Log entry details"
      style={{
        padding: '16px',
        background: 'rgba(0, 0, 0, 0.2)',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        color: 'white',
        fontSize: '14px',
      }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px',
      }}>
        <h4 style={{ margin: 0 }}>Log Details</h4>

        <div style={{ display: 'flex', gap: '16px', alignItems: 'center' }}>
          {/* Tab navigation */}
          <div role="tablist" style={{ display: 'flex', gap: '8px' }}>
            <button
              role="tab"
              aria-selected={activeTab === 'table'}
              aria-controls="table-panel"
              id="table-tab"
              onClick={() => setActiveTab('table')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'table' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'table' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              Table View
            </button>
            <button
              role="tab"
              aria-selected={activeTab === 'json'}
              aria-controls="json-panel"
              id="json-tab"
              onClick={() => setActiveTab('json')}
              style={{
                background: 'transparent',
                border: 'none',
                color: activeTab === 'json' ? '#00e5ff' : 'rgba(255, 255, 255, 0.5)',
                cursor: 'pointer',
                padding: '4px 8px',
                fontSize: '12px',
                borderBottom: activeTab === 'json' ? '2px solid #00e5ff' : '2px solid transparent',
                outline: 'none',
              }}
            >
              JSON View
            </button>
          </div>

          {/* Close button */}
          {onClose && (
            <button
              onClick={onClose}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'rgba(255, 255, 255, 0.7)',
                cursor: 'pointer',
                padding: '4px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M18 6L6 18M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Table view */}
      <div
        role="tabpanel"
        id="table-panel"
        aria-labelledby="table-tab"
        hidden={activeTab !== 'table'}
        style={{
          display: activeTab === 'table' ? 'grid' : 'none',
          gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
          gap: '8px',
          maxHeight: '400px',
          overflowY: 'auto',
        }}
      >
        {fields.map(({ key, value }) => (
          <React.Fragment key={key}>
            <div style={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontWeight: 'bold',
              padding: '4px 8px',
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
            }}>
              {key}
            </div>
            <div style={{
              padding: '4px 8px',
              wordBreak: 'break-word',
            }}>
              {formatFieldValue(value)}
            </div>
          </React.Fragment>
        ))}
      </div>

      {/* JSON view */}
      <pre
        role="tabpanel"
        id="json-panel"
        aria-labelledby="json-tab"
        hidden={activeTab !== 'json'}
        style={{
          display: activeTab === 'json' ? 'block' : 'none',
          background: 'rgba(0, 0, 0, 0.3)',
          padding: '12px',
          borderRadius: '4px',
          overflowX: 'auto',
          maxHeight: '400px',
          fontSize: '12px',
          fontFamily: 'monospace',
          whiteSpace: 'pre-wrap',
        }}
      >
        {formattedJson}
      </pre>
    </div>
  );
};

// ============================================================================
// MAIN DISCOVER TABLE COMPONENT
// ============================================================================

/**
 * Table component for the Discover page
 * Displays log entries with expandable details and virtual scrolling
 */
const DiscoverTable: React.FC = () => {
  const { logs, allLogs, selectedFields, toggleLogExpansion, isLogExpanded, getFieldValue, pagination } = useDiscoverLogs();
  const { toggleField } = useDiscoverFields();
  
  // Refs for virtual scrolling
  const tableRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  
  // State for virtual scrolling and column management
  const [visibleStartIndex, setVisibleStartIndex] = useState(0);
  const [visibleEndIndex, setVisibleEndIndex] = useState(50);
  const [scrollTop, setScrollTop] = useState(0);
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  
  // Constants for virtual scrolling
  const rowHeight = 40; // Approximate height of each row
  const bufferSize = 10; // Number of extra rows to render above and below visible area
  
  // Calculate visible rows based on scroll position
  const calculateVisibleRows = useCallback(() => {
    if (!tableRef.current) return;
    
    const containerHeight = tableRef.current.clientHeight;
    const scrollPosition = tableRef.current.scrollTop;
    
    const startIndex = Math.max(0, Math.floor(scrollPosition / rowHeight) - bufferSize);
    const endIndex = Math.min(
      logs.length - 1,
      Math.ceil((scrollPosition + containerHeight) / rowHeight) + bufferSize
    );
    
    setVisibleStartIndex(startIndex);
    setVisibleEndIndex(endIndex);
    setScrollTop(scrollPosition);
  }, [logs.length, rowHeight, bufferSize]);
  
  // Handle scroll event
  const handleScroll = useCallback(() => {
    requestAnimationFrame(calculateVisibleRows);
  }, [calculateVisibleRows]);
  
  // Initialize column widths
  const initializeColumnWidths = useCallback(() => {
    if (!headerRef.current) return;
    
    const headerCells = headerRef.current.querySelectorAll('[data-field]');
    const newWidths: Record<string, number> = {};
    
    headerCells.forEach((cell) => {
      const field = cell.getAttribute('data-field');
      if (field) {
        newWidths[field] = cell.clientWidth;
      }
    });
    
    setColumnWidths(newWidths);
  }, []);
  
  // Initialize virtual scrolling
  useEffect(() => {
    calculateVisibleRows();
    initializeColumnWidths();
    
    window.addEventListener('resize', initializeColumnWidths);
    return () => {
      window.removeEventListener('resize', initializeColumnWidths);
    };
  }, [calculateVisibleRows, initializeColumnWidths]);
  
  // Update visible rows when logs change
  useEffect(() => {
    calculateVisibleRows();
  }, [logs, calculateVisibleRows]);
  
  // Sort logs if needed
  const sortedLogs = useMemo(() => {
    if (!sortField) return logs;
    
    return [...logs].sort((a, b) => {
      const aValue = getFieldValue(a, sortField);
      const bValue = getFieldValue(b, sortField);
      
      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return sortDirection === 'asc' ? -1 : 1;
      if (bValue === undefined) return sortDirection === 'asc' ? 1 : -1;
      
      // Compare values based on type
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc' 
          ? aValue.localeCompare(bValue) 
          : bValue.localeCompare(aValue);
      }
      
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' 
          ? aValue - bValue 
          : bValue - aValue;
      }
      
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortDirection === 'asc' 
          ? aValue.getTime() - bValue.getTime() 
          : bValue.getTime() - aValue.getTime();
      }
      
      // Default string comparison
      const aStr = String(aValue);
      const bStr = String(bValue);
      return sortDirection === 'asc' 
        ? aStr.localeCompare(bStr) 
        : bStr.localeCompare(aStr);
    });
  }, [logs, sortField, sortDirection]);
  
  // Get visible logs
  const visibleLogs = sortedLogs.slice(visibleStartIndex, visibleEndIndex + 1);
  
  // Calculate total height of all rows
  const totalHeight = logs.length * rowHeight;
  
  // Calculate padding to maintain scroll position
  const topPadding = visibleStartIndex * rowHeight;
  const bottomPadding = Math.max(0, totalHeight - topPadding - visibleLogs.length * rowHeight);
  
  // Handle column click for sorting
  const handleColumnClick = (field: string) => {
    if (sortField === field) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new sort field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
  };
  
  // Handle column resize
  const handleColumnResize = (field: string, width: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [field]: width
    }));
  };
  
  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {/* Scrollable table area (header + body) */}
      <div style={{
        flex: 1,
        minHeight: 0,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}>
        {/* Table header */}
        <div 
          ref={headerRef}
          role="row"
          aria-rowindex={1}
          style={{
            display: 'flex',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
            background: 'rgba(10, 14, 23, 0.8)',
            padding: '8px 16px',
            color: 'white',
            fontWeight: 'bold',
            fontSize: '14px',
            position: 'sticky',
            top: 0,
            zIndex: 10,
          }}
        >
          <div style={{ width: '30px' }} role="columnheader" aria-hidden="true"></div>
          {selectedFields.map((field, index) => (
            <div 
              key={field}
              data-field={field}
              role="columnheader"
              aria-sort={sortField === field ? (sortDirection === 'asc' ? 'ascending' : 'descending') : 'none'}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleColumnClick(field);
                }
              }}
              style={{ 
                flexGrow: field === 'message' ? 3 : 1,
                padding: '0 8px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                cursor: 'pointer',
                userSelect: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                position: 'relative',
                width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
                maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
              }}
              onClick={() => handleColumnClick(field)}
            >
              <span>{field}</span>
              <span 
                aria-hidden="true"
                style={{ 
                  color: 'rgba(255, 255, 255, 0.5)', 
                  fontSize: '12px', 
                  marginLeft: '4px',
                  transform: sortField === field && sortDirection === 'asc' ? 'rotate(180deg)' : 'none',
                  opacity: sortField === field ? 1 : 0.5,
                }}
              >
                ↓
              </span>
              <TableColumnResizer 
                index={index} 
                onResize={(_, width) => handleColumnResize(field, width)} 
              />
            </div>
          ))}
        </div>
        {/* Table body with virtual scrolling */}
        <div 
          ref={tableRef}
          style={{
            flex: 1,
            minHeight: 0,
            overflowY: 'auto',
            position: 'relative',
          }}
          onScroll={handleScroll}
        >
          {logs.length === 0 ? (
            <div style={{
              padding: '24px',
              textAlign: 'center',
              color: 'rgba(255, 255, 255, 0.7)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px',
              gap: '16px',
            }}>
              <div style={{
                fontSize: '18px',
                fontWeight: 'bold',
              }}>
                No logs found
              </div>
              <div style={{
                fontSize: '14px',
                maxWidth: '500px',
                lineHeight: '1.5',
              }}>
                No logs match your current filters. Try adjusting your search criteria or time range.
              </div>
            </div>
          ) : (
            <div style={{ height: totalHeight, position: 'relative' }}>
              <div style={{ paddingTop: topPadding, paddingBottom: bottomPadding }}>
                {visibleLogs.map(log => (
                  <React.Fragment key={log.id}>
                    {/* Log row */}
                    <LogEntryRow 
                      log={log}
                      isExpanded={isLogExpanded(log.id)}
                      onToggleExpand={() => toggleLogExpansion(log.id)}
                      selectedFields={selectedFields}
                      columnWidths={columnWidths}
                    />
                    {/* Expanded log details */}
                    {isLogExpanded(log.id) && (
                      <LogEntryDetails 
                        log={log} 
                        onClose={() => toggleLogExpansion(log.id)}
                      />
                    )}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Pagination controls (always visible at bottom) */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px 0',
        background: 'rgba(10, 14, 23, 0.8)',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
      }}>
        <PaginationControls
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.setCurrentPage}
          onFirstPage={pagination.goToFirstPage}
          onPreviousPage={pagination.goToPreviousPage}
          onNextPage={pagination.goToNextPage}
          onLastPage={pagination.goToLastPage}
          isFirstPage={pagination.isFirstPage}
          isLastPage={pagination.isLastPage}
          isLoading={pagination.isLoading}
          noResults={pagination.noResults}
        />
      </div>
      {/* Status bar (always visible at very bottom) */}
      <div style={{
        padding: '8px 16px',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        background: 'rgba(10, 14, 23, 0.8)',
        color: 'rgba(255, 255, 255, 0.7)',
        fontSize: '12px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <div>
          {pagination.noResults ? (
            <span style={{ color: 'rgba(255, 255, 255, 0.5)', fontStyle: 'italic' }}>
              No logs match your current filters
            </span>
          ) : (
            `${allLogs.length} logs found`
          )}
        </div>
        <PageSizeSelector
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.setPageSize}
          options={[10, 25, 50, 100]}
          noResults={pagination.noResults}
        />
        <div>
          {pagination.showingText}
        </div>
      </div>
    </div>
  );
};

export default DiscoverTable;