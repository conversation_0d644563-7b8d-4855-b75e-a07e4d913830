import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '../router/constants';

/**
 * Dashboard page component.
 * 
 * This component displays the main security dashboard with security score,
 * system status, and other security-related information.
 */
const Dashboard: React.FC = () => {
  const [securityScore] = useState(19);
  const [scanProgress, setScanProgress] = useState(0);
  const [scanComplete, setScanComplete] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isQuickAccessCollapsed, setIsQuickAccessCollapsed] = useState(false);
  const navigate = useNavigate();

  // Explore section cards
  const exploreCards = [
    {
      id: 'discover',
      title: 'Discover',
      description: 'Search and analyze your security data',
      route: ROUTES.EXPLORE_DISCOVER,
      color: '#00e5ff'
    },
    {
      id: 'dashboards',
      title: 'Dashboards',
      description: 'View and manage security dashboards',
      route: ROUTES.EXPLORE_DASHBOARDS,
      color: '#9966ff'
    },
    {
      id: 'visualize',
      title: 'Visualize',
      description: 'Create custom visualizations',
      route: ROUTES.EXPLORE_VISUALIZE,
      color: '#ff9966'
    },
    {
      id: 'reporting',
      title: 'Reporting',
      description: 'Generate and schedule reports',
      route: ROUTES.EXPLORE_REPORTING,
      color: '#66cc99'
    },
    {
      id: 'alerting',
      title: 'Alerting',
      description: 'Manage security alerts and notifications',
      route: ROUTES.EXPLORE_ALERTING,
      color: '#ff6666'
    },
    {
      id: 'maps',
      title: 'Maps',
      description: 'Visualize data on interactive maps',
      route: ROUTES.EXPLORE_MAPS,
      color: '#ffcc66'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      description: 'View and manage system notifications',
      route: ROUTES.EXPLORE_NOTIFICATIONS,
      color: '#66aaff'
    }
  ];

  // Simulate security scan progress
  useEffect(() => {
    if (scanProgress < 100 && !scanComplete) {
      const timer = setTimeout(() => {
        setScanProgress(prev => {
          const newProgress = prev + 1;
          if (newProgress >= 100) {
            setScanComplete(true);
          }
          return newProgress;
        });
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [scanProgress, scanComplete]);

  // Update current time
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Format time for display
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit',
      hour12: false 
    });
  };

  const ClockIcon = () => (
    <svg
      viewBox="0 0 24 24"
      width="22"
      height="22"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  );

  // Card component for dashboard
  const DashboardCard = ({ 
    title, 
    children, 
    height = 'auto', 
    glowColor = 'rgba(0, 229, 255, 0.5)',
    glowIntensity = 0.1
  }: { 
    title: string; 
    children: React.ReactNode; 
    height?: string | number;
    glowColor?: string;
    glowIntensity?: number;
  }) => (
    <div style={{
      position: 'relative',
      width: '100%',
      height,
      borderRadius: '12px',
      padding: '16px',
      boxSizing: 'border-box',
      background: 'rgba(16, 24, 45, 0.7)',
      backdropFilter: 'blur(10px)',
      WebkitBackdropFilter: 'blur(10px)',
      border: '1px solid rgba(0, 229, 255, 0.2)',
      boxShadow: `0 0 20px ${glowColor.replace('0.5', String(glowIntensity))}`,
      overflow: 'hidden',
      marginBottom: '20px',
    }}>
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        background: `linear-gradient(90deg, transparent, ${glowColor}, transparent)`,
        opacity: 0.8,
      }} />
      <h2 style={{ 
        fontSize: '16px', 
        fontWeight: 500, 
        marginTop: '4px',
        marginBottom: '16px',
        color: 'rgba(255, 255, 255, 0.9)',
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
      }}>
        {title}
      </h2>
      {children}
    </div>
  );

  // Threat data for visualization
  const threatData = [
    { type: 'Malware', count: 3, color: 'rgba(255, 99, 132, 0.8)' },
    { type: 'Phishing', count: 7, color: 'rgba(255, 159, 64, 0.8)' },
    { type: 'Unauthorized Access', count: 2, color: 'rgba(255, 205, 86, 0.8)' },
    { type: 'Data Breach', count: 0, color: 'rgba(75, 192, 192, 0.8)' },
    { type: 'DDoS', count: 1, color: 'rgba(153, 102, 255, 0.8)' }
  ];

  // Protected assets data
  const protectedAssets = [
    { name: 'Web Servers', count: 12, protected: 12 },
    { name: 'Databases', count: 8, protected: 7 },
    { name: 'User Endpoints', count: 156, protected: 143 },
    { name: 'Cloud Services', count: 23, protected: 23 },
    { name: 'IoT Devices', count: 42, protected: 38 }
  ];

  return (
    <>
      {/* Header with time and welcome message */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
        position: 'relative',
        zIndex: 1,
      }}>
        <div>
          <h1 style={{ 
            fontSize: '28px', 
            fontWeight: 600, 
            marginBottom: '8px',
            background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
          }}>
            Security Dashboard
          </h1>
          <p style={{ 
            fontSize: '14px', 
            color: 'rgba(255, 255, 255, 0.7)',
            margin: 0,
          }}>
            Welcome back, Security Admin
          </p>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '16px',
        }}>
          <div style={{
            padding: '8px 16px',
            borderRadius: '8px',
            background: 'rgba(0, 229, 255, 0.1)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            fontSize: '14px',
            color: '#00e5ff',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <ClockIcon />
            {formatTime(currentTime)}
          </div>
        </div>
      </div>

      {/* Quick Access Section */}
      <div style={{ marginBottom: '24px', position: 'relative', zIndex: 1 }}>
        <div 
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: '12px 16px',
            background: 'rgba(16, 24, 45, 0.7)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            borderRadius: '12px',
            cursor: 'pointer',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          }}
          onClick={() => setIsQuickAccessCollapsed(!isQuickAccessCollapsed)}
        >
          <h2 style={{
            fontSize: '18px',
            fontWeight: 500,
            color: 'rgba(255, 255, 255, 0.9)',
            margin: 0,
          }}>
            Quick Access
          </h2>
          <svg 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            style={{ transform: isQuickAccessCollapsed ? 'rotate(0deg)' : 'rotate(180deg)', transition: 'transform 0.3s ease' }}
          >
            <polyline points="6 9 12 15 18 9"></polyline>
          </svg>
        </div>
        {!isQuickAccessCollapsed && (
          <div style={{
            padding: '24px 16px 0',
            background: 'rgba(16, 24, 45, 0.5)',
            border: '1px solid rgba(0, 229, 255, 0.1)',
            borderTop: 'none',
            borderBottomLeftRadius: '12px',
            borderBottomRightRadius: '12px',
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
            gap: '24px',
          }}>
            {exploreCards.map(card => (
              <div 
                key={card.id}
                onClick={() => navigate(card.route)}
                style={{
                  borderRadius: '12px',
                  padding: '24px',
                  background: 'rgba(16, 24, 45, 0.7)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: `1px solid ${card.color}40`,
                  boxShadow: `0 0 20px ${card.color}20`,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                {/* Decorative accent */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '4px',
                  background: `linear-gradient(90deg, transparent, ${card.color}, transparent)`,
                  opacity: 0.8,
                }} />
                
                <h2 style={{ 
                  fontSize: '20px', 
                  fontWeight: 500, 
                  marginBottom: '12px',
                  color: card.color,
                }}>
                  {card.title}
                </h2>
                
                <p style={{ 
                  fontSize: '14px', 
                  color: 'rgba(255, 255, 255, 0.7)',
                  marginBottom: '24px',
                }}>
                  {card.description}
                </p>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'flex-end',
                }}>
                  <button style={{
                    background: `${card.color}20`,
                    border: `1px solid ${card.color}60`,
                    borderRadius: '4px',
                    padding: '6px 12px',
                    color: card.color,
                    fontSize: '14px',
                    cursor: 'pointer',
                  }}>
                    Open
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Dashboard Grid Layout */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(3, 1fr)',
        gap: '20px',
        position: 'relative',
        zIndex: 1,
      }}>
        {/* Security Score Card */}
        <div style={{ gridColumn: '1 / 2', gridRow: '1 / 3' }}>
          <DashboardCard 
            title="SECURITY SCORE" 
            height={360}
            glowColor={securityScore < 30 ? 'rgba(0, 229, 255, 0.5)' : 'rgba(255, 99, 132, 0.5)'}
            glowIntensity={0.2}
          >
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 'calc(100% - 40px)',
            }}>
              <div style={{
                position: 'relative',
                width: '180px',
                height: '180px',
                marginBottom: '20px',
              }}>
                {/* Background circle */}
                <svg width="180" height="180" viewBox="0 0 180 180">
                  <circle
                    cx="90"
                    cy="90"
                    r="80"
                    fill="none"
                    stroke="rgba(255, 255, 255, 0.1)"
                    strokeWidth="12"
                  />
                  {/* Progress circle */}
                  <circle
                    cx="90"
                    cy="90"
                    r="80"
                    fill="none"
                    stroke="#00e5ff"
                    strokeWidth="12"
                    strokeDasharray={2 * Math.PI * 80}
                    strokeDashoffset={2 * Math.PI * 80 * (1 - securityScore / 100)}
                    strokeLinecap="round"
                    transform="rotate(-90 90 90)"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(0, 229, 255, 0.8))',
                    }}
                  />
                </svg>
                {/* Score text */}
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center',
                }}>
                  <div style={{
                    fontSize: '48px',
                    fontWeight: 'bold',
                    color: '#00e5ff',
                    textShadow: '0 0 10px rgba(0, 229, 255, 0.5)',
                    lineHeight: 1,
                  }}>
                    {securityScore}
                  </div>
                  <div style={{
                    fontSize: '16px',
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    / 100
                  </div>
                </div>
              </div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                borderRadius: '20px',
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.2)',
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  borderRadius: '50%',
                  backgroundColor: '#00e5ff',
                  boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                }} />
                <span style={{ fontSize: '14px', color: '#00e5ff' }}>
                  4% below industry average
                </span>
              </div>
            </div>
          </DashboardCard>
        </div>

        {/* System Status Card */}
        <div style={{ gridColumn: '2 / 4' }}>
          <DashboardCard title="SYSTEM STATUS">
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              height: '160px',
            }}>
              {/* Status indicators */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                width: '48%',
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>Firewall</div>
                    <div style={{ fontSize: '12px', color: '#00e5ff' }}>Active</div>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>IDS/IPS</div>
                    <div style={{ fontSize: '12px', color: '#00e5ff' }}>Active</div>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>VPN</div>
                    <div style={{ fontSize: '12px', color: '#00e5ff' }}>Active</div>
                  </div>
                </div>
              </div>
              
              {/* More status indicators */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                width: '48%',
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>Antivirus</div>
                    <div style={{ fontSize: '12px', color: '#00e5ff' }}>Active</div>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(255, 99, 132, 0.05)',
                  border: '1px solid rgba(255, 99, 132, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: 'rgba(255, 99, 132, 1)',
                    boxShadow: '0 0 8px rgba(255, 99, 132, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>EDR</div>
                    <div style={{ fontSize: '12px', color: 'rgba(255, 99, 132, 1)' }}>Update Required</div>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '12px',
                  borderRadius: '8px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    backgroundColor: '#00e5ff',
                    boxShadow: '0 0 8px rgba(0, 229, 255, 0.8)',
                  }} />
                  <div>
                    <div style={{ fontSize: '14px', color: 'white' }}>SIEM</div>
                    <div style={{ fontSize: '12px', color: '#00e5ff' }}>Active</div>
                  </div>
                </div>
              </div>
            </div>
          </DashboardCard>
        </div>

        {/* Recent Threats Card */}
        <div style={{ gridColumn: '2 / 3' }}>
          <DashboardCard title="RECENT THREATS">
            <div style={{
              height: '160px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}>
              {threatData.map((threat, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  padding: '8px 0',
                  borderBottom: index < threatData.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}>
                    <div style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      backgroundColor: threat.color,
                    }} />
                    <span style={{ fontSize: '14px', color: 'white' }}>{threat.type}</span>
                  </div>
                  <div style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: threat.count > 0 ? threat.color : 'rgba(255, 255, 255, 0.5)',
                  }}>
                    {threat.count}
                  </div>
                </div>
              ))}
            </div>
          </DashboardCard>
        </div>

        {/* Protected Assets Card */}
        <div style={{ gridColumn: '3 / 4' }}>
          <DashboardCard title="PROTECTED ASSETS">
            <div style={{
              height: '160px',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
            }}>
              {protectedAssets.slice(0, 3).map((asset, index) => (
                <div key={index} style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '4px',
                  padding: '8px 0',
                  borderBottom: index < 2 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}>
                    <span style={{ fontSize: '14px', color: 'white' }}>{asset.name}</span>
                    <span style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.7)' }}>
                      {asset.protected}/{asset.count}
                    </span>
                  </div>
                  <div style={{
                    width: '100%',
                    height: '4px',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '2px',
                    overflow: 'hidden',
                  }}>
                    <div style={{
                      width: `${(asset.protected / asset.count) * 100}%`,
                      height: '100%',
                      backgroundColor: '#00e5ff',
                      borderRadius: '2px',
                    }} />
                  </div>
                </div>
              ))}
            </div>
          </DashboardCard>
        </div>
      </div>
    </>
  );
};

export default Dashboard;
