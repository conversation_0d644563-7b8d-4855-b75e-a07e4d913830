import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { ExploreDropdownProps } from './types';
import { EXPLORE_ITEMS } from './config';
import ExploreSubItem from './ExploreSubItem';

const ExploreDropdown: React.FC<ExploreDropdownProps> = ({
  isExpanded,
  isCollapsed,
  activeItem,
  onItemClick
}) => {
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 60 });

  useEffect(() => {
    if (isCollapsed && isExpanded) {
      // Calculate position based on the explore button
      // Since we're in a centered navigation, we need to estimate the position
      const sidebarHeight = window.innerHeight;
      const navItemHeight = 40;
      const navItemsCount = 7; // Approximate number of nav items
      const centerOffset = (sidebarHeight / 2) - ((navItemsCount * navItemHeight) / 2);
      const exploreButtonIndex = 1; // Explore is the second item (0-indexed)
      const exploreButtonTop = centerOffset + (exploreButtonIndex * (navItemHeight + 15)); // 15px gap between items
      
      setDropdownPosition({
        top: exploreButtonTop,
        left: 80 // Sidebar width when collapsed
      });
    }
  }, [isCollapsed, isExpanded]);
  const getDropdownStyle = () => {
    const maxHeight = isExpanded ? `${EXPLORE_ITEMS.length * 40}px` : '0px';
    
    return {
      overflow: 'hidden',
      maxHeight,
      transition: 'max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      width: '100%',
      paddingLeft: isCollapsed ? '0' : '16px',
      marginBottom: isExpanded ? '8px' : '0',
    };
  };

  const getContainerStyle = () => ({
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '4px',
    paddingTop: isExpanded ? '8px' : '0',
    transition: 'padding-top 0.3s ease',
  });

  // If collapsed sidebar, show as overlay tooltip
  if (isCollapsed && isExpanded) {
    // Use createPortal to render outside the clipping context
    return createPortal(
      <div 
        style={{
          position: 'fixed',
          left: `${dropdownPosition.left}px`,
          top: `${dropdownPosition.top}px`,
          minWidth: '200px',
          background: 'rgba(16, 24, 45, 0.95)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          borderRadius: '8px',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.3)',
          padding: '8px',
          zIndex: 99999,
          pointerEvents: 'auto',
        }}
        role="menu"
        aria-label="Explore navigation menu"
      >
        <div style={getContainerStyle()}>
          {EXPLORE_ITEMS.map((item) => (
            <ExploreSubItem
              key={item.id}
              id={item.id}
              label={item.label}
              icon={item.icon}
              isActive={activeItem === item.id}
              isCollapsed={false} // Always show labels in overlay
              onClick={onItemClick}
            />
          ))}
        </div>
      </div>,
      document.body
    );
  }

  return (
    <div style={getDropdownStyle()}>
      <div style={getContainerStyle()}>
        {EXPLORE_ITEMS.map((item) => (
          <ExploreSubItem
            key={item.id}
            id={item.id}
            label={item.label}
            icon={item.icon}
            isActive={activeItem === item.id}
            isCollapsed={isCollapsed}
            onClick={onItemClick}
          />
        ))}
      </div>
    </div>
  );
};

export default ExploreDropdown;