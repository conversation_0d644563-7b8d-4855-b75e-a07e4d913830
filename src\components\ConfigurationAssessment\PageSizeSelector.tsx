import React, { useState } from 'react';

/**
 * Props for the PageSizeSelector component
 */
interface PageSizeSelectorProps {
  /**
   * Current page size
   */
  pageSize: number;
  
  /**
   * Function to change the page size
   */
  onPageSizeChange: (size: number) => void;
  
  /**
   * Available page size options
   */
  options?: number[];
  
  /**
   * Whether there are no results to display
   */
  noResults?: boolean;
}

/**
 * Component for selecting the number of items to display per page
 */
const PageSizeSelector: React.FC<PageSizeSelectorProps> = ({
  pageSize,
  onPageSizeChange,
  options = [10, 25, 50, 100],
  noResults = false,
}) => {
  // State for hover effect
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Handle change event from the select element
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = parseInt(e.target.value, 10);
    if (!isNaN(newSize) && newSize > 0) {
      onPageSizeChange(newSize);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.currentTarget.click();
    }
  };

  // Container styles
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
  };

  // Label styles
  const labelStyle: React.CSSProperties = {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: '14px',
    fontFamily: 'monospace',
    userSelect: 'none',
  };

  // Select styles
  const selectStyle: React.CSSProperties = {
    padding: '6px 28px 6px 10px',
    borderRadius: '4px',
    border: `1px solid ${isHovered || isFocused ? 'rgba(0, 229, 255, 0.5)' : 'rgba(0, 229, 255, 0.3)'}`,
    background: 'rgba(10, 14, 23, 0.8)',
    color: 'rgba(0, 229, 255, 0.8)',
    fontSize: '14px',
    fontFamily: 'monospace',
    cursor: 'pointer',
    outline: 'none',
    appearance: 'none',
    WebkitAppearance: 'none',
    MozAppearance: 'none',
    backgroundImage: 'url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2300e5ff%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E")',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: 'right 8px center',
    backgroundSize: '10px auto',
    transition: 'all 0.2s ease',
    boxShadow: isFocused ? '0 0 0 2px rgba(0, 229, 255, 0.2)' : 'none',
  };

  // Option styles for the dropdown
  const getOptionStyle = (isSelected: boolean): React.CSSProperties => ({
    padding: '6px 10px',
    backgroundColor: isSelected ? 'rgba(0, 229, 255, 0.1)' : 'rgba(10, 14, 23, 0.95)',
    color: isSelected ? 'rgba(0, 229, 255, 0.9)' : 'rgba(255, 255, 255, 0.8)',
  });

  if (noResults) {
    return null;
  }

  return (
    <div style={containerStyle} role="group" aria-label="Page size selector">
      <label
        htmlFor="page-size-select"
        style={labelStyle}
      >
        Show
      </label>
      <select
        id="page-size-select"
        value={pageSize}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        style={selectStyle}
        aria-label="Items per page"
      >
        {options.map(option => (
          <option 
            key={option} 
            value={option}
            style={getOptionStyle(option === pageSize)}
          >
            {option}
          </option>
        ))}
      </select>
      <span style={labelStyle}>
        entries
      </span>
    </div>
  );
};

export default PageSizeSelector;