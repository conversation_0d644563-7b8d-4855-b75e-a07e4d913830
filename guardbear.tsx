import React, { useState } from 'react';

const GuardBearAdminPanel: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  
  
  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div style={{ 
      height: '100vh', 
      width: '100vw',
      margin: 0,
      padding: 0,
      boxSizing: 'border-box',
      fontFamily: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
      background: 'linear-gradient(135deg, #0f172a, #1e293b)',
      overflow: 'hidden',
      display: 'flex'
    }}>
      {/* Sidebar Container */}
      <div className="sidebar-container" style={{ position: 'relative', height: '100vh' }}>
        {/* Sidebar Navigation */}
        <aside className={`sidebar ${collapsed ? 'collapsed' : ''}`} style={{
          height: '100vh',
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(10px)',
          borderRight: '1px solid rgba(255, 255, 255, 0.1)',
          width: collapsed ? '70px' : '240px',
          transition: 'width 0.2s ease',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <div className="logo-container" style={{
            display: 'flex',
            alignItems: 'center',
            padding: '1.5rem 1rem',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div className="logo" style={{
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            }}>
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" style={{
                minWidth: '40px',
                height: '40px',
                fill: '#38bdf8'
              }}>
                <path d="M12 2L4 6v12l8 4 8-4V6l-8-4zm0 2.6L17.4 8 12 11.4 6.6 8 12 4.6zm-6 4.9l6 3v7.8l-6-3V9.5zm12 0v7.8l-6 3v-7.8l6-3z"/>
              </svg>
              <span className="logo-text" style={{
                fontSize: '1.2rem',
                fontWeight: 'bold',
                color: 'white',
                whiteSpace: 'nowrap',
                transition: 'opacity 0.2s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden'
              }}>Guard Bear</span>
            </div>
          </div>
          
          <nav className="nav-items" style={{
            display: 'flex',
            flexDirection: 'column',
            padding: '1rem 0',
            flex: 1
          }}>
            <a href="#" className="nav-item active" style={{
              display: 'flex',
              alignItems: 'center',
              padding: collapsed ? '0.75rem' : '0.75rem 1rem',
              color: '#38bdf8',
              textDecoration: 'none',
              margin: '0.25rem 0.5rem',
              borderRadius: '0.5rem',
              background: 'rgba(56, 189, 248, 0.15)',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{
                minWidth: '24px',
                height: '24px',
                marginRight: collapsed ? 0 : '1rem'
              }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span style={{
                whiteSpace: 'nowrap',
                transition: 'opacity 0.1s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden',
                display: collapsed ? 'none' : 'inline'
              }}>Dashboard</span>
            </a>
            
            <a href="#" className="nav-item" style={{
              display: 'flex',
              alignItems: 'center',
              padding: collapsed ? '0.75rem' : '0.75rem 1rem',
              color: 'rgba(255, 255, 255, 0.7)',
              textDecoration: 'none',
              margin: '0.25rem 0.5rem',
              borderRadius: '0.5rem',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{
                minWidth: '24px',
                height: '24px',
                marginRight: collapsed ? 0 : '1rem'
              }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span style={{
                whiteSpace: 'nowrap',
                transition: 'opacity 0.1s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden',
                display: collapsed ? 'none' : 'inline'
              }}>Security</span>
            </a>
            
            <a href="#" className="nav-item" style={{
              display: 'flex',
              alignItems: 'center',
              padding: collapsed ? '0.75rem' : '0.75rem 1rem',
              color: 'rgba(255, 255, 255, 0.7)',
              textDecoration: 'none',
              margin: '0.25rem 0.5rem',
              borderRadius: '0.5rem',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{
                minWidth: '24px',
                height: '24px',
                marginRight: collapsed ? 0 : '1rem'
              }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span style={{
                whiteSpace: 'nowrap',
                transition: 'opacity 0.1s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden',
                display: collapsed ? 'none' : 'inline'
              }}>Logs</span>
            </a>
            
            <a href="#" className="nav-item" style={{
              display: 'flex',
              alignItems: 'center',
              padding: collapsed ? '0.75rem' : '0.75rem 1rem',
              color: 'rgba(255, 255, 255, 0.7)',
              textDecoration: 'none',
              margin: '0.25rem 0.5rem',
              borderRadius: '0.5rem',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{
                minWidth: '24px',
                height: '24px',
                marginRight: collapsed ? 0 : '1rem'
              }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              <span style={{
                whiteSpace: 'nowrap',
                transition: 'opacity 0.1s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden',
                display: collapsed ? 'none' : 'inline'
              }}>Users</span>
            </a>
            
            <a href="#" className="nav-item" style={{
              display: 'flex',
              alignItems: 'center',
              padding: collapsed ? '0.75rem' : '0.75rem 1rem',
              color: 'rgba(255, 255, 255, 0.7)',
              textDecoration: 'none',
              margin: '0.25rem 0.5rem',
              borderRadius: '0.5rem',
              justifyContent: collapsed ? 'center' : 'flex-start'
            }}>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" style={{
                minWidth: '24px',
                height: '24px',
                marginRight: collapsed ? 0 : '1rem'
              }}>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span style={{
                whiteSpace: 'nowrap',
                transition: 'opacity 0.1s ease',
                opacity: collapsed ? 0 : 1,
                width: collapsed ? 0 : 'auto',
                overflow: 'hidden',
                display: collapsed ? 'none' : 'inline'
              }}>Settings</span>
            </a>
          </nav>
          
          <div className="user-profile" style={{
            display: 'flex',
            alignItems: 'center',
            padding: '1rem',
            borderTop: '1px solid rgba(255, 255, 255, 0.1)',
            marginTop: 'auto',
            justifyContent: collapsed ? 'center' : 'flex-start'
          }}>
            <div className="avatar" style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              background: '#38bdf8',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontWeight: 'bold',
              marginRight: collapsed ? 0 : '0.75rem',
              flexShrink: 0
            }}>GB</div>
            <div className="user-info" style={{
              whiteSpace: 'nowrap',
              transition: 'opacity 0.2s ease, width 0.2s ease',
              opacity: collapsed ? 0 : 1,
              width: collapsed ? 0 : 'auto',
              overflow: 'hidden',
              display: collapsed ? 'none' : 'block'
            }}>
              <div className="username" style={{
                fontWeight: 500,
                color: 'white'
              }}>Admin User</div>
              <div className="role" style={{
                fontSize: '0.8rem',
                color: 'rgba(255, 255, 255, 0.6)'
              }}>Security Analyst</div>
            </div>
          </div>
        </aside>
        
        {/* Toggle Button positioned on the border */}
        <div 
          className="toggle-btn" 
          onClick={toggleSidebar}
          style={{
            position: 'absolute',
            top: '18px',
            right: '-12px',
            zIndex: 100,
            width: '24px',
            height: '24px',
            background: '#38bdf8',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            boxShadow: '0 0 10px rgba(0, 0, 0, 0.1)'
          }}
        >
          {collapsed ? (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ width: '14px', height: '14px' }}>
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ width: '14px', height: '14px' }}>
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          )}
        </div>
      </div>
      
      {/* Main Content */}
      <div className="content" style={{
        flex: 1,
        padding: '2rem',
        color: 'white',
        position: 'relative'
      }}>
        <h1 style={{ marginBottom: '1rem', fontSize: '2.5rem' }}>Guard Bear Admin Panel</h1>
        <p style={{ fontSize: '1.1rem', lineHeight: 1.6, maxWidth: '800px' }}>
          Welcome to the Guard Bear cybersecurity administration dashboard. Use the sidebar navigation to access different sections of the admin panel.
        </p>
      </div>
    </div>
  );
};

export default GuardBearAdminPanel;
