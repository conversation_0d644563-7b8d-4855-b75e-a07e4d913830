import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { DataPlugin } from '../data-plugin';

// Create a context for the Data Plugin
const DataPluginContext = createContext<DataPlugin | null>(null);

interface DataPluginProviderProps {
  children: ReactNode;
}

/**
 * Provider component for the Data Plugin context
 */
export const DataPluginProvider: React.FC<DataPluginProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    const initializeDataPlugin = async () => {
      try {
        const dataPlugin = DataPlugin.getInstance();
        await dataPlugin.initialize();
        setIsInitialized(true);
      } catch (err) {
        setError(err as Error);
        console.error('Failed to initialize Data Plugin:', err);
      }
    };
    
    initializeDataPlugin();
  }, []);
  
  if (error) {
    // You might want to render an error component here
    return (
      <div className="data-plugin-error">
        <h3>Error initializing Data Plugin</h3>
        <p>{error.message}</p>
      </div>
    );
  }
  
  if (!isInitialized) {
    // You might want to render a loading component here
    return <div className="data-plugin-loading">Loading Data Plugin...</div>;
  }
  
  return (
    <DataPluginContext.Provider value={DataPlugin.getInstance()}>
      {children}
    </DataPluginContext.Provider>
  );
};

/**
 * Hook to use the Data Plugin in React components
 * @returns The Data Plugin instance
 * @throws Error if used outside of a DataPluginProvider
 */
export function useDataPlugin(): DataPlugin {
  const dataPlugin = useContext(DataPluginContext);
  if (!dataPlugin) {
    throw new Error('useDataPlugin must be used within a DataPluginProvider');
  }
  return dataPlugin;
}