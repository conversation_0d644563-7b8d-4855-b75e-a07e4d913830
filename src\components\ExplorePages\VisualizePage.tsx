import React from 'react';

const VisualizePage: React.FC = () => {
  return (
    <div style={{
      padding: '24px',
      color: 'white',
      height: '100%',
      background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
    }}>
      <div style={{
        background: 'rgba(16, 24, 45, 0.7)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 229, 255, 0.2)',
        borderRadius: '12px',
        padding: '32px',
        textAlign: 'center',
        boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
      }}>
        <div style={{
          width: '64px',
          height: '64px',
          margin: '0 auto 24px',
          background: 'rgba(0, 229, 255, 0.1)',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          border: '2px solid rgba(0, 229, 255, 0.3)',
        }}>
          <svg viewBox="0 0 24 24" width="32" height="32" fill="none" stroke="#00e5ff" strokeWidth="2">
            <line x1="18" y1="20" x2="18" y2="10"/>
            <line x1="12" y1="20" x2="12" y2="4"/>
            <line x1="6" y1="20" x2="6" y2="14"/>
          </svg>
        </div>
        <h1 style={{
          fontSize: '32px',
          fontWeight: 600,
          marginBottom: '16px',
          background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}>
          Visualize
        </h1>
        <p style={{
          fontSize: '18px',
          color: 'rgba(255, 255, 255, 0.7)',
          marginBottom: '24px',
          maxWidth: '600px',
          margin: '0 auto',
        }}>
          Create powerful data visualizations and charts to analyze security trends and patterns.
        </p>
        <div style={{
          display: 'inline-block',
          padding: '12px 24px',
          background: 'rgba(0, 229, 255, 0.1)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '8px',
          color: '#00e5ff',
          fontSize: '14px',
        }}>
          Coming Soon - Visualization Builder
        </div>
      </div>
    </div>
  );
};

export default VisualizePage;