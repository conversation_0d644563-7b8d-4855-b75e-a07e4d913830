import { SearchService, QueryService, UiService, FieldFormatsService, AutocompleteService, DataStorage } from './interfaces';
import { SearchServiceImpl } from './services/search-service';
import { QueryServiceImpl } from './services/query-service';
import { UiServiceImpl } from './services/ui-service';
import { FieldFormatsServiceImpl } from './services/field-formats-service';
import { AutocompleteServiceImpl } from './services/autocomplete-service';
import { DataStorageImpl } from './services/data-storage';
import { IndexPatternService } from './services/index-pattern-service';

/**
 * The Data Plugin is the central component that provides core services for data management and search.
 * It follows the singleton pattern to ensure there's only one instance throughout the application.
 */
export class DataPlugin {
  private static instance: DataPlugin | null = null;
  
  private readonly searchService: SearchService;
  private readonly queryService: QueryService;
  private readonly uiService: UiService;
  private readonly fieldFormatsService: FieldFormatsService;
  private readonly autocompleteService: AutocompleteService;
  private readonly storage: DataStorage;
  private readonly indexPatternService: IndexPatternService;
  private initialized: boolean = false;

  /**
   * Private constructor to enforce singleton pattern
   */
  private constructor() {
    this.indexPatternService = new IndexPatternService();
    this.searchService = new SearchServiceImpl(this.indexPatternService);
    this.queryService = new QueryServiceImpl();
    this.uiService = new UiServiceImpl();
    this.fieldFormatsService = new FieldFormatsServiceImpl();
    this.autocompleteService = new AutocompleteServiceImpl();
    this.storage = new DataStorageImpl();
  }

  /**
   * Gets the singleton instance of the Data Plugin
   * @returns The Data Plugin instance
   */
  public static getInstance(): DataPlugin {
    if (!DataPlugin.instance) {
      DataPlugin.instance = new DataPlugin();
    }
    return DataPlugin.instance;
  }

  /**
   * Initializes the Data Plugin
   * @returns A promise that resolves when initialization is complete
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // Initialize services that require async initialization
      // For now, this is just a placeholder
      
      this.initialized = true;
      console.log('Data Plugin initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Data Plugin:', error);
      throw error;
    }
  }

  /**
   * Gets the search service
   * @returns The search service
   */
  public getSearchService(): SearchService {
    return this.searchService;
  }

  /**
   * Gets the query service
   * @returns The query service
   */
  public getQueryService(): QueryService {
    return this.queryService;
  }

  /**
   * Gets the UI service
   * @returns The UI service
   */
  public getUiService(): UiService {
    return this.uiService;
  }

  /**
   * Gets the field formats service
   * @returns The field formats service
   */
  public getFieldFormatsService(): FieldFormatsService {
    return this.fieldFormatsService;
  }

  /**
   * Gets the autocomplete service
   * @returns The autocomplete service
   */
  public getAutocompleteService(): AutocompleteService {
    return this.autocompleteService;
  }

  /**
   * Gets the storage service
   * @returns The storage service
   */
  public getStorage(): DataStorage {
    return this.storage;
  }
  
  /**
   * Gets the index pattern service
   * @returns The index pattern service
   */
  public getIndexPatternService(): IndexPatternService {
    return this.indexPatternService;
  }
}