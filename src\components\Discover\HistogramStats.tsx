import React, { useState } from 'react';
import { useDiscoverTime } from '../../hooks';
import HistogramLevelDistribution from './HistogramLevelDistribution';
import HistogramRuleDistribution from './HistogramRuleDistribution';

/**
 * Component to display histogram statistics and distributions
 */
const HistogramStats: React.FC = () => {
  const { timeRange, formatDate } = useDiscoverTime();
  const [activeTab, setActiveTab] = useState<'levels' | 'rules'>('levels');
  
  return (
    <div style={{
      marginTop: '16px',
      padding: '12px',
      background: 'rgba(0, 0, 0, 0.2)',
      borderRadius: '4px',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        <h4 style={{ 
          margin: 0, 
          color: 'white',
          fontSize: '14px',
        }}>
          Log Statistics
        </h4>
        
        <div style={{
          fontSize: '12px',
          color: 'rgba(255, 255, 255, 0.7)',
        }}>
          {formatDate(timeRange.from)} - {formatDate(timeRange.to)}
        </div>
      </div>
      
      {/* Tab navigation */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '16px',
      }}>
        <button
          onClick={() => setActiveTab('levels')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'levels' 
              ? '2px solid #00e5ff' 
              : '2px solid transparent',
            color: activeTab === 'levels' ? 'white' : 'rgba(255, 255, 255, 0.5)',
            padding: '8px 16px',
            fontSize: '13px',
            cursor: 'pointer',
            marginRight: '16px',
          }}
        >
          Log Levels
        </button>
        <button
          onClick={() => setActiveTab('rules')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'rules' 
              ? '2px solid #00e5ff' 
              : '2px solid transparent',
            color: activeTab === 'rules' ? 'white' : 'rgba(255, 255, 255, 0.5)',
            padding: '8px 16px',
            fontSize: '13px',
            cursor: 'pointer',
          }}
        >
          Rule Groups
        </button>
      </div>
      
      {/* Tab content */}
      {activeTab === 'levels' ? (
        <HistogramLevelDistribution />
      ) : (
        <HistogramRuleDistribution />
      )}
    </div>
  );
};

export default HistogramStats;