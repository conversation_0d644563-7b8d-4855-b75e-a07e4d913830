import React, { useState } from 'react';
import { useDiscover } from '../../context/DiscoverContext';
import DiscoverToolbar from './DiscoverToolbar';
import DiscoverHistogram from './DiscoverHistogram';
import HistogramStats from './HistogramStats';
import DiscoverTable from './DiscoverTable';

/**
 * Main content component for the Discover page
 * Contains the toolbar, histogram, and log table
 */
const DiscoverMainContent: React.FC = () => {
  const { state } = useDiscover();
  const { isLoading, filteredData } = state;
  const [activeTab, setActiveTab] = useState<'logs' | 'statistics' | 'distribution'>('logs');
  const hasLogs = filteredData.length > 0;
  
  return (
    <div style={{
      flexGrow: 1,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
    }}>
      {/* Toolbar with search and controls */}
      <DiscoverToolbar />
      {/* Loading overlay */}
      {isLoading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(10, 14, 23, 0.7)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 100,
          backdropFilter: 'blur(2px)',
        }}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '16px',
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '3px solid rgba(0, 229, 255, 0.3)',
              borderTop: '3px solid #00e5ff',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }} />
            <div style={{
              color: 'white',
              fontSize: '16px',
            }}>
              Loading logs...
            </div>
            <style>
              {`
                @keyframes spin {
                  0% { transform: rotate(0deg); }
                  100% { transform: rotate(360deg); }
                }
              `}
            </style>
          </div>
        </div>
      )}
      {/* Main content area with tabs */}
      <div style={{
        flexGrow: 1,
        padding: '16px',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
      }}>
        {/* Tab bar */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          marginBottom: '16px',
        }}>
          <button
            onClick={() => setActiveTab('logs')}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'logs' ? '2px solid #00e5ff' : '2px solid transparent',
              color: activeTab === 'logs' ? 'white' : 'rgba(255,255,255,0.6)',
              padding: '12px 24px',
              fontSize: '15px',
              cursor: 'pointer',
              fontWeight: activeTab === 'logs' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Logs
          </button>
          <button
            onClick={() => hasLogs && setActiveTab('statistics')}
            disabled={!hasLogs}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'statistics' ? '2px solid #00e5ff' : '2px solid transparent',
              color: !hasLogs ? 'rgba(255,255,255,0.3)' : (activeTab === 'statistics' ? 'white' : 'rgba(255,255,255,0.6)'),
              padding: '12px 24px',
              fontSize: '15px',
              cursor: hasLogs ? 'pointer' : 'not-allowed',
              fontWeight: activeTab === 'statistics' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Statistics
          </button>
          <button
            onClick={() => hasLogs && setActiveTab('distribution')}
            disabled={!hasLogs}
            style={{
              background: 'transparent',
              border: 'none',
              borderBottom: activeTab === 'distribution' ? '2px solid #00e5ff' : '2px solid transparent',
              color: !hasLogs ? 'rgba(255,255,255,0.3)' : (activeTab === 'distribution' ? 'white' : 'rgba(255,255,255,0.6)'),
              padding: '12px 24px',
              fontSize: '15px',
              cursor: hasLogs ? 'pointer' : 'not-allowed',
              fontWeight: activeTab === 'distribution' ? 'bold' : 'normal',
              outline: 'none',
              transition: 'color 0.2s',
            }}
          >
            Distribution
          </button>
        </div>
        {/* Tab content */}
        <div style={{ flexGrow: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
          {activeTab === 'logs' && (
            <div style={{ flexGrow: 1, minHeight: 0, display: 'flex', flexDirection: 'column' }}>
              <div style={{
                flexGrow: 1,
                minHeight: 0,
                background: 'rgba(16, 24, 45, 0.7)',
                borderRadius: '8px',
                overflow: 'hidden',
                border: '1px solid rgba(0, 229, 255, 0.2)',
                position: 'relative',
              }}>
                {filteredData.length === 0 && !isLoading ? (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'rgba(255, 255, 255, 0.7)',
                    padding: '24px',
                    textAlign: 'center',
                  }}>
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="rgba(0, 229, 255, 0.5)" strokeWidth="1.5" style={{ marginBottom: '16px' }}>
                      <circle cx="11" cy="11" r="8" />
                      <path d="M21 21l-4.35-4.35" />
                    </svg>
                    <h3 style={{ margin: '0 0 8px 0', color: 'white' }}>No logs found</h3>
                    <p style={{ margin: 0, maxWidth: '400px' }}>
                      Try adjusting your search query or filters, or changing the time range to see more results.
                    </p>
                  </div>
                ) : (
                  <DiscoverTable
                    showExpandedView={true}
                    showPagination={true}
                  />
                )}
              </div>
            </div>
          )}
          {activeTab === 'statistics' && (
            <div style={{
              flexGrow: 1,
              background: 'rgba(16, 24, 45, 0.7)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
              justifyContent: 'flex-start',
            }}>
              {hasLogs ? <HistogramStats /> : <div style={{ color: 'rgba(255,255,255,0.7)' }}>No statistics available (no logs).</div>}
            </div>
          )}
          {activeTab === 'distribution' && (
            <div style={{
              flexGrow: 1,
              background: 'rgba(16, 24, 45, 0.7)',
              borderRadius: '8px',
              padding: '16px',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
              justifyContent: 'flex-start',
            }}>
              {hasLogs ? <DiscoverHistogram /> : <div style={{ color: 'rgba(255,255,255,0.7)' }}>No distribution available (no logs).</div>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DiscoverMainContent;