import React, { useState } from 'react';
import DiscoverFilters from './DiscoverFilters';
import DiscoverFields from './DiscoverFields';

interface DiscoverSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

/**
 * Sidebar component for the Discover page
 * Contains field selection and filtering controls with tabbed interface
 */
const DiscoverSidebar: React.FC<DiscoverSidebarProps> = ({ 
  isCollapsed, 
  onToggleCollapse 
}) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState<'fields' | 'filters'>('filters');
  
  // Calculate sidebar width based on collapsed state
  const sidebarWidth = isCollapsed ? '50px' : '300px';
  
  return (
    <div style={{
      width: sidebarWidth,
      height: '100%',
      background: 'rgba(10, 14, 23, 0.8)',
      borderRight: '1px solid rgba(0, 229, 255, 0.2)',
      transition: 'width 0.3s ease',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {/* Sidebar header with toggle button */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        display: 'flex',
        justifyContent: isCollapsed ? 'center' : 'space-between',
        alignItems: 'center',
      }}>
        {!isCollapsed && (
          <h3 style={{ margin: 0, color: 'white' }}>Discover</h3>
        )}
        <button 
          onClick={onToggleCollapse}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          style={{
            background: 'transparent',
            border: 'none',
            color: '#00e5ff',
            cursor: 'pointer',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {isCollapsed ? (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
          ) : (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 5l-7 7 7 7M19 5l-7 7 7 7" />
            </svg>
          )}
        </button>
      </div>
      
      {/* Sidebar content - only show when not collapsed */}
      {!isCollapsed && (
        <>
          {/* Tabs */}
          <div style={{
            display: 'flex',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          }}>
            <button
              onClick={() => setActiveTab('filters')}
              style={{
                flex: 1,
                padding: '12px',
                background: 'transparent',
                border: 'none',
                borderBottom: activeTab === 'filters' ? '2px solid #00e5ff' : '2px solid transparent',
                color: activeTab === 'filters' ? 'white' : 'rgba(255, 255, 255, 0.6)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: activeTab === 'filters' ? 'bold' : 'normal',
              }}
            >
              Filters
            </button>
            <button
              onClick={() => setActiveTab('fields')}
              style={{
                flex: 1,
                padding: '12px',
                background: 'transparent',
                border: 'none',
                borderBottom: activeTab === 'fields' ? '2px solid #00e5ff' : '2px solid transparent',
                color: activeTab === 'fields' ? 'white' : 'rgba(255, 255, 255, 0.6)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: activeTab === 'fields' ? 'bold' : 'normal',
              }}
            >
              Fields
            </button>
          </div>
          
          {/* Tab content */}
          <div style={{
            padding: '16px',
            overflowY: 'auto',
            flexGrow: 1,
            position: 'relative',
          }}>
            {activeTab === 'filters' && <DiscoverFilters />}
            {activeTab === 'fields' && <DiscoverFields />}
          </div>
        </>
      )}
    </div>
  );
};

export default DiscoverSidebar;