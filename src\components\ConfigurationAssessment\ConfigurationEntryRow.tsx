import React, { memo, useMemo } from 'react';
import { ConfigurationEntry } from '../../types/configuration';
import { useConfigurationEntries } from '../../hooks';

interface ConfigurationEntryRowProps {
  entry: ConfigurationEntry;
  isExpanded: boolean;
  onToggleExpand: () => void;
  selectedFields: string[];
  columnWidths?: Record<string, number>;
}

/**
 * Component for displaying a single configuration entry row in the table
 * Optimized with memoization to prevent unnecessary re-renders
 */
const ConfigurationEntryRow: React.FC<ConfigurationEntryRowProps> = ({ 
  entry, 
  isExpanded, 
  onToggleExpand,
  selectedFields,
  columnWidths = {}
}) => {
  const { getFieldValue, formatFieldValue, getFieldColor } = useConfigurationEntries();
  
  // Memoize the field values to prevent recalculation on each render
  const fieldValues = useMemo(() => {
    return selectedFields.map(field => {
      const value = getFieldValue(entry, field);
      const color = getFieldColor(value, field);
      const formattedValue = formatFieldValue(value, field);
      
      return { field, value, color, formattedValue };
    });
  }, [entry, selectedFields, getFieldValue, formatFieldValue, getFieldColor]);
  
  // Memoize the background style to prevent object recreation on each render
  const rowStyle = useMemo(() => ({
    display: 'flex',
    borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
    padding: '8px 16px',
    color: 'white',
    fontSize: '14px',
    cursor: 'pointer',
    background: isExpanded 
      ? 'rgba(0, 229, 255, 0.05)' 
      : 'transparent',
    transition: 'background-color 0.2s',
    alignItems: 'center',
    outline: 'none',
  }), [isExpanded]);
  
  // Memoize the chevron style to prevent object recreation on each render
  const chevronStyle = useMemo(() => ({
    transform: isExpanded ? 'rotate(90deg)' : 'rotate(0deg)',
    transition: 'transform 0.2s',
  }), [isExpanded]);
  
  return (
    <div 
      role="row"
      aria-expanded={isExpanded}
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onToggleExpand();
        }
      }}
      style={rowStyle}
      onClick={onToggleExpand}
    >
      <div style={{ width: '30px', display: 'flex', alignItems: 'center' }}>
        <svg 
          width="16" 
          height="16" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="#00e5ff" 
          strokeWidth="2"
          style={chevronStyle}
        >
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </div>
      
      {fieldValues.map(({ field, color, formattedValue }) => {
        // Memoize the cell style for each field
        const cellStyle = {
          flexGrow: columnWidths[field] ? 0 : (field === 'rule.description' ? 3 : 1),
          padding: '0 8px',
          whiteSpace: 'nowrap' as const,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          color: color || 'inherit',
          width: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
          minWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
          maxWidth: columnWidths[field] ? `${columnWidths[field]}px` : undefined,
        };
        
        return (
          <div key={field} style={cellStyle}>
            {formattedValue}
          </div>
        );
      })}
    </div>
  );
};

// Use React.memo to prevent re-renders when props haven't changed
export default memo(ConfigurationEntryRow, (prevProps, nextProps) => {
  // Custom comparison function for more precise control over re-renders
  return (
    prevProps.entry.id === nextProps.entry.id &&
    prevProps.isExpanded === nextProps.isExpanded &&
    prevProps.selectedFields === nextProps.selectedFields &&
    Object.keys(prevProps.columnWidths || {}).length === Object.keys(nextProps.columnWidths || {}).length
  );
});