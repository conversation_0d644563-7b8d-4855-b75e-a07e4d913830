import React, { useState } from 'react';
import FieldCategory from './FieldCategory';

interface FieldInfo {
  type: string;
  count: number;
  topValues?: Array<{value: unknown, count: number}>;
}

interface CategorizedFields {
  [category: string]: {
    [fieldName: string]: FieldInfo;
  };
}

interface AvailableFieldsProps {
  categorizedFields: CategorizedFields;
  filteredFields?: string[];
  onAddField: (field: string) => void;
}

/**
 * Component for displaying available fields grouped by category in the Discover sidebar
 */
const AvailableFields: React.FC<AvailableFieldsProps> = ({ 
  categorizedFields, 
  filteredFields,
  onAddField 
}) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  
  // Toggle category expansion
  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };
  
  // Filter categories and fields based on search term
  const getFilteredCategories = () => {
    if (!filteredFields || filteredFields.length === 0) {
      return categorizedFields;
    }
    
    const filtered: CategorizedFields = {};
    
    Object.entries(categorizedFields).forEach(([category, fields]) => {
      const filteredFieldsInCategory = Object.entries(fields)
        .filter(([fieldName]) => filteredFields.includes(fieldName))
        .reduce((acc, [fieldName, fieldInfo]) => {
          acc[fieldName] = fieldInfo;
          return acc;
        }, {} as {[fieldName: string]: FieldInfo});
      
      if (Object.keys(filteredFieldsInCategory).length > 0) {
        filtered[category] = filteredFieldsInCategory;
      }
    });
    
    return filtered;
  };
  
  const filteredCategories = getFilteredCategories();
  const hasCategories = Object.keys(filteredCategories).length > 0;
  
  return (
    <div>
      <h4 style={{ 
        color: 'white', 
        marginBottom: '8px',
        fontSize: '14px',
        textTransform: 'uppercase',
        letterSpacing: '1px',
      }}>
        Available Fields
      </h4>
      
      {!hasCategories ? (
        <div style={{ 
          padding: '12px', 
          background: 'rgba(0, 0, 0, 0.2)',
          borderRadius: '4px',
          color: 'rgba(255, 255, 255, 0.5)',
          fontSize: '14px',
          textAlign: 'center'
        }}>
          No matching fields found
        </div>
      ) : (
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          {Object.entries(filteredCategories).map(([category, fields]) => (
            <FieldCategory
              key={category}
              category={category}
              fields={fields}
              isExpanded={expandedCategories.includes(category)}
              onToggleExpand={() => toggleCategory(category)}
              onAddField={onAddField}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default AvailableFields;