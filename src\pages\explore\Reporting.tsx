import React from 'react';

/**
 * Reporting page component.
 * 
 * This component renders the Reporting page content from the Explore section.
 */
const Reporting: React.FC = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Reporting
      </h1>
      
      <div style={{
        display: 'flex',
        gap: '24px',
        marginTop: '24px',
      }}>
        {/* Report list */}
        <div style={{
          width: '300px',
          borderRadius: '12px',
          padding: '20px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        }}>
          <h3 style={{ 
            fontSize: '18px', 
            fontWeight: 500, 
            marginBottom: '16px',
            color: '#00e5ff',
          }}>
            Available Reports
          </h3>
          
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '12px',
          }}>
            {[
              'Security Posture Summary', 
              'Threat Intelligence Report', 
              'Compliance Status', 
              'Vulnerability Assessment', 
              'User Activity Analysis',
              'Network Traffic Analysis',
              'Incident Response Summary'
            ].map((report, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 16px',
                borderRadius: '8px',
                background: index === 0 ? 'rgba(0, 229, 255, 0.1)' : 'rgba(0, 229, 255, 0.05)',
                border: index === 0 ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(0, 229, 255, 0.1)',
                cursor: 'pointer',
              }}>
                <span style={{ color: index === 0 ? '#00e5ff' : 'white' }}>{report}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Report preview */}
        <div style={{
          flex: 1,
          borderRadius: '12px',
          padding: '20px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '20px',
          }}>
            <h3 style={{ 
              fontSize: '18px', 
              fontWeight: 500,
              color: '#00e5ff',
            }}>
              Security Posture Summary
            </h3>
            
            <div style={{
              display: 'flex',
              gap: '10px',
            }}>
              <button style={{
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '6px 12px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                Export PDF
              </button>
              <button style={{
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '6px 12px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                Schedule
              </button>
            </div>
          </div>
          
          <div style={{
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '14px',
            lineHeight: '1.6',
          }}>
            <p style={{ marginBottom: '16px' }}>
              This report provides an overview of your organization's security posture for the period of July 10-17, 2025.
            </p>
            
            <h4 style={{ 
              fontSize: '16px', 
              color: 'white',
              marginBottom: '8px',
              marginTop: '16px',
            }}>
              Executive Summary
            </h4>
            <p style={{ marginBottom: '16px' }}>
              Overall security score: 19/100 (High Risk)
              <br />
              Total threats detected: 13 (7 mitigated, 6 pending)
              <br />
              Compliance status: 3 critical issues require attention
            </p>
            
            <h4 style={{ 
              fontSize: '16px', 
              color: 'white',
              marginBottom: '8px',
              marginTop: '16px',
            }}>
              Key Findings
            </h4>
            <ul style={{ 
              marginLeft: '20px',
              marginBottom: '16px',
            }}>
              <li>EDR solution requires critical security update</li>
              <li>7 phishing attempts detected targeting executive team</li>
              <li>Unauthorized access attempts increased by 23% from previous period</li>
              <li>3 endpoints have missing security patches</li>
            </ul>
            
            <h4 style={{ 
              fontSize: '16px', 
              color: 'white',
              marginBottom: '8px',
              marginTop: '16px',
            }}>
              Recommendations
            </h4>
            <ul style={{ 
              marginLeft: '20px',
              marginBottom: '16px',
            }}>
              <li>Update EDR solution immediately</li>
              <li>Conduct phishing awareness training for executive team</li>
              <li>Apply security patches to all endpoints</li>
              <li>Review and strengthen access control policies</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reporting;