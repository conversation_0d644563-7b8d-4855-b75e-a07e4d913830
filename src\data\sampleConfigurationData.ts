/**
 * Sample data generator for Configuration Assessment feature
 */

import { 
  ConfigurationEntry, 
  ComplianceStandard
} from '../types/configuration';
import { v4 as uuidv4 } from 'uuid';

/**
 * Configuration Data Generator class
 * Generates realistic sample data for configuration assessments
 */
export class ConfigurationDataGenerator {
  // Common compliance standards
  private static complianceStandards: ComplianceStandard[] = [
    'PCI DSS', 'HIPAA', 'NIST', 'GDPR', 'CIS', 'SOC2', 'ISO27001'
  ];

  // Common components that might be assessed
  private static components = [
    'Password Policy', 'Firewall', 'Encryption', 'Access Control',
    'Audit Logging', 'Network Configuration', 'File Permissions',
    'User Management', 'Software Updates', 'Backup Configuration',
    'Database Security', 'Web Server Configuration', 'API Security',
    'Cloud Configuration', 'Container Security'
  ];

  // Sample rule definitions with realistic security checks
  private static ruleDefinitions = [
    {
      id: 101,
      description: 'Ensure password policy requires minimum length',
      level: 7,
      groups: ['password', 'policy', 'authentication'],
      check: {
        title: 'Password Minimum Length',
        description: 'Checks if password policy requires minimum length of at least 8 characters',
        rationale: 'Short passwords are easier to crack through brute force attacks',
        remediation: 'Set minimum password length to at least 8 characters in system configuration',
        compliance: ['PCI DSS', 'NIST', 'CIS']
      },
      configuration: 'MinimumPasswordLength',
      scoreRange: [6, 8]
    },
    {
      id: 102,
      description: 'Ensure firewall is enabled',
      level: 9,
      groups: ['firewall', 'network', 'perimeter'],
      check: {
        title: 'Firewall Status',
        description: 'Checks if firewall service is enabled and properly configured',
        rationale: 'Firewall provides essential protection against unauthorized network access',
        remediation: 'Enable firewall service and configure appropriate rules',
        compliance: ['PCI DSS', 'HIPAA', 'NIST', 'CIS']
      },
      configuration: 'FirewallEnabled',
      scoreRange: [8, 10]
    },
    {
      id: 103,
      description: 'Check for unnecessary services',
      level: 5,
      groups: ['services', 'hardening'],
      check: {
        title: 'Unnecessary Services',
        description: 'Checks for unnecessary running services that could increase attack surface',
        rationale: 'Unnecessary services increase the attack surface and potential vulnerabilities',
        remediation: 'Disable or uninstall unnecessary services and daemons',
        compliance: ['CIS', 'NIST']
      },
      configuration: 'UnnecessaryServices',
      scoreRange: [4, 6]
    },
    {
      id: 104,
      description: 'Ensure data encryption at rest',
      level: 8,
      groups: ['encryption', 'data-protection'],
      check: {
        title: 'Data Encryption',
        description: 'Checks if sensitive data is encrypted at rest',
        rationale: 'Unencrypted sensitive data can be exposed if physical access is compromised',
        remediation: 'Enable disk encryption for all volumes containing sensitive data',
        compliance: ['PCI DSS', 'HIPAA', 'GDPR', 'ISO27001']
      },
      configuration: 'DataEncryption',
      scoreRange: [7, 9]
    },
    {
      id: 105,
      description: 'Ensure secure TLS configuration',
      level: 7,
      groups: ['encryption', 'web', 'communication'],
      check: {
        title: 'TLS Configuration',
        description: 'Checks if TLS configuration uses secure protocols and cipher suites',
        rationale: 'Weak TLS configurations can lead to man-in-the-middle attacks',
        remediation: 'Configure servers to use TLS 1.2+ and secure cipher suites only',
        compliance: ['PCI DSS', 'NIST', 'HIPAA']
      },
      configuration: 'TLSConfiguration',
      scoreRange: [6, 8]
    },
    {
      id: 106,
      description: 'Check for default credentials',
      level: 9,
      groups: ['authentication', 'credentials'],
      check: {
        title: 'Default Credentials',
        description: 'Checks if default credentials have been changed',
        rationale: 'Default credentials are widely known and easily exploitable',
        remediation: 'Change all default passwords and disable default accounts',
        compliance: ['PCI DSS', 'CIS', 'NIST', 'ISO27001']
      },
      configuration: 'DefaultCredentials',
      scoreRange: [8, 10]
    },
    {
      id: 107,
      description: 'Ensure proper file permissions',
      level: 6,
      groups: ['file-system', 'permissions'],
      check: {
        title: 'File Permissions',
        description: 'Checks if critical files have appropriate permissions',
        rationale: 'Improper file permissions can allow unauthorized access to sensitive data',
        remediation: 'Set appropriate permissions on critical system and configuration files',
        compliance: ['CIS', 'NIST', 'SOC2']
      },
      configuration: 'FilePermissions',
      scoreRange: [5, 7]
    },
    {
      id: 108,
      description: 'Ensure audit logging is enabled',
      level: 7,
      groups: ['logging', 'auditing'],
      check: {
        title: 'Audit Logging',
        description: 'Checks if audit logging is enabled and properly configured',
        rationale: 'Audit logs are essential for security monitoring and incident response',
        remediation: 'Enable comprehensive audit logging for security-relevant events',
        compliance: ['PCI DSS', 'HIPAA', 'SOC2', 'GDPR']
      },
      configuration: 'AuditLogging',
      scoreRange: [6, 8]
    },
    {
      id: 109,
      description: 'Check for insecure remote access protocols',
      level: 8,
      groups: ['remote-access', 'network'],
      check: {
        title: 'Secure Remote Access',
        description: 'Checks if insecure remote access protocols are disabled',
        rationale: 'Insecure protocols like Telnet and FTP transmit data in cleartext',
        remediation: 'Disable insecure protocols and use encrypted alternatives like SSH and SFTP',
        compliance: ['PCI DSS', 'NIST', 'CIS']
      },
      configuration: 'RemoteAccessProtocols',
      scoreRange: [7, 9]
    },
    {
      id: 110,
      description: 'Ensure regular security updates',
      level: 8,
      groups: ['updates', 'patching'],
      check: {
        title: 'Security Updates',
        description: 'Checks if security updates are regularly applied',
        rationale: 'Unpatched systems are vulnerable to known exploits',
        remediation: 'Implement a regular patching schedule and automated update checks',
        compliance: ['PCI DSS', 'HIPAA', 'CIS', 'ISO27001']
      },
      configuration: 'SecurityUpdates',
      scoreRange: [7, 9]
    },
    {
      id: 111,
      description: 'Check for secure database configuration',
      level: 7,
      groups: ['database', 'configuration'],
      check: {
        title: 'Database Security',
        description: 'Checks if database has secure configuration settings',
        rationale: 'Insecure database configurations can lead to data breaches',
        remediation: 'Apply security hardening guidelines for the specific database system',
        compliance: ['PCI DSS', 'HIPAA', 'GDPR']
      },
      configuration: 'DatabaseSecurity',
      scoreRange: [6, 8]
    },
    {
      id: 112,
      description: 'Ensure proper backup configuration',
      level: 6,
      groups: ['backup', 'disaster-recovery'],
      check: {
        title: 'Backup Configuration',
        description: 'Checks if backups are properly configured and tested',
        rationale: 'Proper backups are essential for recovery from security incidents',
        remediation: 'Configure regular encrypted backups and test restoration procedures',
        compliance: ['HIPAA', 'SOC2', 'ISO27001']
      },
      configuration: 'BackupConfiguration',
      scoreRange: [5, 7]
    },
    {
      id: 113,
      description: 'Check for secure API authentication',
      level: 7,
      groups: ['api', 'authentication'],
      check: {
        title: 'API Authentication',
        description: 'Checks if APIs use secure authentication methods',
        rationale: 'Insecure API authentication can lead to unauthorized access',
        remediation: 'Implement OAuth 2.0 or API keys with proper validation',
        compliance: ['PCI DSS', 'NIST', 'GDPR']
      },
      configuration: 'APIAuthentication',
      scoreRange: [6, 8]
    },
    {
      id: 114,
      description: 'Ensure secure cloud storage configuration',
      level: 8,
      groups: ['cloud', 'storage'],
      check: {
        title: 'Cloud Storage Security',
        description: 'Checks if cloud storage buckets have proper access controls',
        rationale: 'Misconfigured cloud storage can lead to data exposure',
        remediation: 'Configure proper access controls and encryption for cloud storage',
        compliance: ['PCI DSS', 'HIPAA', 'GDPR', 'SOC2']
      },
      configuration: 'CloudStorageSecurity',
      scoreRange: [7, 9]
    },
    {
      id: 115,
      description: 'Check for container security settings',
      level: 7,
      groups: ['container', 'docker', 'kubernetes'],
      check: {
        title: 'Container Security',
        description: 'Checks if containers are running with secure configurations',
        rationale: 'Insecure container configurations can lead to escapes and compromises',
        remediation: 'Apply security best practices for container runtime and orchestration',
        compliance: ['CIS', 'NIST', 'ISO27001']
      },
      configuration: 'ContainerSecurity',
      scoreRange: [6, 8]
    }
  ];

  // Sample agent definitions
  private static agentDefinitions = [
    { id: 'agent001', name: 'web-server-01', ip: '***********0' },
    { id: 'agent002', name: 'db-server-01', ip: '************' },
    { id: 'agent003', name: 'app-server-01', ip: '************' },
    { id: 'agent004', name: 'web-server-02', ip: '***********1' },
    { id: 'agent005', name: 'db-server-02', ip: '************' },
    { id: 'agent006', name: 'app-server-02', ip: '************' },
    { id: 'agent007', name: 'firewall-01', ip: '***********' },
    { id: 'agent008', name: 'backup-server-01', ip: '************' },
    { id: 'agent009', name: 'monitoring-01', ip: '************' },
    { id: 'agent010', name: 'cloud-gateway-01', ip: '*************' }
  ];

  /**
   * Generate a random date within a specified range
   * @param start Start date
   * @param end End date
   * @returns Random date between start and end
   */
  private static randomDate(start: Date, end: Date): Date {
    return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
  }

  /**
   * Generate a random number within a range
   * @param min Minimum value
   * @param max Maximum value
   * @returns Random number between min and max
   */
  private static randomNumber(min: number, max: number): number {
    return Math.round((Math.random() * (max - min) + min) * 10) / 10;
  }

  /**
   * Get random items from an array
   * @param array Source array
   * @param count Number of items to get
   * @returns Array of random items
   */
  private static getRandomItems<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);
  }

  /**
   * Generate a single configuration entry
   * @param resultOverride Optional override for the result status
   * @returns Configuration entry
   */
  public static generateEntry(resultOverride?: 'passed' | 'failed' | 'not_applicable'): ConfigurationEntry {
    // Select a random rule definition
    const ruleDef = this.ruleDefinitions[Math.floor(Math.random() * this.ruleDefinitions.length)];
    
    // Select a random agent
    const agent = this.agentDefinitions[Math.floor(Math.random() * this.agentDefinitions.length)];
    
    // Generate a random date within the last 30 days
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const timestamp = this.randomDate(thirtyDaysAgo, now);
    
    // Generate a random score within the rule's score range
    const score = this.randomNumber(ruleDef.scoreRange[0], ruleDef.scoreRange[1]);
    
    // Determine result status (if not overridden)
    const result = resultOverride || (Math.random() < 0.1 ? 'not_applicable' : (Math.random() < 0.6 ? 'passed' : 'failed'));
    
    // Generate sample data based on the rule and result
    const data: Record<string, unknown> = {};
    
    switch (ruleDef.configuration) {
      case 'MinimumPasswordLength':
        data.current_value = result === 'passed' ? '8' : '4';
        data.expected_value = '8';
        break;
      case 'FirewallEnabled':
        data.current_value = result === 'passed' ? 'true' : 'false';
        data.expected_value = 'true';
        break;
      case 'UnnecessaryServices':
        data.running_services = result === 'passed' ? [] : ['telnet', 'rsh'];
        data.recommended_action = 'Disable unnecessary services';
        break;
      case 'DataEncryption':
        data.encryption_status = result === 'passed' ? 'enabled' : 'disabled';
        data.encryption_algorithm = result === 'passed' ? 'AES-256' : 'none';
        break;
      case 'TLSConfiguration':
        data.current_protocols = result === 'passed' ? ['TLSv1.2', 'TLSv1.3'] : ['TLSv1.0', 'TLSv1.1', 'TLSv1.2'];
        data.recommended_protocols = ['TLSv1.2', 'TLSv1.3'];
        break;
      default:
        data.status = result === 'passed' ? 'compliant' : 'non-compliant';
        data.details = result === 'passed' 
          ? 'Configuration meets security requirements' 
          : 'Configuration does not meet security requirements';
    }
    
    // Create and return the configuration entry
    return {
      id: uuidv4(),
      timestamp,
      agent: {
        id: agent.id,
        name: agent.name,
        ip: agent.ip
      },
      rule: {
        id: ruleDef.id,
        description: ruleDef.description,
        level: ruleDef.level,
        groups: ruleDef.groups
      },
      check: {
        id: `check-${ruleDef.id}`,
        title: ruleDef.check.title,
        description: ruleDef.check.description,
        rationale: ruleDef.check.rationale,
        remediation: ruleDef.check.remediation,
        compliance: ruleDef.check.compliance
      },
      result,
      score,
      scan_id: `scan-${Math.floor(timestamp.getTime() / (24 * 60 * 60 * 1000))}`,
      component: this.components.find(c => c.toLowerCase().includes(ruleDef.configuration.toLowerCase())) || 
                this.components[Math.floor(Math.random() * this.components.length)],
      configuration: ruleDef.configuration,
      data
    };
  }

  /**
   * Generate a dataset with specified distribution of results
   * @param count Total number of entries to generate
   * @param passedPercentage Percentage of passed results (0-100)
   * @param failedPercentage Percentage of failed results (0-100)
   * @param notApplicablePercentage Percentage of not applicable results (0-100)
   * @returns Array of configuration entries
   */
  public static generateDataset(
    count: number,
    passedPercentage: number = 60,
    failedPercentage: number = 30,
    notApplicablePercentage: number = 10
  ): ConfigurationEntry[] {
    // Validate percentages
    const totalPercentage = passedPercentage + failedPercentage + notApplicablePercentage;
    if (totalPercentage !== 100) {
      throw new Error('Percentages must add up to 100');
    }
    
    const entries: ConfigurationEntry[] = [];
    
    // Calculate counts for each result type
    const passedCount = Math.floor(count * (passedPercentage / 100));
    const failedCount = Math.floor(count * (failedPercentage / 100));
    const notApplicableCount = count - passedCount - failedCount;
    
    // Generate entries with specific result types
    for (let i = 0; i < passedCount; i++) {
      entries.push(this.generateEntry('passed'));
    }
    
    for (let i = 0; i < failedCount; i++) {
      entries.push(this.generateEntry('failed'));
    }
    
    for (let i = 0; i < notApplicableCount; i++) {
      entries.push(this.generateEntry('not_applicable'));
    }
    
    // Shuffle the entries to randomize the order
    return entries.sort(() => 0.5 - Math.random());
  }

  /**
   * Generate sample data with default distribution
   * @param count Number of entries to generate
   * @returns Array of configuration entries
   */
  public static generateSampleData(count: number = 100): ConfigurationEntry[] {
    return this.generateDataset(count);
  }

  /**
   * Generate a dataset with all passed results
   * @param count Number of entries to generate
   * @returns Array of configuration entries
   */
  public static generateAllPassedDataset(count: number = 100): ConfigurationEntry[] {
    return this.generateDataset(count, 100, 0, 0);
  }

  /**
   * Generate a dataset with all failed results
   * @param count Number of entries to generate
   * @returns Array of configuration entries
   */
  public static generateAllFailedDataset(count: number = 100): ConfigurationEntry[] {
    return this.generateDataset(count, 0, 100, 0);
  }

  /**
   * Generate a dataset with mixed results
   * @param count Number of entries to generate
   * @returns Array of configuration entries
   */
  public static generateMixedDataset(count: number = 100): ConfigurationEntry[] {
    return this.generateDataset(count, 50, 40, 10);
  }
}

// Export pre-generated sample datasets of different sizes
export const sampleConfigurationData = ConfigurationDataGenerator.generateSampleData(100);
export const smallConfigurationDataset = ConfigurationDataGenerator.generateSampleData(20);
export const largeConfigurationDataset = ConfigurationDataGenerator.generateSampleData(500);
export const allPassedDataset = ConfigurationDataGenerator.generateAllPassedDataset(50);
export const allFailedDataset = ConfigurationDataGenerator.generateAllFailedDataset(50);
export const mixedDataset = ConfigurationDataGenerator.generateMixedDataset(100);