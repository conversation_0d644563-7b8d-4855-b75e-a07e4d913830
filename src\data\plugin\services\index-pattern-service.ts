import { IndexPatternSpec, Dataset, DatasetField } from '../interfaces';

/**
 * Service for managing index patterns
 * Index patterns are used to normalize data from different sources
 */
export class IndexPatternService {
  private patterns: Map<string, IndexPatternSpec> = new Map();
  private cache: Map<string, IndexPatternSpec> = new Map();
  
  /**
   * Registers an index pattern
   * @param pattern The index pattern to register
   */
  public register(pattern: IndexPatternSpec): void {
    this.patterns.set(pattern.id, pattern);
  }
  
  /**
   * Gets an index pattern by ID
   * @param id The ID of the index pattern
   * @returns The index pattern, or undefined if not found
   */
  public get(id: string): IndexPatternSpec | undefined {
    // Check the cache first
    if (this.cache.has(id)) {
      return this.cache.get(id);
    }
    
    // Then check the registered patterns
    return this.patterns.get(id);
  }
  
  /**
   * Gets all registered index patterns
   * @returns An array of all registered index patterns
   */
  public getAll(): IndexPatternSpec[] {
    // Combine registered patterns and cached patterns
    const allPatterns = new Map([...this.patterns, ...this.cache]);
    return Array.from(allPatterns.values());
  }
  
  /**
   * Creates a temporary index pattern from a dataset
   * @param dataset The dataset to create an index pattern from
   * @param fields The fields for the index pattern
   * @returns The created index pattern
   */
  public createTemporaryPattern(dataset: Dataset, fields: DatasetField[]): IndexPatternSpec {
    const patternId = `temp_${dataset.id}`;
    
    // Create the index pattern
    const pattern: IndexPatternSpec = {
      id: patternId,
      title: dataset.title,
      timeFieldName: dataset.timeFieldName,
      fields,
      dataSourceRef: dataset.dataSource ? {
        id: dataset.dataSource.id,
        name: dataset.dataSource.id, // Use ID as name for now
        type: dataset.dataSource.type
      } : undefined
    };
    
    // Cache the pattern
    this.cache.set(patternId, pattern);
    
    return pattern;
  }
  
  /**
   * Gets or creates an index pattern for a dataset
   * @param dataset The dataset
   * @param fields The fields for the dataset (optional if already cached)
   * @returns A promise that resolves to the index pattern
   */
  public async getOrCreatePattern(dataset: Dataset, fields?: DatasetField[]): Promise<IndexPatternSpec> {
    const patternId = `temp_${dataset.id}`;
    
    // Check if the pattern is already cached
    if (this.cache.has(patternId)) {
      return this.cache.get(patternId)!;
    }
    
    // Check if fields were provided
    if (!fields) {
      throw new Error(`No fields provided for dataset ${dataset.id} and no cached pattern found`);
    }
    
    // Create a new pattern
    return this.createTemporaryPattern(dataset, fields);
  }
  
  /**
   * Clears the cache
   */
  public clearCache(): void {
    this.cache.clear();
  }
  
  /**
   * Removes a pattern from the cache
   * @param id The ID of the pattern to remove
   */
  public removeFromCache(id: string): void {
    this.cache.delete(id);
  }
}