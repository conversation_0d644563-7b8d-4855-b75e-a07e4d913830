import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { BaseSearchInterceptor } from './base-search-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../interfaces';
import { IndexPatternService } from '../services/index-pattern-service';

/**
 * Search interceptor that handles index patterns
 * This interceptor ensures that temporary index patterns are created for datasets
 * before the search is executed
 */
export class IndexPatternInterceptor extends BaseSearchInterceptor {
  private indexPatternService: IndexPatternService;
  
  /**
   * Constructor
   * @param indexPatternService The index pattern service
   */
  constructor(indexPatternService: IndexPatternService) {
    super();
    this.indexPatternService = indexPatternService;
  }
  
  /**
   * Processes the search request and ensures that an index pattern exists for the dataset
   * @param request The search request to process
   * @param options Options for the search execution
   * @returns An Observable of the search response
   */
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    // If there's no dataset in the query, just pass the request to the next interceptor
    if (!request.query.dataset) {
      return super.search(request, options);
    }
    
    // Get or create an index pattern for the dataset
    const dataset = request.query.dataset;
    
    // Create an observable that resolves to the index pattern
    return new Observable<SearchRequest>(observer => {
      // Try to get the index pattern from the service
      this.indexPatternService.getOrCreatePattern(dataset)
        .then(indexPattern => {
          // Clone the request to avoid modifying the original
          const modifiedRequest: SearchRequest = { ...request };
          
          // Add the index pattern ID to the request
          modifiedRequest.index = indexPattern.id;
          
          // Emit the modified request
          observer.next(modifiedRequest);
          observer.complete();
        })
        .catch(error => {
          console.error('Error getting or creating index pattern:', error);
          
          // If there's an error, just pass the original request
          observer.next(request);
          observer.complete();
        });
    }).pipe(
      // Pass the modified request to the next interceptor
      switchMap(modifiedRequest => super.search(modifiedRequest, options)),
      
      // Add index pattern information to the response
      map(response => {
        // Clone the response to avoid modifying the original
        const modifiedResponse: SearchResponse = { ...response };
        
        // Add index pattern information to the response metadata
        if (!modifiedResponse.meta) {
          modifiedResponse.meta = {};
        }
        
        modifiedResponse.meta.indexPattern = dataset.id;
        
        return modifiedResponse;
      })
    );
  }
}