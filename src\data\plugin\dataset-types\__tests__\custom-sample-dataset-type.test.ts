import { customSampleDatasetType } from '../custom-sample-dataset-type';
import { DataStructure, Dataset } from '../../interfaces';

describe('Custom Sample Dataset Type', () => {
  it('should have the correct ID and title', () => {
    expect(customSampleDatasetType.id).toBe('custom-sample-data');
    expect(customSampleDatasetType.title).toBe('Custom Sample Data');
  });
  
  it('should convert path to dataset correctly', () => {
    const path: DataStructure[] = [
      {
        id: 'transactions',
        title: 'Transactions',
        type: 'dataset'
      }
    ];
    
    const dataset = customSampleDatasetType.toDataset(path);
    
    expect(dataset).toEqual({
      id: 'custom-transactions',
      title: 'Transactions',
      type: 'custom-sample-data',
      timeFieldName: 'timestamp',
      language: 'simple-query'
    });
  });
  
  it('should throw error for invalid path', () => {
    expect(() => {
      customSampleDatasetType.toDataset([]);
    }).toThrow('Invalid path');
  });
  
  it('should fetch root structure when path is empty', async () => {
    const result = await customSampleDatasetType.fetch({}, []);
    
    expect(result.id).toBe('custom-root');
    expect(result.title).toBe('Custom Sample Data');
    expect(result.type).toBe('folder');
    expect(result.children).toHaveLength(3);
    expect(result.children?.[0].id).toBe('transactions');
    expect(result.children?.[1].id).toBe('users');
    expect(result.children?.[2].id).toBe('products');
  });
  
  it('should fetch specific dataset structure', async () => {
    const path: DataStructure[] = [
      {
        id: 'transactions',
        title: 'Transactions',
        type: 'dataset'
      }
    ];
    
    const result = await customSampleDatasetType.fetch({}, path);
    
    expect(result.id).toBe('transactions');
    expect(result.title).toBe('Transactions');
    expect(result.type).toBe('dataset');
    expect(result.meta?.count).toBe(5000);
  });
  
  it('should fetch fields for transactions dataset', async () => {
    const dataset: Dataset = {
      id: 'custom-transactions',
      title: 'Transactions',
      type: 'custom-sample-data'
    };
    
    const fields = await customSampleDatasetType.fetchFields(dataset);
    
    expect(fields).toHaveLength(6); // 2 common fields + 4 specific fields
    expect(fields.find(f => f.name === 'timestamp')).toBeDefined();
    expect(fields.find(f => f.name === 'id')).toBeDefined();
    expect(fields.find(f => f.name === 'amount')).toBeDefined();
    expect(fields.find(f => f.name === 'status')).toBeDefined();
    expect(fields.find(f => f.name === 'user_id')).toBeDefined();
    expect(fields.find(f => f.name === 'product_id')).toBeDefined();
  });
  
  it('should fetch fields for users dataset', async () => {
    const dataset: Dataset = {
      id: 'custom-users',
      title: 'Users',
      type: 'custom-sample-data'
    };
    
    const fields = await customSampleDatasetType.fetchFields(dataset);
    
    expect(fields).toHaveLength(6); // 2 common fields + 4 specific fields
    expect(fields.find(f => f.name === 'timestamp')).toBeDefined();
    expect(fields.find(f => f.name === 'id')).toBeDefined();
    expect(fields.find(f => f.name === 'name')).toBeDefined();
    expect(fields.find(f => f.name === 'email')).toBeDefined();
    expect(fields.find(f => f.name === 'age')).toBeDefined();
    expect(fields.find(f => f.name === 'active')).toBeDefined();
  });
  
  it('should fetch fields for products dataset', async () => {
    const dataset: Dataset = {
      id: 'custom-products',
      title: 'Products',
      type: 'custom-sample-data'
    };
    
    const fields = await customSampleDatasetType.fetchFields(dataset);
    
    expect(fields).toHaveLength(6); // 2 common fields + 4 specific fields
    expect(fields.find(f => f.name === 'timestamp')).toBeDefined();
    expect(fields.find(f => f.name === 'id')).toBeDefined();
    expect(fields.find(f => f.name === 'name')).toBeDefined();
    expect(fields.find(f => f.name === 'price')).toBeDefined();
    expect(fields.find(f => f.name === 'category')).toBeDefined();
    expect(fields.find(f => f.name === 'in_stock')).toBeDefined();
  });
  
  it('should return common fields for unknown dataset', async () => {
    const dataset: Dataset = {
      id: 'custom-unknown',
      title: 'Unknown',
      type: 'custom-sample-data'
    };
    
    const fields = await customSampleDatasetType.fetchFields(dataset);
    
    expect(fields).toHaveLength(2); // Only common fields
    expect(fields.find(f => f.name === 'timestamp')).toBeDefined();
    expect(fields.find(f => f.name === 'id')).toBeDefined();
  });
  
  it('should support the correct query languages', () => {
    const languages = customSampleDatasetType.supportedLanguages();
    
    expect(languages).toContain('simple-query');
    expect(languages).toContain('kuery');
    expect(languages).toHaveLength(2);
  });
});