import { lastValueFrom } from 'rxjs';
import { BaseSearchInterceptor } from '../base-search-interceptor';
import { SearchRequest, SearchOptions, SearchResponse } from '../../interfaces';

// Mock search strategy registry
jest.mock('../search-strategy', () => {
  return {
    SearchStrategyRegistry: jest.fn().mockImplementation(() => {
      return {
        get: jest.fn().mockReturnValue({
          id: 'mock',
          search: jest.fn().mockResolvedValue({
            hits: {
              total: 10,
              hits: [{ _id: 'test', _source: { field: 'value' } }]
            },
            took: 5,
            timed_out: false
          })
        }),
        getDefault: jest.fn().mockReturnValue({
          id: 'mock',
          search: jest.fn().mockResolvedValue({
            hits: {
              total: 10,
              hits: [{ _id: 'test', _source: { field: 'value' } }]
            },
            took: 5,
            timed_out: false
          })
        })
      };
    })
  };
});

// Mock sample search strategy
jest.mock('../sample-search-strategy', () => {
  return {
    SampleSearchStrategy: jest.fn().mockImplementation(() => {
      return {
        id: 'sample',
        search: jest.fn().mockResolvedValue({
          hits: {
            total: 10,
            hits: [{ _id: 'test', _source: { field: 'value' } }]
          },
          took: 5,
          timed_out: false
        })
      };
    })
  };
});

// Test interceptor that adds a field to the request
class TestRequestInterceptor extends BaseSearchInterceptor {
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    const modifiedRequest = {
      ...request,
      testField: 'test-value'
    };
    return super.search(modifiedRequest, options);
  }
}

// Test interceptor that adds a field to the response
class TestResponseInterceptor extends BaseSearchInterceptor {
  public search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse> {
    const response$ = super.search(request, options);
    return new Observable<SearchResponse>(observer => {
      response$.subscribe({
        next: response => {
          const modifiedResponse = {
            ...response,
            testResponseField: 'test-response-value'
          };
          observer.next(modifiedResponse);
          observer.complete();
        },
        error: err => observer.error(err),
        complete: () => observer.complete()
      });
    });
  }
}

describe('SearchInterceptor', () => {
  let baseInterceptor: BaseSearchInterceptor;
  let requestInterceptor: TestRequestInterceptor;
  let responseInterceptor: TestResponseInterceptor;
  
  beforeEach(() => {
    baseInterceptor = new BaseSearchInterceptor();
    requestInterceptor = new TestRequestInterceptor();
    responseInterceptor = new TestResponseInterceptor();
  });
  
  test('should chain interceptors correctly', () => {
    // Set up the chain
    const chain = responseInterceptor;
    chain.setNext(requestInterceptor);
    requestInterceptor.setNext(baseInterceptor);
    
    // Verify the chain
    expect(responseInterceptor.getNext()).toBe(requestInterceptor);
    expect(requestInterceptor.getNext()).toBe(baseInterceptor);
    expect(baseInterceptor.getNext()).toBeNull();
  });
  
  test('should execute search with chained interceptors', async () => {
    // Set up the chain
    const chain = responseInterceptor;
    chain.setNext(requestInterceptor);
    requestInterceptor.setNext(baseInterceptor);
    
    // Execute the search
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' }
    };
    const options: SearchOptions = {};
    
    const response = await lastValueFrom(chain.search(request, options));
    
    // Verify the response
    expect(response).toHaveProperty('hits');
    expect(response).toHaveProperty('took');
    expect(response).toHaveProperty('timed_out');
    expect(response).toHaveProperty('testResponseField', 'test-response-value');
  });
  
  test('should handle errors gracefully', async () => {
    // Create an interceptor that throws an error
    class ErrorInterceptor extends BaseSearchInterceptor {
      public search(): Observable<SearchResponse> {
        throw new Error('Test error');
      }
    }
    
    const errorInterceptor = new ErrorInterceptor();
    
    // Execute the search
    const request: SearchRequest = {
      query: { query: 'test', language: 'kuery' }
    };
    const options: SearchOptions = {};
    
    const response = await lastValueFrom(errorInterceptor.search(request, options));
    
    // Verify the response
    expect(response).toHaveProperty('hits');
    expect(response).toHaveProperty('took');
    expect(response).toHaveProperty('timed_out', true);
    expect(response).toHaveProperty('error');
    expect(response.error).toHaveProperty('message', 'Test error');
    expect(response.error).toHaveProperty('type', 'Error');
  });
});