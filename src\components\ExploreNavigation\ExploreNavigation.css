/* Responsive styles for Explore Navigation */

/* Base styles are handled in components, these are responsive overrides */

/* Tablet styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .explore-dropdown-overlay {
    min-width: 180px !important;
    font-size: 13px;
  }
  
  .explore-sub-item-label {
    font-size: 12px !important;
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .explore-dropdown-overlay {
    position: fixed !important;
    left: 60px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    min-width: 160px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
  }
  
  .explore-nav-item {
    margin-bottom: 8px !important;
  }
  
  .explore-sub-item {
    height: 36px !important;
    padding: 0 6px !important;
  }
  
  .explore-sub-item-label {
    font-size: 11px !important;
  }
}

/* Small mobile styles */
@media (max-width: 480px) {
  .explore-dropdown-overlay {
    left: 50px !important;
    min-width: 140px !important;
    padding: 6px !important;
  }
  
  .explore-sub-item {
    height: 32px !important;
    padding: 0 4px !important;
  }
  
  .explore-sub-item-label {
    font-size: 10px !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .explore-nav-item,
  .explore-sub-item {
    border-width: 2px !important;
  }
  
  .explore-nav-item.active,
  .explore-sub-item.active {
    border-color: #ffffff !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .explore-nav-item,
  .explore-sub-item,
  .explore-dropdown,
  .explore-chevron {
    transition: none !important;
    animation: none !important;
  }
}

/* Focus styles for better accessibility */
.explore-nav-item:focus-visible,
.explore-sub-item:focus-visible {
  outline: 2px solid #00e5ff !important;
  outline-offset: 2px !important;
}

/* Screen reader only text */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}