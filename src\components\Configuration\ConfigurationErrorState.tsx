import React from 'react';
import { useConfigurationAssessment } from '../../context/ConfigurationAssessmentContext';
import { configurationAssessmentActions } from '../../context/ConfigurationAssessmentActions';

/**
 * Error state component for Configuration Assessment
 * Displays an error message and provides a retry button
 * 
 * Requirements: 6.4
 */
const ConfigurationErrorState: React.FC = () => {
  const { state, dispatch } = useConfigurationAssessment();
  const { error } = state;
  
  const handleRetry = () => {
    dispatch(configurationAssessmentActions.setLoading(true));
    dispatch(configurationAssessmentActions.setError(null));
    
    // In a real app, this would trigger a new API call
    // For now, we'll just simulate a delay and then clear the error
    setTimeout(() => {
      dispatch(configurationAssessmentActions.setLoading(false));
    }, 1000);
  };
  
  if (!error) return null;
  
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(10, 14, 23, 0.9)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 100,
      backdropFilter: 'blur(2px)',
    }}>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '16px',
        maxWidth: '500px',
        padding: '24px',
        background: 'rgba(16, 24, 45, 0.7)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 50, 50, 0.3)',
      }}>
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#ff3232" strokeWidth="1.5" style={{ marginBottom: '8px' }}>
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
        <h3 style={{ margin: '0 0 8px 0', color: 'white' }}>Error Loading Data</h3>
        <p style={{ margin: '0 0 16px 0', color: 'rgba(255, 255, 255, 0.7)', textAlign: 'center' }}>
          {error}
        </p>
        <button
          onClick={handleRetry}
          style={{
            background: 'rgba(255, 50, 50, 0.2)',
            border: '1px solid rgba(255, 50, 50, 0.5)',
            borderRadius: '4px',
            color: 'white',
            padding: '8px 16px',
            cursor: 'pointer',
            fontWeight: 'bold',
            transition: 'all 0.2s',
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.background = 'rgba(255, 50, 50, 0.3)';
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.background = 'rgba(255, 50, 50, 0.2)';
          }}
        >
          Retry
        </button>
      </div>
    </div>
  );
};

export default ConfigurationErrorState;