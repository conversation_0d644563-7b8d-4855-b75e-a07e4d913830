import { BaseSearchStrategy } from './base-search-strategy';
import { SearchRequest, SearchResponse } from '../interfaces';

/**
 * A sample search strategy that demonstrates how to implement a search strategy
 * for a specific data source.
 * 
 * This strategy generates sample data for testing and development purposes.
 */
export class SampleSearchStrategy extends BaseSearchStrategy {
  /**
   * The unique identifier for this search strategy
   */
  public id = 'sample';
  
  /**
   * Executes a search request against the sample data source
   * @param request The search request to execute
   * @param options Additional options for the search
   * @returns A promise that resolves to the search response
   */
  protected async executeSearch(request: SearchRequest): Promise<SearchResponse> {
    // In a real implementation, this would execute the search against a real data source
    // For now, we'll return some sample data that matches the LogEntry structure

    // Simulate a network delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if we should simulate an error (for testing)
    if (request.query.query.toLowerCase().includes('error')) {
      throw new Error('Simulated error in sample search strategy');
    }

    // Generate sample data based on the request
    const size = request.size || 10;
    const from = request.from || 0;

    // Sample data arrays for realistic log generation
    const sources = ['web-server-01', 'mail-server-01', 'firewall-01', 'workstation-01', 'db-server-01'];
    const levels = ['info', 'warning', 'error', 'critical'];
    const agents = [
      { id: 'agent-001', name: 'web-server-01', ip: '************' },
      { id: 'agent-002', name: 'mail-server-01', ip: '************' },
      { id: 'agent-003', name: 'firewall-01', ip: '************' },
      { id: 'agent-004', name: 'workstation-01', ip: '************' },
      { id: 'agent-005', name: 'db-server-01', ip: '************' }
    ];
    const rules = [
      { id: 5501, description: 'Login session opened', level: 3, groups: ['authentication', 'pci_dss_10.2.5'] },
      { id: 5502, description: 'Login session closed', level: 3, groups: ['authentication', 'pci_dss_10.2.5'] },
      { id: 5503, description: 'User missed the password', level: 5, groups: ['authentication', 'pci_dss_10.2.4'] },
      { id: 5504, description: 'Multiple authentication failures', level: 10, groups: ['authentication', 'pci_dss_10.2.4'] },
      { id: 31001, description: 'Web server 400 error code', level: 5, groups: ['web', 'accesslog'] },
      { id: 31002, description: 'Web server 500 error code', level: 10, groups: ['web', 'accesslog'] },
      { id: 31003, description: 'Web server 200 success code', level: 3, groups: ['web', 'accesslog'] }
    ];
    const locations = ['/var/log/auth.log', '/var/log/apache2/access.log', '/var/log/mail.log', '/var/log/syslog'];
    const decoders = ['sshd', 'apache-accesslog', 'postfix', 'syslog'];

    // Create sample hits with proper LogEntry structure
    const hits = Array.from({ length: size }, (_, i) => {
      const index = i + from;
      const timestamp = new Date();
      timestamp.setMinutes(timestamp.getMinutes() - index); // Stagger timestamps

      const agent = agents[index % agents.length];
      const rule = rules[index % rules.length];
      const level = levels[Math.floor(Math.random() * levels.length)];

      // Generate realistic messages based on rule type
      let message = '';
      if (rule.groups.includes('authentication')) {
        if (rule.description.includes('opened')) {
          message = `Accepted password for admin from ${agent.ip} port 22 ssh2`;
        } else if (rule.description.includes('closed')) {
          message = `pam_unix(sshd:session): session closed for user admin`;
        } else if (rule.description.includes('missed')) {
          message = `Failed password for admin from ${agent.ip} port 22 ssh2`;
        } else {
          message = `Multiple failed login attempts from ${agent.ip}`;
        }
      } else if (rule.groups.includes('web')) {
        const methods = ['GET', 'POST', 'PUT', 'DELETE'];
        const paths = ['/login', '/admin', '/api/users', '/dashboard'];
        const statuses = rule.description.includes('400') ? ['400', '403', '404'] :
                        rule.description.includes('500') ? ['500', '502', '503'] : ['200', '201'];

        const method = methods[Math.floor(Math.random() * methods.length)];
        const path = paths[Math.floor(Math.random() * paths.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        message = `${agent.ip} - - [${timestamp.toISOString()}] "${method} ${path} HTTP/1.1" ${status} ${Math.floor(Math.random() * 10000)}`;
      } else {
        message = `System event on ${agent.name}: ${rule.description}`;
      }

      return {
        _id: `log_${index}_${Date.now()}`,
        _source: {
          timestamp: timestamp.toISOString(),
          source: sources[index % sources.length],
          message: message,
          level: level,
          agent: agent,
          rule: rule,
          location: locations[index % locations.length],
          decoder: {
            name: decoders[index % decoders.length]
          },
          // Additional fields that might be useful
          data: {
            srcip: agent.ip,
            dstport: rule.groups.includes('web') ? 80 : 22,
            protocol: rule.groups.includes('web') ? 'tcp' : 'ssh'
          }
        }
      };
    });

    // Return the sample response
    return {
      hits: {
        total: 100, // Simulate a total of 100 documents
        hits
      },
      took: Math.floor(Math.random() * 100), // Simulate random response time
      timed_out: false
    };
  }
}