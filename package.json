{"name": "admin_panel", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "export-logo": "node scripts/export-logo.js", "export-config-data": "node scripts/export-configuration-data.js"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.7.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "rxjs": "^7.8.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "sharp": "^0.33.5", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}