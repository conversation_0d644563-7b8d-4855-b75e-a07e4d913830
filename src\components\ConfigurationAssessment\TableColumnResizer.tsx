import React, { useState, useCallback, useEffect } from 'react';

interface TableColumnResizerProps {
  index: number;
  onResize: (index: number, width: number) => void;
}

/**
 * Component for resizing table columns
 */
const TableColumnResizer: React.FC<TableColumnResizerProps> = ({ index, onResize }) => {
  const [isDragging, setIsDragging] = useState(false);
  
  // Start dragging
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
    document.body.style.userSelect = 'none';
  }, []);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;

      const column = (e.target as HTMLElement).closest('[data-field]') as HTMLElement;
      if (!column) return;

      const rect = column.getBoundingClientRect();
      const width = e.clientX - rect.left;

      const minWidth = 50;
      if (width >= minWidth) {
        onResize(index, width);
      }
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.body.style.userSelect = '';
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, index, onResize]);
  
  return (
    <div
      role="separator"
      aria-orientation="vertical"
      style={{
        position: 'absolute',
        right: 0,
        top: 0,
        bottom: 0,
        width: '8px',
        cursor: 'col-resize',
        zIndex: 1,
      }}
      onMouseDown={handleMouseDown}
    />
  );
};

export default TableColumnResizer;