// Quick test to verify sample data generation works
import { SampleDataGenerator } from './sampleLogData';
import { DiscoverUtils } from '../utils/discoverUtils';

// Generate a small sample for testing
const testData = SampleDataGenerator.generateSampleData(10);
console.log('Generated sample data:', testData.length, 'entries');
console.log('First entry:', testData[0]);

// Test filtering
const timeRange = DiscoverUtils.getDefaultTimeRange();
const filtered = DiscoverUtils.filterLogData(testData, '', [], timeRange);
console.log('Filtered data:', filtered.length, 'entries');

// Test histogram generation
const histogram = DiscoverUtils.generateHistogramData(filtered, timeRange, 10);
console.log('Histogram buckets:', histogram.length);

// Test field extraction
const fields = DiscoverUtils.getAvailableFields(testData);
console.log('Available fields:', Object.keys(fields));

export { testData };