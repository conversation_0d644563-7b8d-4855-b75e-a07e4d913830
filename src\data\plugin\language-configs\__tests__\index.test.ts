import { registerLanguageConfigs } from '../index';
import { DataPlugin } from '../../data-plugin';
import { simpleQueryLanguage } from '../simple-query-language';

// Mock the DataPlugin
jest.mock('../../data-plugin');

describe('registerLanguageConfigs', () => {
  let mockDataPlugin: jest.Mocked<DataPlugin>;
  let mockLanguageService: { registerLanguage: jest.Mock };
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock language service
    mockLanguageService = {
      registerLanguage: jest.fn()
    };
    
    // Create a mock DataPlugin
    mockDataPlugin = {
      getQueryService: jest.fn().mockReturnValue({
        getLanguageService: jest.fn().mockReturnValue(mockLanguageService)
      })
    } as unknown as jest.Mocked<DataPlugin>;
  });
  
  it('should register all language configurations', () => {
    // Call the function
    registerLanguageConfigs(mockDataPlugin);
    
    // Check that the language service was accessed
    expect(mockDataPlugin.getQueryService).toHaveBeenCalled();
    expect(mockDataPlugin.getQueryService().getLanguageService).toHaveBeenCalled();
    
    // Check that all language configurations were registered
    expect(mockLanguageService.registerLanguage).toHaveBeenCalledTimes(1);
    expect(mockLanguageService.registerLanguage).toHaveBeenCalledWith(simpleQueryLanguage);
  });
});