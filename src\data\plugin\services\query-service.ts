import { BehaviorSubject, Observable } from 'rxjs';
import { 
  QueryService, 
  QueryStringManager, 
  TimeFilterService, 
  FilterManager, 
  DatasetService, 
  LanguageService, 
  Query, 
  TimeRange, 
  Filter 
} from '../interfaces';
import { DatasetServiceImpl } from './dataset-service';
import { LanguageServiceImpl } from './language-service';

/**
 * Implementation of the QueryStringManager interface
 * QueryStringManager is responsible for managing the current query state
 */
class QueryStringManagerImpl implements QueryStringManager {
  private query$: BehaviorSubject<Query>;

  /**
   * Constructor for QueryStringManagerImpl
   * Initializes with a default empty query
   */
  constructor() {
    this.query$ = new BehaviorSubject<Query>({ query: '', language: 'kuery' });
  }

  /**
   * Gets the current query
   * @returns The current query
   */
  public getQuery(): Query {
    return this.query$.getValue();
  }

  /**
   * Sets a new query and notifies subscribers
   * @param query The new query
   */
  public setQuery(query: Query): void {
    this.query$.next(query);
  }

  /**
   * Gets an observable that emits when the query changes
   * @returns An observable of Query
   */
  public getUpdates$(): Observable<Query> {
    return this.query$.asObservable();
  }
}

/**
 * Implementation of the TimeFilterService interface
 * TimeFilterService is responsible for managing the current time range
 */
class TimeFilterServiceImpl implements TimeFilterService {
  private time$: BehaviorSubject<TimeRange>;

  /**
   * Constructor for TimeFilterServiceImpl
   * Initializes with a default time range
   */
  constructor() {
    this.time$ = new BehaviorSubject<TimeRange>({ from: 'now-15m', to: 'now' });
  }

  /**
   * Gets the current time range
   * @returns The current time range
   */
  public getTime(): TimeRange {
    return this.time$.getValue();
  }

  /**
   * Sets a new time range and notifies subscribers
   * @param time The new time range
   */
  public setTime(time: TimeRange): void {
    this.time$.next(time);
  }

  /**
   * Gets an observable that emits when the time range changes
   * @returns An observable of TimeRange
   */
  public getTimeUpdate$(): Observable<TimeRange> {
    return this.time$.asObservable();
  }
}

/**
 * Implementation of the FilterManager interface
 * FilterManager is responsible for managing the current filters
 */
class FilterManagerImpl implements FilterManager {
  private filters$: BehaviorSubject<Filter[]>;

  /**
   * Constructor for FilterManagerImpl
   * Initializes with an empty array of filters
   */
  constructor() {
    this.filters$ = new BehaviorSubject<Filter[]>([]);
  }

  /**
   * Gets the current filters
   * @returns The current filters
   */
  public getFilters(): Filter[] {
    return this.filters$.getValue();
  }

  /**
   * Sets new filters and notifies subscribers
   * @param filters The new filters
   */
  public setFilters(filters: Filter[]): void {
    this.filters$.next(filters);
  }

  /**
   * Adds a new filter to the current filters
   * @param filter The filter to add
   */
  public addFilter(filter: Filter): void {
    const currentFilters = this.getFilters();
    this.setFilters([...currentFilters, filter]);
  }

  /**
   * Removes a filter by field name
   * @param field The field name of the filter to remove
   */
  public removeFilter(field: string): void {
    const currentFilters = this.getFilters();
    this.setFilters(currentFilters.filter(filter => filter.field !== field));
  }

  /**
   * Clears all filters
   */
  public clearFilters(): void {
    this.setFilters([]);
  }

  /**
   * Gets an observable that emits when the filters change
   * @returns An observable of Filter[]
   */
  public getFiltersUpdate$(): Observable<Filter[]> {
    return this.filters$.asObservable();
  }
}

/**
 * Implementation of the QueryService interface
 * QueryService is responsible for managing query state and providing access to related services
 */
export class QueryServiceImpl implements QueryService {
  public readonly queryString: QueryStringManager;
  public readonly timefilter: TimeFilterService;
  public readonly filterManager: FilterManager;
  
  private readonly datasetService: DatasetService;
  private readonly languageService: LanguageService;

  /**
   * Constructor for QueryServiceImpl
   * Initializes all required services
   */
  constructor() {
    this.queryString = new QueryStringManagerImpl();
    this.timefilter = new TimeFilterServiceImpl();
    this.filterManager = new FilterManagerImpl();
    
    // Initialize the dataset and language services
    this.datasetService = new DatasetServiceImpl();
    this.languageService = new LanguageServiceImpl();
  }

  /**
   * Gets the DatasetService instance
   * @returns The DatasetService instance
   */
  public getDatasetService(): DatasetService {
    return this.datasetService;
  }

  /**
   * Gets the LanguageService instance
   * @returns The LanguageService instance
   */
  public getLanguageService(): LanguageService {
    return this.languageService;
  }
}