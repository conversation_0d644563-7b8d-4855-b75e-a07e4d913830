import React, { useState } from 'react';

/**
 * Profile page component.
 * 
 * This component displays the user profile information and settings.
 */
const Profile: React.FC = () => {
  const [activeTab, setActiveTab] = useState('info');
  
  // Sample user data
  const userData = {
    name: 'Security Admin',
    email: '<EMAIL>',
    role: 'Security Administrator',
    department: 'IT Security',
    joinDate: '2024-01-15',
    lastLogin: '2025-07-17T08:45:22',
    twoFactorEnabled: true,
    notifications: {
      email: true,
      push: true,
      slack: false
    },
    recentActivity: [
      { id: 1, action: 'Login', timestamp: '2025-07-17T08:45:22', ip: '*************' },
      { id: 2, action: 'Modified Firewall Rule', timestamp: '2025-07-17T09:12:33', ip: '*************' },
      { id: 3, action: 'Generated Security Report', timestamp: '2025-07-16T15:30:45', ip: '*************' },
      { id: 4, action: 'Added User to Group', timestamp: '2025-07-16T11:22:18', ip: '*************' },
      { id: 5, action: 'Login', timestamp: '2025-07-16T08:50:12', ip: '*************' }
    ],
    accessGroups: [
      'Security Admins',
      'Report Viewers',
      'Configuration Managers',
      'Incident Responders'
    ],
    apiKeys: [
      { id: 'api-key-1', name: 'Dashboard Integration', created: '2025-06-10T14:30:00', lastUsed: '2025-07-17T06:15:33' },
      { id: 'api-key-2', name: 'Reporting Tool', created: '2025-05-22T09:45:12', lastUsed: '2025-07-15T11:20:45' }
    ]
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };
  
  // Format simple date (without time)
  const formatSimpleDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'auto',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '24px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Profile
      </h1>
      
      {/* Profile header */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '24px',
        marginBottom: '32px',
      }}>
        {/* Profile avatar */}
        <div style={{
          width: '100px',
          height: '100px',
          borderRadius: '50%',
          overflow: 'hidden',
          border: '2px solid #00e5ff',
          boxShadow: '0 0 15px rgba(0, 229, 255, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(0, 229, 255, 0.1)',
          color: '#00e5ff',
          fontSize: '36px',
          fontWeight: 'bold',
        }}>
          {userData.name.charAt(0)}
        </div>
        
        {/* Profile info */}
        <div>
          <h2 style={{ 
            fontSize: '24px', 
            fontWeight: 500, 
            color: 'white',
            marginBottom: '8px',
          }}>
            {userData.name}
          </h2>
          <div style={{ 
            fontSize: '16px', 
            color: 'rgba(255, 255, 255, 0.7)',
            marginBottom: '4px',
          }}>
            {userData.role} • {userData.department}
          </div>
          <div style={{ 
            fontSize: '14px', 
            color: 'rgba(255, 255, 255, 0.5)',
          }}>
            Member since {formatSimpleDate(userData.joinDate)}
          </div>
        </div>
        
        {/* Edit profile button */}
        <button style={{
          marginLeft: 'auto',
          background: 'rgba(0, 229, 255, 0.1)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '8px 16px',
          color: '#00e5ff',
          fontSize: '14px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
          </svg>
          Edit Profile
        </button>
      </div>
      
      {/* Profile tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '24px',
      }}>
        {['info', 'activity', 'security', 'api'].map((tab) => (
          <div 
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: '12px 24px',
              cursor: 'pointer',
              color: activeTab === tab ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              borderBottom: activeTab === tab ? '2px solid #00e5ff' : 'none',
              fontWeight: activeTab === tab ? 500 : 400,
              fontSize: '14px',
              textTransform: 'capitalize',
            }}
          >
            {tab === 'info' ? 'Personal Info' : 
             tab === 'activity' ? 'Recent Activity' : 
             tab === 'security' ? 'Security' : 'API Keys'}
          </div>
        ))}
      </div>
      
      {/* Tab content */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '24px',
      }}>
        {/* Personal Info Tab */}
        {activeTab === 'info' && (
          <>
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
              }}>
                Contact Information
              </h3>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}>
                <div>
                  <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.5)', marginBottom: '4px' }}>
                    Email
                  </div>
                  <div style={{ fontSize: '16px', color: 'white' }}>
                    {userData.email}
                  </div>
                </div>
                
                <div>
                  <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.5)', marginBottom: '4px' }}>
                    Department
                  </div>
                  <div style={{ fontSize: '16px', color: 'white' }}>
                    {userData.department}
                  </div>
                </div>
                
                <div>
                  <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.5)', marginBottom: '4px' }}>
                    Role
                  </div>
                  <div style={{ fontSize: '16px', color: 'white' }}>
                    {userData.role}
                  </div>
                </div>
              </div>
            </div>
            
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                Access Groups
                <button style={{
                  background: 'transparent',
                  border: '1px solid rgba(0, 229, 255, 0.3)',
                  borderRadius: '4px',
                  padding: '4px 8px',
                  color: '#00e5ff',
                  fontSize: '12px',
                  cursor: 'pointer',
                }}>
                  Manage
                </button>
              </h3>
              
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px',
              }}>
                {userData.accessGroups.map((group, index) => (
                  <div 
                    key={index}
                    style={{
                      background: 'rgba(0, 229, 255, 0.1)',
                      border: '1px solid rgba(0, 229, 255, 0.2)',
                      borderRadius: '16px',
                      padding: '6px 12px',
                      fontSize: '14px',
                      color: '#00e5ff',
                    }}
                  >
                    {group}
                  </div>
                ))}
              </div>
            </div>
            
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                Notification Preferences
                <button style={{
                  background: 'transparent',
                  border: '1px solid rgba(0, 229, 255, 0.3)',
                  borderRadius: '4px',
                  padding: '4px 8px',
                  color: '#00e5ff',
                  fontSize: '12px',
                  cursor: 'pointer',
                }}>
                  Edit
                </button>
              </h3>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                  <div style={{ fontSize: '14px', color: 'white' }}>
                    Email Notifications
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    background: userData.notifications.email ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    cursor: 'pointer',
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      background: 'white',
                      position: 'absolute',
                      top: '2px',
                      left: userData.notifications.email ? '22px' : '2px',
                      transition: 'left 0.2s ease',
                    }} />
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                  <div style={{ fontSize: '14px', color: 'white' }}>
                    Push Notifications
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    background: userData.notifications.push ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    cursor: 'pointer',
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      background: 'white',
                      position: 'absolute',
                      top: '2px',
                      left: userData.notifications.push ? '22px' : '2px',
                      transition: 'left 0.2s ease',
                    }} />
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                  <div style={{ fontSize: '14px', color: 'white' }}>
                    Slack Notifications
                  </div>
                  <div style={{
                    width: '40px',
                    height: '20px',
                    borderRadius: '10px',
                    background: userData.notifications.slack ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                    position: 'relative',
                    cursor: 'pointer',
                  }}>
                    <div style={{
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      background: 'white',
                      position: 'absolute',
                      top: '2px',
                      left: userData.notifications.slack ? '22px' : '2px',
                      transition: 'left 0.2s ease',
                    }} />
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
        
        {/* Recent Activity Tab */}
        {activeTab === 'activity' && (
          <div style={{
            gridColumn: '1 / -1',
            borderRadius: '12px',
            background: 'rgba(16, 24, 45, 0.7)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
            padding: '24px',
          }}>
            <h3 style={{ 
              fontSize: '18px', 
              fontWeight: 500, 
              color: '#00e5ff',
              marginBottom: '16px',
            }}>
              Recent Activity
            </h3>
            
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
            }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Action
                  </th>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Time
                  </th>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    IP Address
                  </th>
                </tr>
              </thead>
              <tbody>
                {userData.recentActivity.map((activity, index) => (
                  <tr 
                    key={activity.id}
                    style={{
                      borderBottom: index < userData.recentActivity.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                    }}
                  >
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'white',
                    }}>
                      {activity.action}
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      {formatDate(activity.timestamp)}
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      {activity.ip}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            
            <div style={{
              marginTop: '16px',
              textAlign: 'center',
            }}>
              <button style={{
                background: 'transparent',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '8px 16px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                View Full Activity Log
              </button>
            </div>
          </div>
        )}
        
        {/* Security Tab */}
        {activeTab === 'security' && (
          <>
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
              }}>
                Password
              </h3>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '16px',
              }}>
                <div>
                  <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.5)', marginBottom: '4px' }}>
                    Last Changed
                  </div>
                  <div style={{ fontSize: '16px', color: 'white' }}>
                    30 days ago
                  </div>
                </div>
                
                <button style={{
                  background: 'rgba(0, 229, 255, 0.1)',
                  border: '1px solid rgba(0, 229, 255, 0.3)',
                  borderRadius: '4px',
                  padding: '8px 16px',
                  color: '#00e5ff',
                  fontSize: '14px',
                  cursor: 'pointer',
                  alignSelf: 'flex-start',
                }}>
                  Change Password
                </button>
              </div>
            </div>
            
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
              }}>
                Two-Factor Authentication
                <div style={{
                  width: '40px',
                  height: '20px',
                  borderRadius: '10px',
                  background: userData.twoFactorEnabled ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                  position: 'relative',
                  cursor: 'pointer',
                }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: userData.twoFactorEnabled ? '22px' : '2px',
                    transition: 'left 0.2s ease',
                  }} />
                </div>
              </h3>
              
              <div style={{
                fontSize: '14px',
                color: 'rgba(255, 255, 255, 0.7)',
                marginBottom: '16px',
                lineHeight: '1.5',
              }}>
                Two-factor authentication adds an extra layer of security to your account by requiring more than just a password to sign in.
              </div>
              
              {userData.twoFactorEnabled ? (
                <button style={{
                  background: 'transparent',
                  border: '1px solid rgba(255, 99, 132, 0.5)',
                  borderRadius: '4px',
                  padding: '8px 16px',
                  color: 'rgba(255, 99, 132, 1)',
                  fontSize: '14px',
                  cursor: 'pointer',
                }}>
                  Disable Two-Factor Authentication
                </button>
              ) : (
                <button style={{
                  background: 'rgba(0, 229, 255, 0.1)',
                  border: '1px solid rgba(0, 229, 255, 0.3)',
                  borderRadius: '4px',
                  padding: '8px 16px',
                  color: '#00e5ff',
                  fontSize: '14px',
                  cursor: 'pointer',
                }}>
                  Enable Two-Factor Authentication
                </button>
              )}
            </div>
            
            <div style={{
              borderRadius: '12px',
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
              padding: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
                marginBottom: '16px',
              }}>
                Sessions
              </h3>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '12px',
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: '12px',
                  background: 'rgba(0, 229, 255, 0.05)',
                  borderRadius: '8px',
                  border: '1px solid rgba(0, 229, 255, 0.1)',
                }}>
                  <div>
                    <div style={{ fontSize: '14px', color: 'white', marginBottom: '4px' }}>
                      Current Session
                    </div>
                    <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>
                      Windows • Chrome • *************
                    </div>
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: '#00e5ff',
                    background: 'rgba(0, 229, 255, 0.1)',
                    padding: '4px 8px',
                    borderRadius: '4px',
                  }}>
                    Active Now
                  </div>
                </div>
              </div>
              
              <button style={{
                background: 'transparent',
                border: '1px solid rgba(255, 99, 132, 0.5)',
                borderRadius: '4px',
                padding: '8px 16px',
                color: 'rgba(255, 99, 132, 1)',
                fontSize: '14px',
                cursor: 'pointer',
                marginTop: '16px',
              }}>
                Sign Out All Other Sessions
              </button>
            </div>
          </>
        )}
        
        {/* API Keys Tab */}
        {activeTab === 'api' && (
          <div style={{
            gridColumn: '1 / -1',
            borderRadius: '12px',
            background: 'rgba(16, 24, 45, 0.7)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
            padding: '24px',
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '24px',
            }}>
              <h3 style={{ 
                fontSize: '18px', 
                fontWeight: 500, 
                color: '#00e5ff',
              }}>
                API Keys
              </h3>
              
              <button style={{
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '8px 16px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
              }}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Create New API Key
              </button>
            </div>
            
            <div style={{
              fontSize: '14px',
              color: 'rgba(255, 255, 255, 0.7)',
              marginBottom: '16px',
              lineHeight: '1.5',
            }}>
              API keys allow external applications to authenticate with the GuardBear API. Keys should be kept secure and never shared.
            </div>
            
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
            }}>
              <thead>
                <tr style={{
                  borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                }}>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Name
                  </th>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Created
                  </th>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'left',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Last Used
                  </th>
                  <th style={{
                    padding: '12px 16px',
                    textAlign: 'right',
                    fontSize: '14px',
                    fontWeight: 500,
                    color: 'rgba(255, 255, 255, 0.7)',
                  }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {userData.apiKeys.map((key, index) => (
                  <tr 
                    key={key.id}
                    style={{
                      borderBottom: index < userData.apiKeys.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                    }}
                  >
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'white',
                    }}>
                      {key.name}
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      {formatDate(key.created)}
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      fontSize: '14px',
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      {formatDate(key.lastUsed)}
                    </td>
                    <td style={{
                      padding: '12px 16px',
                      textAlign: 'right',
                    }}>
                      <button style={{
                        background: 'transparent',
                        border: '1px solid rgba(255, 99, 132, 0.5)',
                        borderRadius: '4px',
                        padding: '4px 8px',
                        color: 'rgba(255, 99, 132, 1)',
                        fontSize: '12px',
                        cursor: 'pointer',
                      }}>
                        Revoke
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;