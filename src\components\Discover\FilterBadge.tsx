import React, { useState, useRef } from 'react';
import { Filter } from '../../types/discover';

interface FilterBadgeProps {
  filter: Filter;
  onRemove: (field: string) => void;
  onEdit: (filter: Filter) => void;
}

/**
 * Component that displays a filter badge with edit functionality
 */
const FilterBadge: React.FC<FilterBadgeProps> = ({ filter, onRemove, onEdit }) => {
  const [isEditMenuOpen, setIsEditMenuOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  
  // Available operators for editing
  const operators = [
    { label: 'is', value: 'is' },
    { label: 'is not', value: 'is not' },
    { label: 'exists', value: 'exists' },
    { label: 'does not exist', value: 'does not exist' },
    { label: 'contains', value: 'contains' },
  ];
  
  // Handle operator change
  const handleOperatorChange = (operator: string) => {
    onEdit({
      ...filter,
      operator: operator as Filter['operator'],
    });
    setIsEditMenuOpen(false);
  };
  
  return (
    <div style={{
      position: 'relative',
    }}>
      <div 
        onClick={() => setIsEditMenuOpen(!isEditMenuOpen)}
        style={{
          display: 'flex',
          alignItems: 'center',
          background: 'rgba(0, 229, 255, 0.1)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '4px 8px',
          fontSize: '12px',
          color: 'white',
          cursor: 'pointer',
        }}
      >
        <span style={{ fontWeight: 'bold', marginRight: '4px' }}>
          {filter.field}:
        </span>
        <span style={{ marginRight: '8px' }}>
          {filter.operator === 'is' ? '' : filter.operator + ' '}
          {String(filter.value)}
        </span>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove(filter.field);
          }}
          style={{
            background: 'transparent',
            border: 'none',
            color: 'rgba(255, 255, 255, 0.7)',
            cursor: 'pointer',
            padding: '0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* Edit menu */}
      {isEditMenuOpen && (
        <div 
          ref={menuRef}
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            marginTop: '4px',
            background: 'rgba(10, 14, 23, 0.95)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '4px',
            padding: '8px 0',
            zIndex: 10,
            minWidth: '150px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
          }}
        >
          <div style={{ 
            padding: '4px 8px', 
            fontSize: '12px',
            color: 'rgba(255, 255, 255, 0.7)',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
            marginBottom: '4px',
          }}>
            Change operator
          </div>
          
          {operators.map(op => (
            <div 
              key={op.value}
              onClick={() => handleOperatorChange(op.value)}
              style={{
                padding: '6px 12px',
                cursor: 'pointer',
                color: 'white',
                fontSize: '12px',
                background: filter.operator === op.value 
                  ? 'rgba(0, 229, 255, 0.1)' 
                  : 'transparent',
                transition: 'background-color 0.2s',
              }}
              onMouseOver={(e) => {
                if (filter.operator !== op.value) {
                  e.currentTarget.style.background = 'rgba(0, 229, 255, 0.05)';
                }
              }}
              onMouseOut={(e) => {
                if (filter.operator !== op.value) {
                  e.currentTarget.style.background = 'transparent';
                }
              }}
            >
              {op.label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FilterBadge;