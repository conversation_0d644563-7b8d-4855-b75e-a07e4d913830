import { QueryServiceImpl } from '../query-service';
import { Query } from '../../interfaces';

describe('QueryService', () => {
  let queryService: QueryServiceImpl;

  beforeEach(() => {
    queryService = new QueryServiceImpl();
  });

  describe('QueryStringManager', () => {
    it('should initialize with default query', () => {
      const query = queryService.queryString.getQuery();
      expect(query).toEqual({ query: '', language: 'kuery' });
    });

    it('should update query when setQuery is called', () => {
      const newQuery: Query = { query: 'test query', language: 'lucene' };
      queryService.queryString.setQuery(newQuery);
      const query = queryService.queryString.getQuery();
      expect(query).toEqual(newQuery);
    });

    it('should emit updates when query changes', (done) => {
      const newQuery: Query = { query: 'test query', language: 'lucene' };
      
      queryService.queryString.getUpdates$().subscribe((query) => {
        if (query.query === 'test query') {
          expect(query).toEqual(newQuery);
          done();
        }
      });
      
      queryService.queryString.setQuery(newQuery);
    });
  });

  describe('TimeFilterService', () => {
    it('should initialize with default time range', () => {
      const time = queryService.timefilter.getTime();
      expect(time).toEqual({ from: 'now-15m', to: 'now' });
    });

    it('should update time range when setTime is called', () => {
      const newTime = { from: 'now-1h', to: 'now' };
      queryService.timefilter.setTime(newTime);
      const time = queryService.timefilter.getTime();
      expect(time).toEqual(newTime);
    });
  });

  describe('FilterManager', () => {
    it('should initialize with empty filters', () => {
      const filters = queryService.filterManager.getFilters();
      expect(filters).toEqual([]);
    });

    it('should update filters when setFilters is called', () => {
      const newFilters = [{ field: 'test', operator: 'is', value: 'value' }];
      queryService.filterManager.setFilters(newFilters);
      const filters = queryService.filterManager.getFilters();
      expect(filters).toEqual(newFilters);
    });

    it('should add a filter when addFilter is called', () => {
      const filter = { field: 'test', operator: 'is', value: 'value' };
      queryService.filterManager.addFilter(filter);
      const filters = queryService.filterManager.getFilters();
      expect(filters).toEqual([filter]);
    });

    it('should remove a filter when removeFilter is called', () => {
      const filter = { field: 'test', operator: 'is', value: 'value' };
      queryService.filterManager.addFilter(filter);
      queryService.filterManager.removeFilter('test');
      const filters = queryService.filterManager.getFilters();
      expect(filters).toEqual([]);
    });

    it('should clear all filters when clearFilters is called', () => {
      const filter1 = { field: 'test1', operator: 'is', value: 'value1' };
      const filter2 = { field: 'test2', operator: 'is', value: 'value2' };
      queryService.filterManager.addFilter(filter1);
      queryService.filterManager.addFilter(filter2);
      queryService.filterManager.clearFilters();
      const filters = queryService.filterManager.getFilters();
      expect(filters).toEqual([]);
    });
  });

  describe('Service Access', () => {
    it('should provide access to DatasetService', () => {
      const datasetService = queryService.getDatasetService();
      expect(datasetService).toBeDefined();
    });

    it('should provide access to LanguageService', () => {
      const languageService = queryService.getLanguageService();
      expect(languageService).toBeDefined();
    });
  });
});