import React, { useState } from 'react';
import ConfigurationSidebar from './ConfigurationSidebar';
import ConfigurationMainContent from './ConfigurationMainContent';

/**
 * Main layout component for the Configuration Assessment page
 * Provides the overall structure with sidebar and main content
 * 
 * Requirements: 1.1, 6.1
 */
const ConfigurationLayout: React.FC = () => {
  // State for sidebar collapse
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Toggle sidebar collapse
  const toggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };
  
  return (
    <div style={{
      display: 'flex',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
    }}>
      {/* Sidebar */}
      <ConfigurationSidebar 
        isCollapsed={isSidebarCollapsed} 
        onToggleCollapse={toggleSidebar} 
      />
      
      {/* Main content */}
      <ConfigurationMainContent 
        isSidebarCollapsed={isSidebarCollapsed}
      />
    </div>
  );
};

export default ConfigurationLayout;