import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { DataPluginProvider, useDataPlugin } from '../DataPluginContext';
import { DataPlugin } from '../../data/plugin/data-plugin';
import { initializeDataPlugin } from '../../data/plugin/initialize';

// Mock the DataPlugin and initializeDataPlugin
jest.mock('../../data/plugin/data-plugin');
jest.mock('../../data/plugin/initialize');

// Test component that uses the DataPlugin context
const TestComponent: React.FC = () => {
  const dataPlugin = useDataPlugin();
  return <div>DataPlugin initialized: {dataPlugin ? 'Yes' : 'No'}</div>;
};

describe('DataPluginContext', () => {
  let mockDataPlugin: jest.Mocked<DataPlugin>;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock DataPlugin
    mockDataPlugin = {
      initialize: jest.fn().mockResolvedValue(undefined),
      getSearchService: jest.fn(),
      getQueryService: jest.fn(),
      getUiService: jest.fn(),
      getFieldFormatsService: jest.fn(),
      getAutocompleteService: jest.fn(),
      getStorage: jest.fn(),
      getIndexPatternService: jest.fn()
    } as unknown as jest.Mocked<DataPlugin>;
    
    // Mock the getInstance method
    (DataPlugin.getInstance as jest.Mock).mockReturnValue(mockDataPlugin);
    
    // Mock initializeDataPlugin
    (initializeDataPlugin as jest.Mock).mockResolvedValue(undefined);
  });
  
  it('should initialize the DataPlugin and provide it to children', async () => {
    render(
      <DataPluginProvider>
        <TestComponent />
      </DataPluginProvider>
    );
    
    // Check that the loading state is shown
    expect(screen.getByText('Initializing data services...')).toBeInTheDocument();
    
    // Wait for initialization to complete
    await waitFor(() => {
      expect(screen.getByText('DataPlugin initialized: Yes')).toBeInTheDocument();
    });
    
    // Check that initializeDataPlugin was called
    expect(initializeDataPlugin).toHaveBeenCalled();
  });
  
  it('should handle initialization errors', async () => {
    // Mock an error during initialization
    (initializeDataPlugin as jest.Mock).mockRejectedValue(new Error('Initialization error'));
    
    // Mock console.error to prevent test output noise
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <DataPluginProvider>
        <TestComponent />
      </DataPluginProvider>
    );
    
    // Check that the loading state is shown
    expect(screen.getByText('Initializing data services...')).toBeInTheDocument();
    
    // Wait for the error to be logged
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith('Failed to initialize DataPlugin:', expect.any(Error));
    });
    
    // The loading state should still be shown
    expect(screen.getByText('Initializing data services...')).toBeInTheDocument();
    
    // Restore console.error
    jest.restoreAllMocks();
  });
  
  it('should throw an error when useDataPlugin is used outside of DataPluginProvider', () => {
    // Mock console.error to prevent test output noise
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Expect the render to throw an error
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useDataPlugin must be used within a DataPluginProvider');
    
    // Restore console.error
    jest.restoreAllMocks();
  });
});