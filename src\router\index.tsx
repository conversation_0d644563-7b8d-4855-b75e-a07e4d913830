import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { ROUTES } from './constants';

// Import layout component
import AppLayout from '../layouts/AppLayout';

// Import common components
import LoadingIndicator from '../components/common/LoadingIndicator';
import ErrorBoundary from '../components/common/ErrorBoundary';

// Lazy-loaded page components for better performance
// These will be fully implemented in task 7.1
const Dashboard = lazy(() => import('../pages/Dashboard'));
const Explore = lazy(() => import('../pages/explore'));
const Discover = lazy(() => import('../pages/explore/Discover'));
const Dashboards = lazy(() => import('../pages/explore/Dashboards'));
const Visualize = lazy(() => import('../pages/explore/Visualize'));
const Reporting = lazy(() => import('../pages/explore/Reporting'));
const Alerting = lazy(() => import('../pages/explore/Alerting'));
const Maps = lazy(() => import('../pages/explore/Maps'));
const Notifications = lazy(() => import('../pages/explore/Notifications'));
const GridView = lazy(() => import('../pages/GridView'));
const History = lazy(() => import('../pages/History'));
const Files = lazy(() => import('../pages/Files'));
const Profile = lazy(() => import('../pages/Profile'));
const Settings = lazy(() => import('../pages/Settings'));
const NotFound = lazy(() => import('../pages/NotFound'));
const ConfigurationAssessment = lazy(() => import('../pages/endpoint-security/ConfigurationAssessment'));
const MalwareDetection = lazy(() => import('../pages/endpoint-security/MalwareDetection'));
const FileIntegrityMonitoring = lazy(() => import('../pages/endpoint-security/FileIntegrityMonitoring'));

/**
 * Main Router component for the GuardBear application.
 * 
 * This component sets up the complete routing structure using React Router v6,
 * including all navigation items and nested routes for the explore section.
 */
const Router = () => {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Suspense fallback={<LoadingIndicator />}>
          <Routes>
            <Route path="/" element={<AppLayout />}>
              {/* Main routes */}
              <Route index element={<Dashboard />} />
              
              {/* Explore section with nested routes */}
              <Route path={ROUTES.EXPLORE}>
                <Route index element={<Explore />} />
                <Route path="discover" element={<Discover />} />
                <Route path="dashboards" element={<Dashboards />} />
                <Route path="visualize" element={<Visualize />} />
                <Route path="reporting" element={<Reporting />} />
                <Route path="alerting" element={<Alerting />} />
                <Route path="maps" element={<Maps />} />
                <Route path="notifications" element={<Notifications />} />
              </Route>
              
              {/* Endpoint Security section as top-level routes */}
              <Route path={ROUTES.ENDPOINT_SECURITY_CONFIGURATION} element={<ConfigurationAssessment />} />
              <Route path={ROUTES.ENDPOINT_SECURITY_MALWARE} element={<MalwareDetection />} />
              <Route path={ROUTES.ENDPOINT_SECURITY_FILE_INTEGRITY} element={<FileIntegrityMonitoring />} />
              
              {/* Other main navigation items */}
              <Route path={ROUTES.GRID} element={<GridView />} />
              <Route path={ROUTES.HISTORY} element={<History />} />
              <Route path={ROUTES.FILES} element={<Files />} />
              <Route path={ROUTES.PROFILE} element={<Profile />} />
              <Route path={ROUTES.SETTINGS} element={<Settings />} />
              
              {/* 404 Not Found route */}
              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ErrorBoundary>
  );
};

export default Router;