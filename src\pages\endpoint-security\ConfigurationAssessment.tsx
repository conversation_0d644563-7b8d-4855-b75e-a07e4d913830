import React from 'react';
import { ConfigurationAssessmentProvider } from '../../context/ConfigurationAssessmentContext';
import { ConfigurationLayout } from '../../components/Configuration';

/**
 * Configuration Assessment component
 * 
 * This component displays the Configuration Assessment dashboard using configuration-specific
 * components and layout but with a similar UI/UX as the Discover page.
 * 
 * Requirements: 1.1, 6.1
 */
const ConfigurationAssessment: React.FC = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
    }}>
      <ConfigurationAssessmentProvider>
        <ConfigurationLayout />
      </ConfigurationAssessmentProvider>
    </div>
  );
};

export default ConfigurationAssessment;
