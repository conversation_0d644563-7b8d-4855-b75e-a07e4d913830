import React, { useState } from 'react';

/**
 * Alerting page component.
 * 
 * This component renders the Alerting page content from the Explore section.
 */
const Alerting: React.FC = () => {
  const [activeTab, setActiveTab] = useState('active');
  
  // Sample alert data
  const alerts = {
    active: [
      { id: 'ALT-1023', severity: 'critical', title: 'Ransomware Activity Detected', time: '10 minutes ago', source: 'EDR' },
      { id: 'ALT-1022', severity: 'high', title: 'Multiple Failed Login Attempts', time: '25 minutes ago', source: 'SIEM' },
      { id: 'ALT-1021', severity: 'medium', title: 'Unusual Network Traffic Pattern', time: '1 hour ago', source: 'IDS' },
      { id: 'ALT-1020', severity: 'low', title: 'New Device Connected', time: '2 hours ago', source: 'NAC' },
    ],
    resolved: [
      { id: 'ALT-1019', severity: 'high', title: 'Suspicious File Execution', time: '5 hours ago', source: 'EDR' },
      { id: 'ALT-1018', severity: 'medium', title: 'Outdated Software Detected', time: '1 day ago', source: 'Vulnerability Scanner' },
      { id: 'ALT-1017', severity: 'critical', title: 'Data Exfiltration Attempt', time: '2 days ago', source: 'DLP' },
    ],
    snoozed: [
      { id: 'ALT-1016', severity: 'low', title: 'SSL Certificate Expiring Soon', time: '3 days ago', source: 'Certificate Monitor' },
      { id: 'ALT-1015', severity: 'medium', title: 'Unusual User Behavior', time: '4 days ago', source: 'UEBA' },
    ]
  };
  
  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'rgba(255, 0, 0, 0.8)';
      case 'high':
        return 'rgba(255, 99, 71, 0.8)';
      case 'medium':
        return 'rgba(255, 165, 0, 0.8)';
      case 'low':
        return 'rgba(255, 255, 0, 0.6)';
      default:
        return 'rgba(255, 255, 255, 0.6)';
    }
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Alerting
      </h1>
      
      {/* Alert summary */}
      <div style={{
        display: 'flex',
        gap: '16px',
        marginBottom: '24px',
      }}>
        <div style={{
          flex: 1,
          borderRadius: '8px',
          padding: '16px',
          background: 'rgba(255, 0, 0, 0.1)',
          border: '1px solid rgba(255, 0, 0, 0.2)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: 'rgba(255, 0, 0, 0.8)' }}>1</div>
          <div style={{ fontSize: '14px', color: 'white' }}>Critical</div>
        </div>
        <div style={{
          flex: 1,
          borderRadius: '8px',
          padding: '16px',
          background: 'rgba(255, 99, 71, 0.1)',
          border: '1px solid rgba(255, 99, 71, 0.2)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: 'rgba(255, 99, 71, 0.8)' }}>1</div>
          <div style={{ fontSize: '14px', color: 'white' }}>High</div>
        </div>
        <div style={{
          flex: 1,
          borderRadius: '8px',
          padding: '16px',
          background: 'rgba(255, 165, 0, 0.1)',
          border: '1px solid rgba(255, 165, 0, 0.2)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: 'rgba(255, 165, 0, 0.8)' }}>1</div>
          <div style={{ fontSize: '14px', color: 'white' }}>Medium</div>
        </div>
        <div style={{
          flex: 1,
          borderRadius: '8px',
          padding: '16px',
          background: 'rgba(255, 255, 0, 0.1)',
          border: '1px solid rgba(255, 255, 0, 0.2)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}>
          <div style={{ fontSize: '24px', fontWeight: 'bold', color: 'rgba(255, 255, 0, 0.6)' }}>1</div>
          <div style={{ fontSize: '14px', color: 'white' }}>Low</div>
        </div>
      </div>
      
      {/* Tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '16px',
      }}>
        {['active', 'resolved', 'snoozed'].map((tab) => (
          <div 
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: '12px 24px',
              cursor: 'pointer',
              color: activeTab === tab ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              borderBottom: activeTab === tab ? '2px solid #00e5ff' : 'none',
              fontWeight: activeTab === tab ? 500 : 400,
              fontSize: '14px',
              textTransform: 'capitalize',
            }}
          >
            {tab} ({alerts[tab as keyof typeof alerts].length})
          </div>
        ))}
      </div>
      
      {/* Alert list */}
      <div style={{
        borderRadius: '12px',
        background: 'rgba(16, 24, 45, 0.7)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        overflow: 'hidden',
      }}>
        {alerts[activeTab as keyof typeof alerts].map((alert, index) => (
          <div 
            key={alert.id}
            style={{
              padding: '16px',
              borderBottom: index < alerts[activeTab as keyof typeof alerts].length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              cursor: 'pointer',
              transition: 'background 0.2s ease',
              ':hover': {
                background: 'rgba(0, 229, 255, 0.05)',
              },
            }}
          >
            <div style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: getSeverityColor(alert.severity),
              boxShadow: `0 0 8px ${getSeverityColor(alert.severity)}`,
            }} />
            
            <div style={{ flex: 1 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ fontSize: '16px', color: 'white', fontWeight: 500 }}>{alert.title}</div>
                <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>{alert.id}</div>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '4px' }}>
                <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.7)' }}>Source: {alert.source}</div>
                <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>{alert.time}</div>
              </div>
            </div>
            
            <div>
              <button style={{
                background: 'rgba(0, 229, 255, 0.1)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px',
                padding: '6px 12px',
                color: '#00e5ff',
                fontSize: '14px',
                cursor: 'pointer',
              }}>
                {activeTab === 'active' ? 'Investigate' : 'View Details'}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Alerting;