import React, { useState } from 'react';

/**
 * History page component.
 * 
 * This component displays a history of security events and activities.
 */
const History: React.FC = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [timeRange, setTimeRange] = useState('24h');
  
  // Sample history data
  const historyItems = [
    {
      id: 'H1001',
      type: 'alert',
      title: 'Ransomware Activity Detected',
      description: 'Suspicious encryption activity detected on endpoint DEV-WS-42',
      timestamp: '2025-07-17T10:23:45',
      user: 'System',
      status: 'critical'
    },
    {
      id: 'H1002',
      type: 'login',
      title: 'Admin Login',
      description: 'Administrator logged in from *************',
      timestamp: '2025-07-17T09:45:12',
      user: '<EMAIL>',
      status: 'normal'
    },
    {
      id: 'H1003',
      type: 'config',
      title: 'Firewall Rule Modified',
      description: 'Firewall rule #42 was updated to allow traffic on port 8080',
      timestamp: '2025-07-17T09:12:33',
      user: '<EMAIL>',
      status: 'normal'
    },
    {
      id: 'H1004',
      type: 'scan',
      title: 'Vulnerability Scan Completed',
      description: 'Weekly vulnerability scan completed with 3 findings',
      timestamp: '2025-07-17T08:30:00',
      user: 'System',
      status: 'warning'
    },
    {
      id: 'H1005',
      type: 'alert',
      title: 'Multiple Failed Login Attempts',
      description: '5 failed login attempts for user jsmith from IP ************',
      timestamp: '2025-07-17T07:15:22',
      user: 'System',
      status: 'warning'
    },
    {
      id: 'H1006',
      type: 'system',
      title: 'System Update',
      description: 'Security patch KB4023057 was installed',
      timestamp: '2025-07-17T06:45:10',
      user: 'System',
      status: 'normal'
    },
    {
      id: 'H1007',
      type: 'config',
      title: 'User Added to Group',
      description: 'User mwilson was added to Security Admins group',
      timestamp: '2025-07-17T05:30:45',
      user: '<EMAIL>',
      status: 'normal'
    },
    {
      id: 'H1008',
      type: 'alert',
      title: 'Unusual Network Traffic',
      description: 'Unusual outbound traffic detected to IP ************** on port 445',
      timestamp: '2025-07-16T23:12:18',
      user: 'System',
      status: 'critical'
    },
    {
      id: 'H1009',
      type: 'backup',
      title: 'Backup Completed',
      description: 'System backup completed successfully',
      timestamp: '2025-07-16T22:00:00',
      user: 'System',
      status: 'normal'
    },
    {
      id: 'H1010',
      type: 'login',
      title: 'User Login',
      description: 'User jsmith logged in from *************',
      timestamp: '2025-07-16T17:45:33',
      user: '<EMAIL>',
      status: 'normal'
    }
  ];
  
  // Filter history items
  const filteredHistory = historyItems.filter(item => {
    if (activeFilter === 'all') return true;
    return item.type === activeFilter;
  });
  
  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical':
        return '#ff4d4d';
      case 'warning':
        return '#ffcc00';
      case 'normal':
        return '#00e5ff';
      default:
        return 'rgba(255, 255, 255, 0.7)';
    }
  };
  
  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        );
      case 'login':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
            <polyline points="10 17 15 12 10 7"></polyline>
            <line x1="15" y1="12" x2="3" y2="12"></line>
          </svg>
        );
      case 'config':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
        );
      case 'scan':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M2 12h6"></path>
            <path d="M22 12h-6"></path>
            <path d="M12 2v6"></path>
            <path d="M12 22v-6"></path>
            <path d="M20 16l-4-4 4-4"></path>
            <path d="M4 8l4 4-4 4"></path>
            <path d="M16 4l-4 4-4-4"></path>
            <path d="M8 20l4-4 4 4"></path>
          </svg>
        );
      case 'system':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
            <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
            <line x1="6" y1="6" x2="6.01" y2="6"></line>
            <line x1="6" y1="18" x2="6.01" y2="18"></line>
          </svg>
        );
      case 'backup':
        return (
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
            <polyline points="17 8 12 3 7 8"></polyline>
            <line x1="12" y1="3" x2="12" y2="15"></line>
          </svg>
        );
      default:
        return null;
    }
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'auto',
      position: 'relative',
      padding: '24px',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
      }}>
        <h1 style={{ 
          fontSize: '28px', 
          fontWeight: 600,
          background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
        }}>
          History
        </h1>
        
        <div style={{
          display: 'flex',
          gap: '12px',
        }}>
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            style={{
              background: 'rgba(16, 24, 45, 0.7)',
              backdropFilter: 'blur(10px)',
              WebkitBackdropFilter: 'blur(10px)',
              border: '1px solid rgba(0, 229, 255, 0.2)',
              borderRadius: '4px',
              padding: '8px 12px',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer',
            }}
          >
            <option value="24h">Last 24 hours</option>
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="custom">Custom range</option>
          </select>
          
          <button style={{
            background: 'rgba(0, 229, 255, 0.1)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '4px',
            padding: '8px 12px',
            color: '#00e5ff',
            fontSize: '14px',
            cursor: 'pointer',
          }}>
            Export
          </button>
        </div>
      </div>
      
      {/* Filter tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '16px',
        overflowX: 'auto',
      }}>
        {['all', 'alert', 'login', 'config', 'scan', 'system', 'backup'].map((filter) => (
          <div 
            key={filter}
            onClick={() => setActiveFilter(filter)}
            style={{
              padding: '12px 24px',
              cursor: 'pointer',
              color: activeFilter === filter ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              borderBottom: activeFilter === filter ? '2px solid #00e5ff' : 'none',
              fontWeight: activeFilter === filter ? 500 : 400,
              fontSize: '14px',
              textTransform: 'capitalize',
              whiteSpace: 'nowrap',
            }}
          >
            {filter}
          </div>
        ))}
      </div>
      
      {/* History list */}
      <div style={{
        borderRadius: '12px',
        background: 'rgba(16, 24, 45, 0.7)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        overflow: 'hidden',
      }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
        }}>
          <thead>
            <tr style={{
              borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
            }}>
              <th style={{
                padding: '16px',
                textAlign: 'left',
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Type
              </th>
              <th style={{
                padding: '16px',
                textAlign: 'left',
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Event
              </th>
              <th style={{
                padding: '16px',
                textAlign: 'left',
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                User
              </th>
              <th style={{
                padding: '16px',
                textAlign: 'left',
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Time
              </th>
              <th style={{
                padding: '16px',
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: 500,
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            {filteredHistory.map((item, index) => (
              <tr 
                key={item.id}
                style={{
                  borderBottom: index < filteredHistory.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                  cursor: 'pointer',
                  transition: 'background 0.2s ease',
                  ':hover': {
                    background: 'rgba(0, 229, 255, 0.05)',
                  },
                }}
              >
                <td style={{
                  padding: '16px',
                  color: getStatusColor(item.status),
                  textTransform: 'capitalize',
                }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}>
                    {getTypeIcon(item.type)}
                    <span>{item.type}</span>
                  </div>
                </td>
                <td style={{
                  padding: '16px',
                }}>
                  <div style={{ fontSize: '14px', color: 'white', marginBottom: '4px' }}>
                    {item.title}
                  </div>
                  <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>
                    {item.description}
                  </div>
                </td>
                <td style={{
                  padding: '16px',
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.7)',
                }}>
                  {item.user}
                </td>
                <td style={{
                  padding: '16px',
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.7)',
                }}>
                  {formatTimestamp(item.timestamp)}
                </td>
                <td style={{
                  padding: '16px',
                  textAlign: 'center',
                }}>
                  <div style={{
                    display: 'inline-block',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    fontWeight: 500,
                    color: getStatusColor(item.status),
                    background: `${getStatusColor(item.status)}20`,
                    border: `1px solid ${getStatusColor(item.status)}40`,
                    textTransform: 'capitalize',
                  }}>
                    {item.status}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: '16px',
      }}>
        <div style={{
          fontSize: '14px',
          color: 'rgba(255, 255, 255, 0.5)',
        }}>
          Showing 1-10 of 42 items
        </div>
        
        <div style={{
          display: 'flex',
          gap: '8px',
        }}>
          {['1', '2', '3', '4', '5'].map((page, index) => (
            <button 
              key={page}
              style={{
                width: '32px',
                height: '32px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '4px',
                background: index === 0 ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: index === 0 ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(255, 255, 255, 0.2)',
                color: index === 0 ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
                fontSize: '14px',
                cursor: 'pointer',
              }}
            >
              {page}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default History;