import { useCallback } from 'react';
import { useConfigurationAssessment } from '../context/ConfigurationAssessmentContext';
import { configurationAssessmentActions } from '../context/ConfigurationAssessmentActions';

/**
 * Hook for managing time range and refresh functionality for configuration assessment
 * 
 * Requirements: 2.1, 2.2
 */
export const useConfigurationTime = () => {
  const { state, dispatch } = useConfigurationAssessment();
  const { timeRange, autoRefresh, refreshInterval } = state;
  
  // Time range options
  const timeRangeOptions = [
    { value: 'last-15m', label: 'Last 15 minutes' },
    { value: 'last-30m', label: 'Last 30 minutes' },
    { value: 'last-1h', label: 'Last 1 hour' },
    { value: 'last-24h', label: 'Last 24 hours' },
    { value: 'last-7d', label: 'Last 7 days' },
    { value: 'last-30d', label: 'Last 30 days' },
    { value: 'last-90d', label: 'Last 90 days' },
    { value: 'last-1y', label: 'Last 1 year' },
    { value: 'custom', label: 'Custom range' },
  ];
  
  // Set time range by preset
  const setTimeRangePreset = useCallback((preset: string) => {
    const now = new Date();
    let start = new Date();
    
    switch (preset) {
      case 'last-15m':
        start = new Date(now.getTime() - 15 * 60 * 1000);
        break;
      case 'last-30m':
        start = new Date(now.getTime() - 30 * 60 * 1000);
        break;
      case 'last-1h':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'last-24h':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'last-7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'last-30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'last-90d':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case 'last-1y':
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        // Default to last 24 hours
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        preset = 'last-24h';
    }
    
    dispatch(configurationAssessmentActions.setTimeRange(start, now, preset));
  }, [dispatch]);
  
  // Set custom time range
  const setCustomTimeRange = useCallback((start: Date, end: Date) => {
    dispatch(configurationAssessmentActions.setTimeRange(start, end));
  }, [dispatch]);
  
  // Format date for display
  const formatDate = useCallback((date: Date, includeTime: boolean = true): string => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };
    
    if (includeTime) {
      options.hour = '2-digit';
      options.minute = '2-digit';
      options.hour12 = true;
    }
    
    return date.toLocaleDateString('en-US', options);
  }, []);
  
  // Refresh data
  const refresh = useCallback(() => {
    dispatch(configurationAssessmentActions.refresh());
  }, [dispatch]);
  
  // Toggle auto-refresh
  const toggleAutoRefresh = useCallback((enabled: boolean) => {
    dispatch(configurationAssessmentActions.setAutoRefresh(enabled));
  }, [dispatch]);
  
  // Set refresh interval
  const setRefreshInterval = useCallback((interval: number) => {
    dispatch(configurationAssessmentActions.setRefreshInterval(interval));
  }, [dispatch]);
  
  return {
    timeRange,
    timeRangeOptions,
    setTimeRangePreset,
    setCustomTimeRange,
    formatDate,
    refresh,
    autoRefresh,
    toggleAutoRefresh,
    refreshInterval,
    setRefreshInterval,
  };
};