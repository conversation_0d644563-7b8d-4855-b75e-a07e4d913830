import { AutocompleteService, AutocompleteProvider } from '../interfaces';

/**
 * Implementation of the AutocompleteService interface
 */
export class AutocompleteServiceImpl implements AutocompleteService {
  private providers: Map<string, AutocompleteProvider> = new Map();

  /**
   * Gets an autocomplete provider by type
   * @param type The provider type
   * @returns The autocomplete provider or undefined if not found
   */
  public getProvider(type: string): AutocompleteProvider | undefined {
    return this.providers.get(type);
  }

  /**
   * Registers a new autocomplete provider
   * @param type The provider type
   * @param provider The autocomplete provider
   */
  public registerProvider(type: string, provider: AutocompleteProvider): void {
    this.providers.set(type, provider);
  }
}