import { Observable } from 'rxjs';

// Core data structures
export interface SearchResponse {
  hits: {
    total: number;
    hits: Array<{
      _id: string;
      _source: Record<string, unknown>;
      fields?: Record<string, unknown[]>;
      highlight?: Record<string, string[]>;
    }>;
  };
  aggregations?: Record<string, unknown>;
  took: number;
  timed_out: boolean;
  error?: {
    message: string;
    type: string;
  };
  meta?: Record<string, unknown>;
}

export interface DataFrame {
  columns: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  rows: Array<Record<string, unknown>>;
  meta?: {
    total: number;
    took: number;
  };
}

export interface DataStructure {
  id: string;
  title: string;
  type: string;
  parent?: DataStructure;
  children?: DataStructure[];
  hasNext?: boolean;
  paginationToken?: string;
  multiSelect?: boolean;
  columnHeader?: string;
  meta?: Record<string, unknown>;
}

export interface SearchRequest {
  query: Query;
  index?: string;
  size?: number;
  from?: number;
  sort?: Array<Record<string, 'asc' | 'desc'>>;
  fields?: string[];
  highlight?: Record<string, unknown>;
}

export interface SearchOptions {
  signal?: AbortSignal;
  strategy?: string;
}

// Query interfaces
export interface Query {
  query: string;
  language: string;
  dataset?: Dataset;
}

// Dataset interfaces
export interface Dataset extends BaseDataset {
  timeFieldName?: string;
  language?: string;
}

export interface BaseDataset {
  id: string;
  title: string;
  type: string;
  dataSource?: DataSource;
}

export interface DataSource {
  id: string;
  type: string;
}

export interface DatasetField {
  name: string;
  type: string;
  searchable: boolean;
  aggregatable: boolean;
  format?: {
    id: string;
    params?: Record<string, unknown>;
  };
}

export interface DatasetTypeConfig {
  id: string;
  title: string;
  meta: {
    icon: unknown;
    tooltip: string;
    searchOnLoad: boolean;
    isFieldLoadAsync?: boolean;
  };
  toDataset: (path: DataStructure[]) => Dataset;
  fetch: (services: unknown, path: DataStructure[]) => Promise<DataStructure>;
  fetchFields: (dataset: Dataset) => Promise<DatasetField[]>;
  supportedLanguages: () => string[];
}

// Language interfaces
export interface LanguageConfig {
  id: string;
  name: string;
  description: string;
  syntax: {
    highlight: (query: string) => string;
    validate: (query: string) => { valid: boolean; error?: string };
  };
  autocomplete?: {
    getCompletions: (query: string, position: number) => Promise<Completion[]>;
  };
}

export interface Completion {
  value: string;
  score: number;
  meta?: string;
}

// Service interfaces
export interface SearchService {
  searchSource: {
    create: () => Promise<SearchSource>;
  };
}

export interface SearchSource {
  setField: (field: string, value: unknown) => SearchSource;
  getField: (field: string) => unknown;
  fetch: () => Promise<SearchResponse>;
}

export interface QueryService {
  queryString: QueryStringManager;
  timefilter: TimeFilterService;
  filterManager: FilterManager;
  getDatasetService: () => DatasetService;
  getLanguageService: () => LanguageService;
}

export interface QueryStringManager {
  getQuery: () => Query;
  setQuery: (query: Query) => void;
  getUpdates$: () => Observable<Query>;
}

export interface TimeFilterService {
  getTime: () => TimeRange;
  setTime: (time: TimeRange) => void;
  getTimeUpdate$: () => Observable<TimeRange>;
}

export interface TimeRange {
  from: string;
  to: string;
}

export interface FilterManager {
  getFilters: () => Filter[];
  setFilters: (filters: Filter[]) => void;
  getFiltersUpdate$: () => Observable<Filter[]>;
}

export interface Filter {
  field: string;
  operator: string;
  value: unknown;
}

export interface DatasetService {
  getDatasets: () => Dataset[];
  getDataset: (id: string) => Dataset | undefined;
  registerType: (config: DatasetTypeConfig) => void;
  getType: (type: string) => DatasetTypeConfig | undefined;
  cacheDataset: (dataset: Dataset, services: unknown) => Promise<void>;
}

export interface LanguageService {
  getLanguages: () => LanguageConfig[];
  getLanguage: (id: string) => LanguageConfig | undefined;
  registerLanguage: (config: LanguageConfig) => void;
}

export interface UiService {
  getComponent: (id: string) => React.ComponentType<unknown> | undefined;
  registerComponent: (id: string, component: React.ComponentType<unknown>) => void;
}

export interface FieldFormatsService {
  getDefaultFormat: (fieldType: string) => FieldFormat;
  getFormat: (fieldType: string, formatId: string) => FieldFormat | undefined;
  registerFormat: (fieldType: string, format: FieldFormat) => void;
}

export interface FieldFormat {
  id: string;
  title: string;
  format: (value: unknown, params?: Record<string, unknown>) => string;
}

export interface AutocompleteService {
  getProvider: (type: string) => AutocompleteProvider | undefined;
  registerProvider: (type: string, provider: AutocompleteProvider) => void;
}

export interface AutocompleteProvider {
  getCompletions: (context: unknown) => Promise<Completion[]>;
}

export interface DataStorage {
  get: <T>(key: string, defaultValue?: T) => T | undefined;
  set: <T>(key: string, value: T) => void;
  remove: (key: string) => void;
}

// Index pattern interfaces
export interface IndexPatternSpec {
  id: string;
  title: string;
  timeFieldName?: string;
  fields: DatasetField[];
  dataSourceRef?: {
    id: string;
    name: string;
    type: string;
  };
}