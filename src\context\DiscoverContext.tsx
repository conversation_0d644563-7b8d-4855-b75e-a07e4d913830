import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { DiscoverState, DiscoverAction, LogEntry, Filter, TimeRange } from '../types/discover';
import { DiscoverUtils } from '../utils/discoverUtils';
import { sampleLogData } from '../data/sampleLogData';

import { useSearch, useQuery, useDataset } from '../hooks/dataHooks';
import { SearchResponse } from '../data/plugin/interfaces';

// Initial state
const initialState: DiscoverState = DiscoverUtils.getInitialState();

// Create context
const DiscoverContext = createContext<{
  state: DiscoverState;
  dispatch: React.Dispatch<DiscoverAction>;
} | undefined>(undefined);

// Reducer function
function discoverReducer(state: DiscoverState, action: DiscoverAction): DiscoverState {
  switch (action.type) {
    case 'SET_SEARCH_QUERY': {
      return {
        ...state,
        searchQuery: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          action.payload,
          state.appliedFilters,
          state.timeRange
        ),
      };
    }
    case 'SET_TIME_RANGE': {
      return {
        ...state,
        timeRange: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          state.appliedFilters,
          action.payload
        ),
        histogramData: DiscoverUtils.generateHistogramData(
          DiscoverUtils.filterLogData(
            state.logData,
            state.searchQuery,
            state.appliedFilters,
            action.payload
          ),
          action.payload
        ),
      };
    }
    case 'ADD_FILTER': {
      const newFilters = [...state.appliedFilters, action.payload];
      return {
        ...state,
        appliedFilters: newFilters,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          newFilters,
          state.timeRange
        ),
      };
    }
    case 'REMOVE_FILTER': {
      const updatedFilters = state.appliedFilters.filter(filter => filter.field !== action.payload);
      return {
        ...state,
        appliedFilters: updatedFilters,
        filteredData: DiscoverUtils.filterLogData(
          state.logData,
          state.searchQuery,
          updatedFilters,
          state.timeRange
        ),
      };
    }
    case 'TOGGLE_FIELD': {
      const fieldExists = state.selectedFields.includes(action.payload);
      const selectedFields = fieldExists
        ? state.selectedFields.filter(field => field !== action.payload)
        : [...state.selectedFields, action.payload];
      
      return {
        ...state,
        selectedFields,
      };
    }
    case 'SET_LOG_DATA': {
      return {
        ...state,
        logData: action.payload,
        filteredData: DiscoverUtils.filterLogData(
          action.payload,
          state.searchQuery,
          state.appliedFilters,
          state.timeRange
        ),
        histogramData: DiscoverUtils.generateHistogramData(
          DiscoverUtils.filterLogData(
            action.payload,
            state.searchQuery,
            state.appliedFilters,
            state.timeRange
          ),
          state.timeRange
        ),
        isLoading: false,
      };
    }
    case 'SET_LOADING': {
      return {
        ...state,
        isLoading: action.payload,
      };
    }
    case 'SET_AUTO_REFRESH': {
      return {
        ...state,
        autoRefresh: action.payload,
      };
    }
    case 'SET_REFRESH_INTERVAL': {
      return {
        ...state,
        refreshInterval: action.payload,
      };
    }
    case 'SET_CURRENT_PAGE': {
      return {
        ...state,
        pagination: {
          ...state.pagination,
          currentPage: action.payload,
        },
      };
    }
    case 'SET_PAGE_SIZE': {
      return {
        ...state,
        pagination: {
          ...state.pagination,
          pageSize: action.payload,
          currentPage: 1, // Reset to first page when changing page size
        },
      };
    }
    default:
      return state;
  }
}

// Provider component
interface DiscoverProviderProps {
  children: ReactNode;
  initialData?: LogEntry[];
  useDataPlugin?: boolean; // Flag to enable Data Plugin integration
}

export const DiscoverProvider: React.FC<DiscoverProviderProps> = ({ 
  children, 
  initialData = sampleLogData,
  useDataPlugin: useDataPluginFlag = true // Default to using Data Plugin
}) => {
  const [state, dispatch] = useReducer(discoverReducer, {
    ...initialState,
    isLoading: true,
  });
  
  // Always call hooks, then conditionally use their return values
  const searchHook = useSearch();
  const queryHook = useQuery();
  const datasetHook = useDataset();

  
  
  // Load initial data - legacy approach
  useEffect(() => {
    // Skip if using Data Plugin
    if (useDataPluginFlag) return;
    
    const loadData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      try {
        // In a real app, this would be an API call
        // For now, we're using the sample data
        dispatch({ type: 'SET_LOG_DATA', payload: initialData });
      } catch (error) {
        console.error('Error loading log data:', error);
        dispatch({ type: 'SET_LOG_DATA', payload: [] });
      }
    };
    
    loadData();
  }, [initialData]);
  
  // Set up auto-refresh if enabled - legacy approach
  useEffect(() => {
    // Skip if using Data Plugin
    if (useDataPluginFlag) return;
    if (!state.autoRefresh) return;
    
    const intervalId = setInterval(() => {
      // In a real app, this would fetch fresh data
      // For demo purposes, we'll just refresh with the same data
      dispatch({ type: 'SET_LOG_DATA', payload: state.logData });
    }, state.refreshInterval);
    
    return () => clearInterval(intervalId);
  }, [state.autoRefresh, state.refreshInterval, state.logData, useDataPluginFlag]);
  
  // Data Plugin integration - sync search results to state
  useEffect(() => {
    if (!useDataPluginFlag || !searchHook) return;

    const { results, isLoading, error } = searchHook;

    console.log('[DiscoverContext] Search hook state changed:', {
      hasResults: !!results,
      isLoading,
      hasError: !!error
    });

    // Update loading state
    dispatch({ type: 'SET_LOADING', payload: isLoading });

    // Handle search results
    if (results && !isLoading) {
      // Convert search results to LogEntry format
      const logEntries = convertSearchResultsToLogEntries(results);
      console.log('[DiscoverContext] Setting log data from search results:', logEntries.length, 'entries');
      console.log('[DiscoverContext] Sample log entry:', logEntries[0]);
      console.log('[DiscoverContext] Raw search results:', results);
      dispatch({ type: 'SET_LOG_DATA', payload: logEntries });
    }

    // Handle errors
    if (error) {
      console.error('Search error:', error);
      dispatch({ type: 'SET_LOG_DATA', payload: [] });
    }
  }, [searchHook?.results, searchHook?.isLoading, searchHook?.error, useDataPluginFlag]);
  
  // Data Plugin integration - sync query state
  useEffect(() => {
    if (!useDataPluginFlag || !queryHook) return;

    const { query, filters, timeRange } = queryHook;

    // Only log if there are actual changes to prevent spam
    const hasChanges = (query && query.query !== state.searchQuery) ||
                      (filters && !areFiltersEqual(filters.map(convertDataPluginFilterToDiscoverFilter), state.appliedFilters)) ||
                      (timeRange && !areTimeRangesEqual(convertDataPluginTimeRangeToDiscoverTimeRange(timeRange), state.timeRange));

    if (hasChanges) {
      console.log('[DiscoverContext] Query hook state changed:', {
        query: query?.query,
        filtersCount: filters?.length,
        timeRange: timeRange ? `${timeRange.from} - ${timeRange.to}` : null
      });
    }

    // Set flag to prevent sync loops
    setIsSyncingFromDataPlugin(true);

    // Sync query to state
    if (query && query.query !== state.searchQuery) {
      console.log('[DiscoverContext] Syncing query to state:', query.query);
      dispatch({ type: 'SET_SEARCH_QUERY', payload: query.query });
    }

    // Sync filters to state
    if (filters) {
      // Convert Data Plugin filters to Discover filters
      const discoverFilters = filters.map(convertDataPluginFilterToDiscoverFilter);

      // Check if filters have changed
      if (!areFiltersEqual(discoverFilters, state.appliedFilters)) {
        console.log('[DiscoverContext] Syncing filters to state:', discoverFilters.length, 'filters');
        // Replace all filters
        state.appliedFilters.forEach(filter => {
          dispatch({ type: 'REMOVE_FILTER', payload: filter.field });
        });

        discoverFilters.forEach(filter => {
          dispatch({ type: 'ADD_FILTER', payload: filter });
        });
      }
    }

    // Sync time range to state
    if (timeRange) {
      const discoverTimeRange = convertDataPluginTimeRangeToDiscoverTimeRange(timeRange);

      if (!areTimeRangesEqual(discoverTimeRange, state.timeRange)) {
        console.log('[DiscoverContext] Syncing time range to state:', discoverTimeRange);
        dispatch({ type: 'SET_TIME_RANGE', payload: discoverTimeRange });
      }
    }

    // Reset flag after a longer delay to ensure sync completes
    const timeoutId = setTimeout(() => setIsSyncingFromDataPlugin(false), 300);
    return () => clearTimeout(timeoutId);
  }, [queryHook?.query, queryHook?.filters, queryHook?.timeRange, useDataPluginFlag]);
  
  // Data Plugin integration - sync state changes back to Data Plugin
  // Use refs to track if we're in the middle of syncing to prevent loops
  const [isSyncingFromDataPlugin, setIsSyncingFromDataPlugin] = React.useState(false);

  // Use refs to track the last synced values to prevent unnecessary updates
  const lastSyncedQuery = React.useRef<string>('');
  const lastSyncedFilters = React.useRef<any[]>([]);
  const lastSyncedTimeRange = React.useRef<any>(null);

  useEffect(() => {
    if (!useDataPluginFlag || !queryHook || isSyncingFromDataPlugin) return;

    const { setQuery, setFilters, setTimeRange } = queryHook;

    // Check if any values have actually changed since last sync
    const queryChanged = state.searchQuery !== lastSyncedQuery.current;
    const filtersChanged = JSON.stringify(state.appliedFilters) !== JSON.stringify(lastSyncedFilters.current);
    const timeRangeChanged = JSON.stringify(state.timeRange) !== JSON.stringify(lastSyncedTimeRange.current);

    if (!queryChanged && !filtersChanged && !timeRangeChanged) {
      return; // No changes, skip sync
    }

    // Only log when there are actual changes
    console.log('[DiscoverContext] Syncing state changes back to Data Plugin:', {
      searchQuery: state.searchQuery,
      filtersCount: state.appliedFilters.length,
      timeRange: `${state.timeRange.from} - ${state.timeRange.to}`,
      queryChanged,
      filtersChanged,
      timeRangeChanged
    });

    // Sync search query to Data Plugin only if changed
    if (queryChanged) {
      setQuery({
        query: state.searchQuery,
        language: 'kuery', // Default to KQL
        dataset: datasetHook?.selectedDataset,
      });
      lastSyncedQuery.current = state.searchQuery;
    }

    // Sync filters to Data Plugin only if changed
    if (filtersChanged) {
      const dataPluginFilters = state.appliedFilters.map(convertDiscoverFilterToDataPluginFilter);
      setFilters(dataPluginFilters);
      lastSyncedFilters.current = [...state.appliedFilters];
    }

    // Sync time range to Data Plugin only if changed
    if (timeRangeChanged) {
      const dataPluginTimeRange = convertDiscoverTimeRangeToDataPluginTimeRange(state.timeRange);
      setTimeRange(dataPluginTimeRange);
      lastSyncedTimeRange.current = { ...state.timeRange };
    }
  }, [
    state.searchQuery,
    state.appliedFilters,
    state.timeRange,
    queryHook?.setQuery,
    queryHook?.setFilters,
    queryHook?.setTimeRange,
    datasetHook?.selectedDataset,
    useDataPluginFlag,
    isSyncingFromDataPlugin
  ]);
  
  // Set up auto-refresh with Data Plugin
  useEffect(() => {
    if (!useDataPluginFlag) return;
    if (!state.autoRefresh) return;
    
    const intervalId = setInterval(() => {
      // Execute search to refresh data
      searchHook.executeSearch();
    }, state.refreshInterval);
    
    return () => clearInterval(intervalId);
  }, [state.autoRefresh, state.refreshInterval, searchHook, useDataPluginFlag]);
  
  const value = { state, dispatch };
  
  return (
    <DiscoverContext.Provider value={value}>
      {children}
    </DiscoverContext.Provider>
  );
};

// Custom hook for using the context
export const useDiscover = () => {
  const context = useContext(DiscoverContext);
  
  if (context === undefined) {
    throw new Error('useDiscover must be used within a DiscoverProvider');
  }
  
  return context;
};

// Action creator functions for common operations
export const discoverActions = {
  setSearchQuery: (query: string): DiscoverAction => ({
    type: 'SET_SEARCH_QUERY',
    payload: query,
  }),
  
  setTimeRange: (from: Date, to: Date, preset?: string): DiscoverAction => ({
    type: 'SET_TIME_RANGE',
    payload: { from, to, preset },
  }),
  
  addFilter: (field: string, value: unknown, operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains' = 'is'): DiscoverAction => ({
    type: 'ADD_FILTER',
    payload: DiscoverUtils.createFilter(field, value, operator),
  }),
  
  removeFilter: (field: string): DiscoverAction => ({
    type: 'REMOVE_FILTER',
    payload: field,
  }),
  
  toggleField: (field: string): DiscoverAction => ({
    type: 'TOGGLE_FIELD',
    payload: field,
  }),
  
  setAutoRefresh: (enabled: boolean): DiscoverAction => ({
    type: 'SET_AUTO_REFRESH',
    payload: enabled,
  }),
  
  setRefreshInterval: (interval: number): DiscoverAction => ({
    type: 'SET_REFRESH_INTERVAL',
    payload: interval,
  }),
  
  refresh: (): DiscoverAction => ({
    type: 'SET_LOADING',
    payload: true,
  }),
  
  setCurrentPage: (page: number): DiscoverAction => ({
    type: 'SET_CURRENT_PAGE',
    payload: page,
  }),
  
  setPageSize: (size: number): DiscoverAction => ({
    type: 'SET_PAGE_SIZE',
    payload: size,
  }),
};

// Helper functions for Data Plugin integration

/**
 * Convert SearchResponse to LogEntry array
 */
interface SourceData {
  timestamp?: number;
  source?: string;
  message?: string;
  level?: string;
  agent?: {
    id?: string;
    name?: string;
    ip?: string;
  };
  rule?: {
    id?: number;
    description?: string;
    level?: number;
    groups?: string[];
  };
  location?: string;
  decoder?: {
    name?: string;
  };
  [key: string]: unknown; // Allow for other properties
}

/**
 * Convert SearchResponse to LogEntry array
 */
export function convertSearchResultsToLogEntries(results: SearchResponse): LogEntry[] {
  return results.hits.hits.map(hit => {
    const source: SourceData = hit._source;
    
    // Try to map fields to LogEntry structure
    return {
      id: hit._id,
      timestamp: new Date(source.timestamp || Date.now()),
      source: source.source || 'unknown',
      message: source.message || JSON.stringify(source),
      level: (source.level as 'info' | 'warning' | 'error' | 'critical') || 'info',
      agent: {
        id: source.agent?.id || 'unknown',
        name: source.agent?.name || 'unknown',
        ip: source.agent?.ip || 'unknown',
      },
      rule: {
        id: source.rule?.id || 0,
        description: source.rule?.description || 'unknown',
        level: source.rule?.level || 0,
        groups: source.rule?.groups || [],
      },
      location: source.location || 'unknown',
      decoder: {
        name: source.decoder?.name || 'unknown',
      },
      data: { ...source }, // Store the full source as data
    };
  });
}

/**
 * Convert Data Plugin filter to Discover filter
 */
export function convertDataPluginFilterToDiscoverFilter(filter: { field: string; operator: string; value: unknown }): Filter {
  return {
    field: filter.field,
    operator: mapOperator(filter.operator),
    value: filter.value,
    enabled: true,
  };
}

/**
 * Convert Discover filter to Data Plugin filter
 */
export function convertDiscoverFilterToDataPluginFilter(filter: Filter): { field: string; operator: string; value: unknown } {
  return {
    field: filter.field,
    operator: mapOperatorReverse(filter.operator),
    value: filter.value,
  };
}

/**
 * Map Data Plugin operator to Discover operator
 */
export function mapOperator(operator: string): 'is' | 'is not' | 'exists' | 'does not exist' | 'contains' {
  switch (operator) {
    case 'eq':
      return 'is';
    case 'ne':
      return 'is not';
    case 'exists':
      return 'exists';
    case 'not_exists':
      return 'does not exist';
    case 'contains':
      return 'contains';
    default:
      return 'is';
  }
}

/**
 * Map Discover operator to Data Plugin operator
 */
export function mapOperatorReverse(operator: 'is' | 'is not' | 'exists' | 'does not exist' | 'contains'): string {
  switch (operator) {
    case 'is':
      return 'eq';
    case 'is not':
      return 'ne';
    case 'exists':
      return 'exists';
    case 'does not exist':
      return 'not_exists';
    case 'contains':
      return 'contains';
    default:
      return 'eq';
  }
}

/**
 * Convert Data Plugin time range to Discover time range
 */
export function convertDataPluginTimeRangeToDiscoverTimeRange(timeRange: { from: string; to: string }): TimeRange {
  return {
    from: new Date(timeRange.from),
    to: new Date(timeRange.to),
    preset: determinePreset(timeRange.from, timeRange.to),
  };
}

/**
 * Convert Discover time range to Data Plugin time range
 */
export function convertDiscoverTimeRangeToDataPluginTimeRange(timeRange: TimeRange): { from: string; to: string } {
  const from = new Date(timeRange.from);
  const to = new Date(timeRange.to);

  // Validate dates before converting
  const fromISO = !isNaN(from.getTime()) ? from.toISOString() : new Date().toISOString();
  const toISO = !isNaN(to.getTime()) ? to.toISOString() : new Date().toISOString();

  return {
    from: fromISO,
    to: toISO,
  };
}

/**
 * Determine time range preset from from/to values
 */
export function determinePreset(from: string, to: string): TimeRange['preset'] | undefined {
  const fromDate = new Date(from);
  const toDate = new Date(to);
  const diffMs = toDate.getTime() - fromDate.getTime();
  const diffMinutes = diffMs / (1000 * 60);
  
  if (Math.abs(diffMinutes - 15) < 1) return 'last-15m';
  if (Math.abs(diffMinutes - 60) < 5) return 'last-1h';
  if (Math.abs(diffMinutes - 24 * 60) < 10) return 'last-24h';
  if (Math.abs(diffMinutes - 7 * 24 * 60) < 60) return 'last-7d';
  if (Math.abs(diffMinutes - 30 * 24 * 60) < 60) return 'last-30d';
  
  return 'custom';
}

/**
 * Check if two filter arrays are equal
 */
export function areFiltersEqual(filters1: Filter[], filters2: Filter[]): boolean {
  if (filters1.length !== filters2.length) return false;
  
  for (let i = 0; i < filters1.length; i++) {
    const f1 = filters1[i];
    const f2 = filters2.find(f => f.field === f1.field);
    
    if (!f2) return false;
    if (f1.operator !== f2.operator) return false;
    if (f1.value !== f2.value) return false;
    if (f1.enabled !== f2.enabled) return false;
  }
  
  return true;
}

/**
 * Check if two time ranges are equal
 */
export function areTimeRangesEqual(range1: TimeRange, range2: TimeRange): boolean {
  return (
    range1.from.getTime() === range2.from.getTime() &&
    range1.to.getTime() === range2.to.getTime()
  );
}