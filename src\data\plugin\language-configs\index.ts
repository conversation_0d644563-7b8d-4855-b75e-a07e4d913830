import { DataPlugin } from '../plugin';
import { simpleQueryLanguage } from './simple-query-language';

/**
 * Registers all language configurations with the LanguageService
 * @param dataPlugin The data plugin instance
 */
export function registerLanguageConfigs(dataPlugin: DataPlugin): void {
  const languageService = dataPlugin.getQueryService().getLanguageService();
  
  // Register the simple query language
  languageService.registerLanguage(simpleQueryLanguage);
}