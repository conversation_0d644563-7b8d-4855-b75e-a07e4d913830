import { FilterManager, Filter } from '../interfaces';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Implementation of the FilterManager interface
 * FilterManager is responsible for managing the current filters
 */
export class FilterManagerImpl implements FilterManager {
  private filtersSubject: BehaviorSubject<Filter[]>;
  
  /**
   * Constructor for FilterManagerImpl
   * @param initialFilters The initial filters
   */
  constructor(initialFilters: Filter[] = []) {
    this.filtersSubject = new BehaviorSubject<Filter[]>(initialFilters);
  }

  /**
   * Gets the current filters
   * @returns The current filters
   */
  public getFilters(): Filter[] {
    return this.filtersSubject.getValue();
  }

  /**
   * Sets new filters
   * @param filters The new filters
   */
  public setFilters(filters: Filter[]): void {
    this.filtersSubject.next(filters);
  }

  /**
   * Gets an observable that emits when the filters change
   * @returns An observable of Filter[]
   */
  public getFiltersUpdate$(): Observable<Filter[]> {
    return this.filtersSubject.asObservable();
  }

  /**
   * Adds a filter to the current filters
   * @param filter The filter to add
   */
  public addFilter(filter: Filter): void {
    const currentFilters = this.getFilters();
    this.setFilters([...currentFilters, filter]);
  }

  /**
   * Removes a filter from the current filters
   * @param field The field of the filter to remove
   */
  public removeFilter(field: string): void {
    const currentFilters = this.getFilters();
    this.setFilters(currentFilters.filter(f => f.field !== field));
  }

  /**
   * Clears all filters
   */
  public clearFilters(): void {
    this.setFilters([]);
  }
}