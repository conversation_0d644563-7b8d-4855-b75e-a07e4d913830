import React from 'react';

interface ConfigurationLoadingStateProps {
  message?: string;
}

/**
 * Loading state component for Configuration Assessment
 * Displays a loading spinner and message
 * 
 * Requirements: 6.4
 */
const ConfigurationLoadingState: React.FC<ConfigurationLoadingStateProps> = ({
  message = 'Loading configurations...',
}) => {
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(10, 14, 23, 0.7)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 100,
      backdropFilter: 'blur(2px)',
    }}>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '16px',
      }}>
        <div className="loading-spinner" style={{
          width: '40px',
          height: '40px',
          position: 'relative',
        }}>
          <div style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            border: '3px solid rgba(0, 229, 255, 0.1)',
            borderTop: '3px solid #00e5ff',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
          }} />
          <div style={{
            position: 'absolute',
            width: '70%',
            height: '70%',
            top: '15%',
            left: '15%',
            border: '3px solid rgba(0, 229, 255, 0)',
            borderTop: '3px solid rgba(0, 229, 255, 0.5)',
            borderRadius: '50%',
            animation: 'spin 1.5s linear infinite reverse',
          }} />
        </div>
        <div style={{
          color: 'white',
          fontSize: '16px',
        }}>
          {message}
        </div>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    </div>
  );
};

export default ConfigurationLoadingState;