import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { ENDPOINT_SECURITY_ITEMS } from '../EndpointSecurityNavigation/config';
import ExploreSubItem from './ExploreSubItem';

interface EndpointSecurityDropdownProps {
  isExpanded: boolean;
  isCollapsed: boolean;
  activeItem?: string;
  onItemClick: (item: string) => void;
}

const EndpointSecurityDropdown: React.FC<EndpointSecurityDropdownProps> = ({
  isExpanded,
  isCollapsed,
  activeItem,
  onItemClick
}) => {
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 60 });

  useEffect(() => {
    if (isCollapsed && isExpanded) {
      // Calculate position based on the nav button
      const sidebarHeight = window.innerHeight;
      const navItemHeight = 40;
      const navItemsCount = 7; // Approximate number of nav items
      const centerOffset = (sidebarHeight / 2) - ((navItemsCount * navItemHeight) / 2);
      const endpointSecurityButtonIndex = 2; // Endpoint Security is the third item (0-indexed)
      const endpointSecurityButtonTop = centerOffset + (endpointSecurityButtonIndex * (navItemHeight + 15));
      setDropdownPosition({
        top: endpointSecurityButtonTop,
        left: 80
      });
    }
  }, [isCollapsed, isExpanded]);

  const getDropdownStyle = () => {
    const maxHeight = isExpanded ? `${ENDPOINT_SECURITY_ITEMS.length * 40}px` : '0px';
    return {
      overflow: 'hidden',
      maxHeight,
      transition: 'max-height 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      width: '100%',
      paddingLeft: isCollapsed ? '0' : '16px',
      marginBottom: isExpanded ? '8px' : '0',
    };
  };

  const getContainerStyle = () => ({
    display: 'flex',
    flexDirection: 'column' as const,
    gap: '4px',
    paddingTop: isExpanded ? '8px' : '0',
    transition: 'padding-top 0.3s ease',
  });

  if (isCollapsed && isExpanded) {
    return createPortal(
      <div 
        style={{
          position: 'fixed',
          left: `${dropdownPosition.left}px`,
          top: `${dropdownPosition.top}px`,
          minWidth: '200px',
          background: 'rgba(16, 24, 45, 0.95)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          borderRadius: '8px',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.3)',
          padding: '8px',
          zIndex: 99999,
          pointerEvents: 'auto',
        }}
        role="menu"
        aria-label="Endpoint Security navigation menu"
      >
        <div style={getContainerStyle()}>
          {ENDPOINT_SECURITY_ITEMS.map((item) => (
            <ExploreSubItem
              key={item.id}
              id={item.id}
              label={item.label}
              icon={item.icon}
              isActive={activeItem === item.id}
              isCollapsed={false}
              onClick={onItemClick}
            />
          ))}
        </div>
      </div>,
      document.body
    );
  }

  return (
    <div style={getDropdownStyle()}>
      <div style={getContainerStyle()}>
        {ENDPOINT_SECURITY_ITEMS.map((item) => (
          <ExploreSubItem
            key={item.id}
            id={item.id}
            label={item.label}
            icon={item.icon}
            isActive={activeItem === item.id}
            isCollapsed={isCollapsed}
            onClick={onItemClick}
          />
        ))}
      </div>
    </div>
  );
};

export default EndpointSecurityDropdown; 