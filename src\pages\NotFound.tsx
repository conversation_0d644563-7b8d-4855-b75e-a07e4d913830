import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * Not Found (404) page component.
 * 
 * This component displays a 404 error page when a route is not found.
 */
const NotFound: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      padding: '24px',
      textAlign: 'center',
    }}>
      <div style={{
        fontSize: '120px',
        fontWeight: 'bold',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 20px rgba(0, 229, 255, 0.3)',
        marginBottom: '16px',
        lineHeight: 1,
      }}>
        404
      </div>
      
      <h1 style={{ 
        fontSize: '32px', 
        fontWeight: 600, 
        marginBottom: '16px',
        color: 'white',
      }}>
        Page Not Found
      </h1>
      
      <p style={{ 
        fontSize: '16px', 
        color: 'rgba(255, 255, 255, 0.7)',
        marginBottom: '32px',
        maxWidth: '500px',
      }}>
        The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
      </p>
      
      <button 
        onClick={() => navigate('/')}
        style={{
          background: 'rgba(0, 229, 255, 0.1)',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '12px 24px',
          color: '#00e5ff',
          fontSize: '16px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
          <polyline points="9 22 9 12 15 12 15 22" />
        </svg>
        Return to Dashboard
      </button>
    </div>
  );
};

export default NotFound;