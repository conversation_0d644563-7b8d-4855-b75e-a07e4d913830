import React, { useRef } from 'react';
import { useDiscoverTime } from '../../hooks';

/**
 * Histogram component for the Discover page
 * Displays log distribution over time with interactive features
 */
const DiscoverHistogram: React.FC = () => {
  const { 
    groupedData, 
    timeInterval, 
  } = useDiscoverTime();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Format count with thousands separator
  const formatCount = (count: number): string => {
    return new Intl.NumberFormat().format(count);
  };
  
  // Get total count of logs
  const totalLogs = groupedData.reduce((sum, item) => sum + item.count, 0);
  
  // Get interval label
  const getIntervalLabel = (): string => {
    switch (timeInterval) {
      case 'minute': return 'minute';
      case 'hour': return 'hour';
      case 'day': return 'day';
      case 'week': return 'week';
      default: return 'interval';
    }
  };
  
  return (
    <div 
      ref={containerRef}
      style={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {/* Header with stats */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        <h3 style={{ 
          margin: 0, 
          color: 'white',
          fontSize: '16px',
        }}>
          Log Distribution Over Time
        </h3>
        
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          fontSize: '12px',
          color: 'rgba(255, 255, 255, 0.7)',
        }}>
          <div>
            <span style={{ fontWeight: 'bold' }}>{formatCount(totalLogs)}</span> logs
          </div>
          <div>
            <span style={{ fontWeight: 'bold' }}>{groupedData.length}</span> {getIntervalLabel()}s
          </div>
          <div style={{ fontSize: '11px', color: 'rgba(255, 255, 255, 0.5)' }}>
            Drag to zoom
          </div>
        </div>
      </div>
      
      {/* Time-based histogram visualization */}
      <HistogramTimeDistribution height={200} />
      
      {/* Level distribution compact view (optional, remove if not needed elsewhere) */}
    </div>
  );
};

export default DiscoverHistogram;