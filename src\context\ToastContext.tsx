import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { ToastItem } from '../components/common/ToastContainer';
import { ToastType } from '../components/common/Toast';

interface ToastContextType {
  addToast: (message: string, type?: ToastType, duration?: number) => string;
  removeToast: (id: string) => void;
  toasts: ToastItem[];
}

// Create the context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
  maxToasts?: number;
}

/**
 * Provider component for the ToastContext
 */
export function ToastProvider({
  children,
  maxToasts = 5
}: ToastProviderProps) {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  
  // Add a new toast
  const addToast = useCallback((message: string, type: ToastType = 'info', duration: number = 5000) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToast: ToastItem = { id, message, type, duration };
    
    setToasts(prevToasts => {
      // If we've reached the maximum number of toasts, remove the oldest one
      if (prevToasts.length >= maxToasts) {
        return [...prevToasts.slice(1), newToast];
      }
      return [...prevToasts, newToast];
    });
    
    return id;
  }, [maxToasts]);
  
  // Remove a toast by ID
  const removeToast = useCallback((id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);
  
  // Create the context value
  const contextValue: ToastContextType = {
    addToast,
    removeToast,
    toasts
  };
  
  return (
    <ToastContext.Provider value={contextValue}>
      {children}
    </ToastContext.Provider>
  );
};

/**
 * Hook for using the ToastContext
 */
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  
  return context;
};