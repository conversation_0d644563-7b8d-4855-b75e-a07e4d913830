# Discover Page Table Rendering and Pagination Documentation

## Overview

The Discover page displays log data in a sophisticated table format with advanced pagination controls and expandable row details. The implementation leverages a plugin-based architecture for data management and uses React Context for state management.

## Architecture Overview

### Plugin Architecture Integration

The Discover page is built on a plugin-based architecture centered around the **Data Plugin**, which provides core services for data management, search execution, and UI components.

#### Key Plugin Components:

1. **DataPlugin** (`src/data/plugin/data-plugin.ts`)
   - Singleton pattern implementation
   - Provides SearchService, QueryService, UiService, FieldFormatsService, AutocompleteService
   - Manages IndexPatternService for data structure handling

2. **DataPluginProvider** (`src/context/DataPluginContext.tsx`)
   - React context provider that initializes and provides access to the DataPlugin
   - Handles plugin initialization and error states
   - Must wrap the DiscoverProvider for plugin integration

3. **Data Hooks** (`src/hooks/dataHooks.ts`)
   - `useSearch()`: Executes searches and manages search state
   - `useQuery()`: Manages query state and synchronization
   - `useDataset()`: Handles dataset selection and management

### Context Architecture

The Discover functionality is managed through a React Context system:

```typescript
// Provider hierarchy for plugin integration
<DataPluginProvider>
  <DiscoverProvider useDataPlugin={true}>
    <DiscoverTable />
  </DiscoverProvider>
</DataPluginProvider>
```

## Table Rendering System

### Main Components

#### 1. DiscoverTable (`src/components/Discover/DiscoverTable.tsx`)

The primary table component that orchestrates data display and pagination:

**Key Features:**
- Dual rendering modes: Standard table and virtualized table
- Automatic virtualization for datasets > 100 items
- Expandable row details
- Integrated pagination controls
- Field-based column rendering

**State Management:**
```typescript
const { state, dispatch } = useDiscover();
const { isLoading, error } = useSearch();
```

**Pagination Logic:**
```typescript
// Calculate pagination values
const totalPages = Math.ceil(state.filteredData.length / state.pagination.pageSize);
const startIndex = (state.pagination.currentPage - 1) * state.pagination.pageSize;
const endIndex = Math.min(startIndex + state.pagination.pageSize, state.filteredData.length);
const currentPageData = state.filteredData.slice(startIndex, endIndex);
```

#### 2. VirtualizedTable (`src/components/Discover/VirtualizedTable.tsx`)

High-performance virtualized table for large datasets:

**Technology Stack:**
- `react-window` for virtualization
- `react-virtualized-auto-sizer` for responsive sizing
- Fixed row height (40px default) with configurable header height

**Features:**
- Efficient rendering of large datasets
- Dynamic field width calculation
- Expandable row support
- Memoized row rendering for performance

#### 3. Pagination Controls

**Built-in Pagination** (within DiscoverTable):
```typescript
const renderPagination = () => {
  return (
    <div className="table-pagination">
      <div className="pagination-info">
        Showing {startIndex + 1} to {endIndex} of {state.filteredData.length} entries
      </div>
      <div className="pagination-controls">
        {/* First, Previous, Next, Last buttons */}
      </div>
      <div className="pagination-size">
        {/* Page size selector */}
      </div>
    </div>
  );
};
```

**Standalone Components:**
- `PaginationControls` (`src/components/Discover/PaginationControls.tsx`)
- `PageSizeSelector` (`src/components/Discover/PageSizeSelector.tsx`)

## Data Flow and Plugin Integration

### 1. Data Plugin Integration

The DiscoverProvider can operate in two modes:

**Plugin Mode** (`useDataPlugin: true`):
```typescript
// Data flows through plugin services
const searchHook = useSearch();     // Executes searches via SearchService
const queryHook = useQuery();       // Manages query state via QueryService
const datasetHook = useDataset();   // Handles dataset selection
```

**Legacy Mode** (`useDataPlugin: false`):
```typescript
// Direct data loading from sample data
useEffect(() => {
  dispatch({ type: 'SET_LOG_DATA', payload: initialData });
}, [initialData]);
```

### 2. State Synchronization

When using the plugin architecture, the DiscoverContext synchronizes with plugin services:

```typescript
// Sync query state from plugin to context
useEffect(() => {
  if (query && query.query !== state.searchQuery) {
    dispatch({ type: 'SET_SEARCH_QUERY', payload: query.query });
  }
}, [query, state.searchQuery]);

// Sync context state back to plugin
useEffect(() => {
  setQuery({
    query: state.searchQuery,
    language: 'kuery',
    dataset: datasetHook?.selectedDataset,
  });
}, [state.searchQuery, state.appliedFilters, state.timeRange]);
```

### 3. Search Execution Flow

1. **Query Updates**: Changes trigger search execution via `useSearch` hook
2. **Data Processing**: Results processed through SearchService
3. **State Updates**: Filtered data updates trigger table re-render
4. **Pagination Reset**: Page resets to 1 when data changes

## Pagination Implementation Details

### State Structure

```typescript
interface DiscoverState {
  pagination: {
    currentPage: number;    // 1-based page numbering
    pageSize: number;       // Items per page (10, 25, 50, 100)
    totalItems: number;     // Total number of items
  };
  // ... other state properties
}
```

### Action Creators

```typescript
export const discoverActions = {
  setCurrentPage: (page: number): DiscoverAction => ({
    type: 'SET_CURRENT_PAGE',
    payload: page,
  }),
  
  setPageSize: (size: number): DiscoverAction => ({
    type: 'SET_PAGE_SIZE',
    payload: size,
  }),
};
```

### Reducer Logic

```typescript
case 'SET_CURRENT_PAGE': {
  return {
    ...state,
    pagination: {
      ...state.pagination,
      currentPage: action.payload,
    },
  };
}

case 'SET_PAGE_SIZE': {
  return {
    ...state,
    pagination: {
      ...state.pagination,
      pageSize: action.payload,
      currentPage: 1, // Reset to first page when changing page size
    },
  };
}
```

## Performance Optimizations

### 1. Virtualization Strategy

- **Threshold**: Automatically enables virtualization for datasets > 100 items
- **Row Height**: Fixed 40px rows for consistent performance
- **Memory Management**: Only renders visible rows plus buffer

### 2. Memoization

- **Row Rendering**: `React.memo` on VirtualizedTable component
- **Field Formatting**: Memoized `formatFieldValue` function
- **Expanded Rows**: Memoized expanded row data calculation

### 3. Data Slicing

- **Client-side Pagination**: Data sliced on client for immediate response
- **Filtered Data**: Pagination operates on pre-filtered dataset
- **Lazy Loading**: Expanded row details loaded on demand

## Configuration Options

### DiscoverTable Props

```typescript
interface DiscoverTableProps {
  className?: string;           // Additional CSS classes
  pageSize?: number;           // Override default page size
  showPagination?: boolean;    // Enable/disable pagination (default: true)
  showExpandedView?: boolean;  // Enable/disable row expansion (default: true)
}
```

### Page Size Options

Default options: `[10, 25, 50, 100]`

Configurable through PageSizeSelector component:
```typescript
<PageSizeSelector
  pageSize={state.pagination.pageSize}
  onPageSizeChange={handlePageSizeChange}
  options={[10, 25, 50, 100]}
/>
```

## Usage Examples

### Basic Implementation

```typescript
import DiscoverTable from '../discover/DiscoverTable';
import { DiscoverProvider } from '../../context/DiscoverContext';
import { DataPluginProvider } from '../../context/DataPluginContext';

const MyComponent: React.FC = () => {
  return (
    <DataPluginProvider>
      <DiscoverProvider useDataPlugin={true}>
        <DiscoverTable
          pageSize={25}
          showPagination={true}
          showExpandedView={true}
        />
      </DiscoverProvider>
    </DataPluginProvider>
  );
};
```

### Custom Pagination Controls

```typescript
import PaginationControls from '../discover/PaginationControls';
import { useDiscover, discoverActions } from '../../context/DiscoverContext';

const CustomPagination: React.FC = () => {
  const { state, dispatch } = useDiscover();
  
  return (
    <PaginationControls
      currentPage={state.pagination.currentPage}
      totalPages={Math.ceil(state.filteredData.length / state.pagination.pageSize)}
      onPageChange={(page) => dispatch(discoverActions.setCurrentPage(page))}
      isLoading={state.isLoading}
    />
  );
};
```

## Testing

The table and pagination functionality includes comprehensive test coverage:

- **Unit Tests**: `src/components/Discover/__tests__/DiscoverTable.test.tsx`
- **Context Tests**: `src/context/__tests__/DiscoverContext.test.tsx`
- **Integration Tests**: Plugin integration and data flow testing

Key test scenarios:
- Pagination navigation (First, Previous, Next, Last)
- Page size changes and state updates
- Row expansion and data display
- Plugin integration and data synchronization
- Error handling and loading states

## File Structure

```
src/
├── components/Discover/
│   ├── DiscoverTable.tsx           # Main table component
│   ├── VirtualizedTable.tsx        # High-performance virtualized table
│   ├── PaginationControls.tsx      # Standalone pagination controls
│   ├── PageSizeSelector.tsx        # Page size selection component
│   └── __tests__/                  # Component tests
├── context/
│   ├── DiscoverContext.tsx         # Main state management
│   └── DataPluginContext.tsx       # Plugin integration context
├── hooks/
│   └── dataHooks.ts               # Plugin integration hooks
├── data/plugin/
│   ├── data-plugin.ts             # Core plugin implementation
│   └── services/                  # Plugin services
└── types/
    └── discover.ts                # TypeScript interfaces
```

This architecture provides a scalable, performant, and maintainable solution for displaying and paginating large datasets while leveraging the plugin system for data management and search capabilities.
