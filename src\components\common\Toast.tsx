import React, { useState, useEffect } from 'react';

export type ToastType = 'info' | 'success' | 'warning' | 'error';

interface ToastProps {
  message: string;
  type?: ToastType;
  duration?: number;
  onClose?: () => void;
  showCloseButton?: boolean;
}

/**
 * Toast notification component for displaying messages
 */
const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  duration = 5000,
  onClose,
  showCloseButton = true
}) => {
  const [visible, setVisible] = useState(true);
  
  // Auto-hide the toast after the specified duration
  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setVisible(false);
        if (onClose) {
          onClose();
        }
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);
  
  // Handle close button click
  const handleClose = () => {
    setVisible(false);
    if (onClose) {
      onClose();
    }
  };
  
  // Get icon based on toast type
  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'warning':
        return '⚠️';
      case 'error':
        return '✕';
      case 'info':
      default:
        return 'ℹ️';
    }
  };
  
  if (!visible) {
    return null;
  }
  
  return (
    <div className={`toast toast-${type}`}>
      <div className="toast-icon">{getIcon()}</div>
      <div className="toast-message">{message}</div>
      {showCloseButton && (
        <button className="toast-close" onClick={handleClose}>
          ✕
        </button>
      )}
    </div>
  );
};

export default Toast;