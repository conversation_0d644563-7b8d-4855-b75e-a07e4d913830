import React, { useState } from 'react';
import { useConfigurationFields } from '../../hooks';
import ConfigurationFilters from './ConfigurationFilters';

interface ConfigurationSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

/**
 * Sidebar component for the Configuration Assessment page
 * Contains filters and field selectors
 * 
 * Requirements: 2.3, 2.4
 */
const ConfigurationSidebar: React.FC<ConfigurationSidebarProps> = ({ 
  isCollapsed, 
  onToggleCollapse 
}) => {
  const { 
    selectedFields, 
    availableFields, 
    toggleField, 
    resetFields 
  } = useConfigurationFields();
  
  // State for active tab
  const [activeTab, setActiveTab] = useState<'fields' | 'filters'>('filters');
  
  return (
    <div style={{
      width: isCollapsed ? '40px' : '300px',
      height: '100%',
      background: 'rgba(10, 14, 23, 0.9)',
      borderRight: '1px solid rgba(0, 229, 255, 0.2)',
      transition: 'width 0.3s',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
    }}>
      {/* Header */}
      <div style={{
        padding: isCollapsed ? '12px 8px' : '12px 16px',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        display: 'flex',
        justifyContent: isCollapsed ? 'center' : 'space-between',
        alignItems: 'center',
      }}>
        {!isCollapsed && (
          <h3 style={{ 
            margin: 0, 
            color: 'white',
            fontSize: '16px',
          }}>
            Configuration Assessment
          </h3>
        )}
        
        <button
          onClick={onToggleCollapse}
          style={{
            background: 'transparent',
            border: 'none',
            color: 'rgba(255, 255, 255, 0.7)',
            cursor: 'pointer',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        >
          <svg 
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2"
            style={{
              transform: isCollapsed ? 'rotate(0deg)' : 'rotate(180deg)',
            }}
          >
            <polyline points="15 18 9 12 15 6" />
          </svg>
        </button>
      </div>
      
      {/* Content (only visible when expanded) */}
      {!isCollapsed && (
        <>
          {/* Tabs */}
          <div style={{
            display: 'flex',
            borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
          }}>
            <button
              onClick={() => setActiveTab('filters')}
              style={{
                flex: 1,
                padding: '12px',
                background: 'transparent',
                border: 'none',
                borderBottom: activeTab === 'filters' ? '2px solid #00e5ff' : '2px solid transparent',
                color: activeTab === 'filters' ? 'white' : 'rgba(255, 255, 255, 0.6)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: activeTab === 'filters' ? 'bold' : 'normal',
              }}
            >
              Filters
            </button>
            <button
              onClick={() => setActiveTab('fields')}
              style={{
                flex: 1,
                padding: '12px',
                background: 'transparent',
                border: 'none',
                borderBottom: activeTab === 'fields' ? '2px solid #00e5ff' : '2px solid transparent',
                color: activeTab === 'fields' ? 'white' : 'rgba(255, 255, 255, 0.6)',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: activeTab === 'fields' ? 'bold' : 'normal',
              }}
            >
              Fields
            </button>
          </div>
          
          {/* Tab content */}
          <div style={{
            flex: 1,
            overflowY: 'auto',
            padding: '16px',
          }}>
            {activeTab === 'filters' && (
              <ConfigurationFilters />
            )}
            
            {activeTab === 'fields' && (
              <>
                {/* Selected fields section */}
                <div style={{ marginBottom: '24px' }}>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '8px',
                  }}>
                    <h4 style={{ 
                      margin: 0, 
                      color: 'white',
                      fontSize: '14px',
                    }}>
                      Selected Fields
                    </h4>
                    
                    <button
                      onClick={resetFields}
                      style={{
                        background: 'transparent',
                        border: 'none',
                        color: '#00e5ff',
                        cursor: 'pointer',
                        padding: '4px',
                        fontSize: '12px',
                        textDecoration: 'underline',
                      }}
                    >
                      Reset to default
                    </button>
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                  }}>
                    {selectedFields.map(field => (
                      <div 
                        key={field}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '4px 8px',
                          background: 'rgba(0, 229, 255, 0.1)',
                          borderRadius: '4px',
                          cursor: 'pointer',
                        }}
                        onClick={() => toggleField(field)}
                      >
                        <span style={{
                          width: '16px',
                          height: '16px',
                          borderRadius: '2px',
                          border: '1px solid rgba(0, 229, 255, 0.5)',
                          background: 'rgba(0, 229, 255, 0.2)',
                          marginRight: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                        }}>
                          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                            <polyline points="20 6 9 17 4 12" />
                          </svg>
                        </span>
                        <span style={{ color: 'white', fontSize: '14px' }}>
                          {field}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Available fields section */}
                <div>
                  <h4 style={{ 
                    margin: '0 0 8px 0', 
                    color: 'white',
                    fontSize: '14px',
                  }}>
                    Available Fields
                  </h4>
                  
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '4px',
                  }}>
                    {Object.keys(availableFields).filter(field => !selectedFields.includes(field)).map(field => (
                      <div 
                        key={field}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '4px 8px',
                          borderRadius: '4px',
                          cursor: 'pointer',
                        }}
                        onClick={() => toggleField(field)}
                        onMouseOver={(e) => {
                          e.currentTarget.style.background = 'rgba(0, 229, 255, 0.05)';
                        }}
                        onMouseOut={(e) => {
                          e.currentTarget.style.background = 'transparent';
                        }}
                      >
                        <span style={{
                          width: '16px',
                          height: '16px',
                          borderRadius: '2px',
                          border: '1px solid rgba(255, 255, 255, 0.3)',
                          marginRight: '8px',
                        }} />
                        <span style={{ color: 'rgba(255, 255, 255, 0.7)', fontSize: '14px' }}>
                          {field}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default ConfigurationSidebar;