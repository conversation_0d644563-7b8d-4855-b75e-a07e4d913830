import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { DiscoverProvider, useDiscover, discoverActions } from '../DiscoverContext';
import { DataPluginProvider } from '../DataPluginContext';

import { smallSampleLogData } from '../../data/sampleLogData';
import { BehaviorSubject } from 'rxjs';

// Mock the DataPlugin
jest.mock('../../data/plugin/data-plugin', () => {
  // Create mock observables
  const querySubject = new BehaviorSubject({ query: 'test query', language: 'kuery' });
  const filtersSubject = new BehaviorSubject([{ field: 'test', operator: 'eq', value: 'value' }]);
  const timeRangeSubject = new BehaviorSubject({ from: 'now-15m', to: 'now' });
  
  // Mock search source
  const mockSearchSource = {
    setField: jest.fn().mockReturnThis(),
    getField: jest.fn(),
    fetch: jest.fn().mockResolvedValue({
      hits: {
        total: 10,
        hits: [
          { 
            _id: '1', 
            _source: { 
              timestamp: new Date().toISOString(),
              source: 'test-source',
              message: 'Test message',
              level: 'info',
              agent: { id: '001', name: 'test-agent', ip: '127.0.0.1' },
              rule: { id: 1001, description: 'Test rule', level: 3, groups: ['test'] },
              location: '/var/log/test.log',
              decoder: { name: 'test-decoder' }
            } 
          }
        ]
      },
      took: 5,
      timed_out: false
    })
  };
  
  // Mock services
  const mockQueryStringManager = {
    getQuery: jest.fn().mockReturnValue({ query: 'test query', language: 'kuery' }),
    setQuery: jest.fn((query) => querySubject.next(query)),
    getUpdates$: jest.fn().mockReturnValue(querySubject)
  };
  
  const mockFilterManager = {
    getFilters: jest.fn().mockReturnValue([{ field: 'test', operator: 'eq', value: 'value' }]),
    setFilters: jest.fn((filters) => filtersSubject.next(filters)),
    getFiltersUpdate$: jest.fn().mockReturnValue(filtersSubject)
  };
  
  const mockTimeFilterService = {
    getTime: jest.fn().mockReturnValue({ from: 'now-15m', to: 'now' }),
    setTime: jest.fn((timeRange) => timeRangeSubject.next(timeRange)),
    getTimeUpdate$: jest.fn().mockReturnValue(timeRangeSubject)
  };
  
  const mockDatasetService = {
    getDatasets: jest.fn().mockReturnValue([
      { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
      { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
    ]),
    getDataset: jest.fn().mockImplementation((id) => {
      const datasets = [
        { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
        { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
      ];
      return datasets.find(d => d.id === id);
    }),
    cacheDataset: jest.fn().mockResolvedValue(undefined)
  };
  
  const mockQueryService = {
    queryString: mockQueryStringManager,
    filterManager: mockFilterManager,
    timefilter: mockTimeFilterService,
    getDatasetService: jest.fn().mockReturnValue(mockDatasetService),
    getLanguageService: jest.fn().mockReturnValue({})
  };
  
  const mockSearchService = {
    searchSource: {
      create: jest.fn().mockResolvedValue(mockSearchSource)
    }
  };
  
  const mockDataPlugin = {
    initialize: jest.fn().mockResolvedValue(undefined),
    getSearchService: jest.fn().mockReturnValue(mockSearchService),
    getQueryService: jest.fn().mockReturnValue(mockQueryService),
    getUiService: jest.fn().mockReturnValue({}),
    getFieldFormatsService: jest.fn().mockReturnValue({}),
    getAutocompleteService: jest.fn().mockReturnValue({}),
    getStorage: jest.fn().mockReturnValue({}),
    getIndexPatternService: jest.fn().mockReturnValue({})
  };
  
  return {
    DataPlugin: {
      getInstance: jest.fn().mockReturnValue(mockDataPlugin),
    },
  };
});

// Mock the hooks
jest.mock('../../hooks/dataHooks', () => {
  const originalModule = jest.requireActual('../../hooks/dataHooks');
  
  return {
    ...originalModule,
    useSearch: jest.fn().mockReturnValue({
      results: {
        hits: {
          total: 10,
          hits: [
            { 
              _id: '1', 
              _source: { 
                timestamp: new Date().toISOString(),
                source: 'test-source',
                message: 'Test message',
                level: 'info',
                agent: { id: '001', name: 'test-agent', ip: '127.0.0.1' },
                rule: { id: 1001, description: 'Test rule', level: 3, groups: ['test'] },
                location: '/var/log/test.log',
                decoder: { name: 'test-decoder' }
              } 
            }
          ]
        },
        took: 5,
        timed_out: false
      },
      isLoading: false,
      error: null,
      executeSearch: jest.fn()
    }),
    useQuery: jest.fn().mockReturnValue({
      query: { query: 'test query', language: 'kuery' },
      setQuery: jest.fn(),
      filters: [{ field: 'test', operator: 'eq', value: 'value' }],
      setFilters: jest.fn(),
      timeRange: { from: 'now-15m', to: 'now' },
      setTimeRange: jest.fn()
    }),
    useDataset: jest.fn().mockReturnValue({
      datasets: [
        { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
        { id: 'dataset2', title: 'Dataset 2', type: 'sample' }
      ],
      selectedDataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
      selectDataset: jest.fn()
    })
  };
});

// Test component that uses the useDiscover hook
const TestComponent: React.FC = () => {
  const { state, dispatch } = useDiscover();
  
  return (
    <div>
      <div data-testid="search-query">{state.searchQuery}</div>
      <div data-testid="loading-state">{state.isLoading ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="log-count">{state.logData.length}</div>
      <button 
        data-testid="set-query-button"
        onClick={() => dispatch(discoverActions.setSearchQuery('new query'))}
      >
        Set Query
      </button>
    </div>
  );
};

describe('DiscoverContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should initialize with legacy mode when useDataPlugin is false', async () => {
    render(
      <DiscoverProvider initialData={smallSampleLogData} useDataPlugin={false}>
        <TestComponent />
      </DiscoverProvider>
    );
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByTestId('loading-state')).toHaveTextContent('Not Loading');
    });
    
    // Check that data was loaded
    expect(screen.getByTestId('log-count')).toHaveTextContent(smallSampleLogData.length.toString());
  });
  
  it('should initialize with Data Plugin when useDataPlugin is true', async () => {
    render(
      <DataPluginProvider>
        <DiscoverProvider useDataPlugin={true}>
          <TestComponent />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByTestId('loading-state')).toHaveTextContent('Not Loading');
    });
    
    // Check that data was loaded from Data Plugin
    expect(screen.getByTestId('log-count')).toHaveTextContent('1');
    expect(screen.getByTestId('search-query')).toHaveTextContent('test query');
  });
  
  it('should update query and sync with Data Plugin', async () => {
    const { useQuery } = jest.requireMock('../../hooks/dataHooks');
    const mockSetQuery = jest.fn();
    useQuery.mockReturnValue({
      query: { query: 'test query', language: 'kuery' },
      setQuery: mockSetQuery,
      filters: [{ field: 'test', operator: 'eq', value: 'value' }],
      setFilters: jest.fn(),
      timeRange: { from: 'now-15m', to: 'now' },
      setTimeRange: jest.fn()
    });
    
    render(
      <DataPluginProvider>
        <DiscoverProvider useDataPlugin={true}>
          <TestComponent />
        </DiscoverProvider>
      </DataPluginProvider>
    );
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByTestId('loading-state')).toHaveTextContent('Not Loading');
    });
    
    // Click the button to update the query
    act(() => {
      screen.getByTestId('set-query-button').click();
    });
    
    // Check that the query was updated
    expect(screen.getByTestId('search-query')).toHaveTextContent('new query');
    
    // Check that setQuery was called with the new query
    await waitFor(() => {
      expect(mockSetQuery).toHaveBeenCalledWith(expect.objectContaining({
        query: 'new query',
        language: 'kuery'
      }));
    });
  });
  
  it('should throw an error when useDiscover is used outside of DiscoverProvider', () => {
    // Suppress console.error for this test
    const originalConsoleError = console.error;
    console.error = jest.fn();
    
    // Expect render to throw an error
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useDiscover must be used within a DiscoverProvider');
    
    // Restore console.error
    console.error = originalConsoleError;
  });
});