import { useMemo } from 'react';
import { useConfigurationAssessment } from '../context/ConfigurationAssessmentContext';
import { configurationAssessmentActions } from '../context/ConfigurationAssessmentActions';
import { 
  getAvailableConfigurationFields, 
  getFieldType, 
  formatFieldValue,
  defaultConfigurationFields
} from '../utils/configurationAdapters';

/**
 * Hook for managing fields and columns in the Configuration Assessment interface
 */
export const useConfigurationFields = () => {
  const { state, dispatch } = useConfigurationAssessment();
  const { selectedFields, configData } = state;
  
  // Get all available fields from the data
  const availableFields = useMemo(() => {
    return getAvailableConfigurationFields(configData);
  }, [configData]);
  
  // Toggle field selection
  const toggleField = (field: string) => {
    dispatch(configurationAssessmentActions.toggleField(field));
  };
  
  // Add field to selected fields
  const addField = (field: string) => {
    if (!selectedFields.includes(field)) {
      dispatch(configurationAssessmentActions.toggleField(field));
    }
  };
  
  // Remove field from selected fields
  const removeField = (field: string) => {
    if (selectedFields.includes(field)) {
      dispatch(configurationAssessmentActions.toggleField(field));
    }
  };
  
  // Reset to default fields
  const resetFields = () => {
    // Remove all current fields
    selectedFields.forEach(field => {
      dispatch(configurationAssessmentActions.toggleField(field));
    });
    
    // Add default fields
    defaultConfigurationFields.forEach(field => {
      if (!selectedFields.includes(field)) {
        dispatch(configurationAssessmentActions.toggleField(field));
      }
    });
  };
  
  return {
    selectedFields,
    availableFields,
    toggleField,
    addField,
    removeField,
    resetFields,
    getFieldType,
    formatFieldValue,
  };
};