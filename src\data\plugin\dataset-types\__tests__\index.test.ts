import { registerDatasetTypes } from '../index';
import { DataPlugin } from '../../data-plugin';
import { sampleDatasetType } from '../sample-dataset-type';
import { customSampleDatasetType } from '../custom-sample-dataset-type';

// Mock the DataPlugin
jest.mock('../../data-plugin');

describe('registerDatasetTypes', () => {
  let mockDataPlugin: jest.Mocked<DataPlugin>;
  let mockDatasetService: { registerType: jest.Mock };
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create a mock dataset service
    mockDatasetService = {
      registerType: jest.fn()
    };
    
    // Create a mock DataPlugin
    mockDataPlugin = {
      getQueryService: jest.fn().mockReturnValue({
        getDatasetService: jest.fn().mockReturnValue(mockDatasetService)
      })
    } as unknown as jest.Mocked<DataPlugin>;
  });
  
  it('should register all dataset types', () => {
    // Call the function
    registerDatasetTypes(mockDataPlugin);
    
    // Check that the dataset service was accessed
    expect(mockDataPlugin.getQueryService).toHaveBeenCalled();
    expect(mockDataPlugin.getQueryService().getDatasetService).toHaveBeenCalled();
    
    // Check that all dataset types were registered
    expect(mockDatasetService.registerType).toHaveBeenCalledTimes(2);
    expect(mockDatasetService.registerType).toHaveBeenCalledWith(sampleDatasetType);
    expect(mockDatasetService.registerType).toHaveBeenCalledWith(customSampleDatasetType);
  });
});