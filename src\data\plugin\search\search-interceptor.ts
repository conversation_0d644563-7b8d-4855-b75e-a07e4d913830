import { Observable, from } from 'rxjs';
import { SearchRequest, SearchOptions, SearchResponse } from '../interfaces';
import { SearchStrategyRegistry } from './search-strategy';
import { SampleSearchStrategy } from './sample-search-strategy';

// Create and initialize the search strategy registry
const searchStrategyRegistry = new SearchStrategyRegistry();
searchStrategyRegistry.register(new SampleSearchStrategy(), true); // Register as default

/**
 * Base class for search interceptors that implement the Chain of Responsibility pattern.
 * Each interceptor can transform the search request before passing it to the next interceptor
 * and can transform the response before returning it to the previous interceptor.
 * 
 * This pattern allows for a flexible and extensible search execution flow where
 * different interceptors can be added to the chain to handle specific aspects of
 * the search process, such as:
 * - Query language transformation
 * - Dataset-specific modifications
 * - Security filtering
 * - Result formatting
 * - Error handling
 */
export abstract class SearchInterceptor {
  protected next: SearchInterceptor | null = null;

  /**
   * Sets the next interceptor in the chain.
   * @param interceptor The next interceptor to process the request
   * @returns The next interceptor for chaining
   */
  public setNext(interceptor: SearchInterceptor): SearchInterceptor {
    this.next = interceptor;
    return interceptor;
  }

  /**
   * Gets the next interceptor in the chain.
   * @returns The next interceptor, or null if this is the last interceptor
   */
  public getNext(): SearchInterceptor | null {
    return this.next;
  }

  /**
   * Abstract method that must be implemented by concrete interceptors.
   * This method should:
   * 1. Transform the search request if needed
   * 2. Pass the request to the next interceptor or execute the search if this is the last interceptor
   * 3. Transform the response if needed
   * 
   * @param request The search request to process
   * @param options Options for the search execution
   * @returns An Observable of the search response
   */
  public abstract search(request: SearchRequest, options: SearchOptions): Observable<SearchResponse>;

  /**
   * Helper method to execute the search using the specified strategy.
   * This method is typically called by the last interceptor in the chain.
   * 
   * @param request The search request to execute
   * @param signal Optional AbortSignal to cancel the request
   * @param strategy Optional search strategy to use
   * @returns An Observable of the search response
   */
  protected runSearch(request: SearchRequest, signal?: AbortSignal, strategy?: string): Observable<SearchResponse> {
    try {
      // Get the appropriate search strategy
      let searchStrategy;
      
      if (strategy) {
        // Use the specified strategy if provided
        searchStrategy = searchStrategyRegistry.get(strategy);
      } else if (request.query.dataset) {
        // Use the strategy for the dataset if available
        searchStrategy = searchStrategyRegistry.getForDataset(request.query.dataset);
      } else {
        // Fall back to the default strategy
        searchStrategy = searchStrategyRegistry.getDefault();
      }
      
      if (!searchStrategy) {
        console.error(`No search strategy found for ${strategy || 'request'}`);
        return from(Promise.resolve({
          hits: {
            total: 0,
            hits: []
          },
          took: 0,
          timed_out: false,
          error: {
            message: `No search strategy found for ${strategy || 'request'}`,
            type: 'StrategyNotFoundError'
          }
        }));
      }
      
      // Execute the search using the selected strategy
      return from(searchStrategy.search(request, { signal }));
    } catch (error) {
      console.error('Error in runSearch:', error);
      return from(Promise.resolve({
        hits: {
          total: 0,
          hits: []
        },
        took: 0,
        timed_out: true,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error in search execution',
          type: error instanceof Error ? error.name : 'Error'
        }
      }));
    }
  }
}