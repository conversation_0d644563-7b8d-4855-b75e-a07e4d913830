import React, { useState } from 'react';

/**
 * Maps page component.
 * 
 * This component renders the Maps page content from the Explore section.
 */
const Maps: React.FC = () => {
  const [activeMap, setActiveMap] = useState('global');
  
  // Map selection options
  const mapOptions = [
    { id: 'global', name: 'Global Threat Map' },
    { id: 'network', name: 'Network Topology' },
    { id: 'infrastructure', name: 'Infrastructure Map' },
    { id: 'cloud', name: 'Cloud Resources' },
    { id: 'endpoints', name: 'Endpoint Distribution' }
  ];
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Maps
      </h1>
      
      {/* Map selection tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '24px',
      }}>
        {mapOptions.map((option) => (
          <div 
            key={option.id}
            onClick={() => setActiveMap(option.id)}
            style={{
              padding: '12px 24px',
              cursor: 'pointer',
              color: activeMap === option.id ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              borderBottom: activeMap === option.id ? '2px solid #00e5ff' : 'none',
              fontWeight: activeMap === option.id ? 500 : 400,
              fontSize: '14px',
            }}
          >
            {option.name}
          </div>
        ))}
      </div>
      
      {/* Map display area */}
      <div style={{
        borderRadius: '12px',
        background: 'rgba(16, 24, 45, 0.7)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        height: 'calc(100vh - 200px)',
        position: 'relative',
        overflow: 'hidden',
      }}>
        {/* Map placeholder with grid lines */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `
            linear-gradient(rgba(0, 229, 255, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(0, 229, 255, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
          opacity: 0.3,
        }} />
        
        {/* Map content - would be replaced with actual map component */}
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          color: 'rgba(255, 255, 255, 0.7)',
        }}>
          <div style={{ fontSize: '18px', marginBottom: '16px' }}>
            {mapOptions.find(option => option.id === activeMap)?.name}
          </div>
          <div style={{ fontSize: '14px' }}>
            Interactive map would be displayed here
          </div>
        </div>
        
        {/* Map controls */}
        <div style={{
          position: 'absolute',
          bottom: '20px',
          right: '20px',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        }}>
          {['+', '-', '⟳'].map((control, index) => (
            <div key={index} style={{
              width: '36px',
              height: '36px',
              borderRadius: '4px',
              background: 'rgba(0, 229, 255, 0.1)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              color: '#00e5ff',
              fontSize: '18px',
              cursor: 'pointer',
            }}>
              {control}
            </div>
          ))}
        </div>
        
        {/* Legend */}
        <div style={{
          position: 'absolute',
          bottom: '20px',
          left: '20px',
          padding: '12px',
          borderRadius: '8px',
          background: 'rgba(16, 24, 45, 0.9)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
        }}>
          <div style={{ fontSize: '14px', color: 'white', marginBottom: '8px' }}>Legend</div>
          {[
            { color: '#00e5ff', label: 'Secure' },
            { color: '#ffcc00', label: 'Warning' },
            { color: '#ff4d4d', label: 'Critical' },
            { color: '#9966ff', label: 'Unknown' }
          ].map((item, index) => (
            <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>
              <div style={{
                width: '10px',
                height: '10px',
                borderRadius: '50%',
                backgroundColor: item.color,
              }} />
              <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.7)' }}>{item.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Maps;