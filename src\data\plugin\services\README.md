# Data Plugin Services

This directory contains the implementations of the core services provided by the Data Plugin.

## QueryService

The QueryService is responsible for managing query state and providing access to related services. It includes:

- **QueryStringManager**: Manages the current query string and language
- **TimeFilterService**: Manages the current time range
- **FilterManager**: Manages the current filters
- Access to **DatasetService** and **LanguageService**

## DatasetService

The DatasetService is responsible for managing datasets and dataset types. It provides:

- Registration and management of dataset types
- Access to available datasets
- Dataset caching mechanism for improved performance
- Methods for creating datasets from paths

### Usage

```typescript
// Get the QueryService from the DataPlugin
const queryService = DataPlugin.getInstance().getQueryService();

// Get the current query
const query = queryService.queryString.getQuery();

// Set a new query
queryService.queryString.setQuery({
  query: 'status:error',
  language: 'kuery'
});

// Subscribe to query updates
const subscription = queryService.queryString.getUpdates$().subscribe((query) => {
  console.log('Query updated:', query);
});

// Get the current time range
const timeRange = queryService.timefilter.getTime();

// Set a new time range
queryService.timefilter.setTime({
  from: 'now-1h',
  to: 'now'
});

// Get the current filters
const filters = queryService.filterManager.getFilters();

// Add a filter
queryService.filterManager.addFilter({
  field: 'status',
  operator: 'is',
  value: 'error'
});

// Remove a filter
queryService.filterManager.removeFilter('status');

// Clear all filters
queryService.filterManager.clearFilters();

// Access the DatasetService
const datasetService = queryService.getDatasetService();

// Access the LanguageService
const languageService = queryService.getLanguageService();
```

### React Integration

The QueryService can be easily integrated with React components using hooks:

```typescript
import { useEffect, useState } from 'react';
import { DataPlugin } from '../data-plugin';
import { Query } from '../interfaces';

function useQuery() {
  const [query, setQuery] = useState<Query>({ query: '', language: 'kuery' });
  const queryService = DataPlugin.getInstance().getQueryService();

  useEffect(() => {
    const subscription = queryService.queryString.getUpdates$().subscribe((updatedQuery) => {
      setQuery(updatedQuery);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);

  const updateQuery = (newQuery: Query) => {
    queryService.queryString.setQuery(newQuery);
  };

  return { query, updateQuery };
}
```

See the `examples/query-service-example.tsx` file for a complete example of using the QueryService in a React component.
## Dataset
Service

### Usage

```typescript
// Get the DatasetService from the QueryService
const datasetService = queryService.getDatasetService();

// Register a dataset type
datasetService.registerType({
  id: 'sample-data',
  title: 'Sample Data',
  meta: {
    icon: 'sampleDataIcon',
    tooltip: 'Sample data for testing',
    searchOnLoad: true,
    isFieldLoadAsync: false
  },
  toDataset: (path) => ({
    id: `sample-${path[0].id}`,
    title: path[0].title,
    type: 'sample-data'
  }),
  fetch: async (services, path) => {
    // Fetch data structure
    return {
      id: 'sample-structure',
      title: 'Sample Structure',
      type: 'sample-data'
    };
  },
  fetchFields: async (dataset) => {
    // Return fields for the dataset
    return [
      {
        name: 'timestamp',
        type: 'date',
        searchable: true,
        aggregatable: true
      },
      {
        name: 'message',
        type: 'text',
        searchable: true,
        aggregatable: false
      }
    ];
  },
  supportedLanguages: () => ['kuery', 'lucene']
});

// Get all registered dataset types
const types = datasetService.getTypes();

// Create a dataset from a path
const path = [{ id: 'logs', title: 'Logs', type: 'folder' }];
const dataset = datasetService.createDataset('sample-data', path);

// Add a dataset to the available datasets
datasetService.addDataset({
  id: 'sample-logs',
  title: 'Sample Logs',
  type: 'sample-data',
  timeFieldName: 'timestamp'
});

// Get all available datasets
const datasets = datasetService.getDatasets();

// Get a dataset by ID
const logsDataset = datasetService.getDataset('sample-logs');

// Cache a dataset for faster access
await datasetService.cacheDataset(logsDataset, {});

// Check if a dataset is cached
const isCached = datasetService.isDatasetCached('sample-logs');

// Get cached fields for a dataset
const fields = datasetService.getCachedFields('sample-logs');

// Invalidate the cache for a dataset
datasetService.invalidateCache('sample-logs');

// Remove a dataset
datasetService.removeDataset('sample-logs');
```

### React Integration

The DatasetService can be integrated with React components using hooks:

```typescript
import { useEffect, useState } from 'react';
import { DataPlugin } from '../data-plugin';
import { Dataset, DatasetField } from '../interfaces';

function useDataset(datasetId: string) {
  const [dataset, setDataset] = useState<Dataset | undefined>(undefined);
  const [fields, setFields] = useState<DatasetField[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const queryService = DataPlugin.getInstance().getQueryService();
  const datasetService = queryService.getDatasetService();

  useEffect(() => {
    const fetchDataset = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const foundDataset = datasetService.getDataset(datasetId);
        
        if (!foundDataset) {
          throw new Error(`Dataset ${datasetId} not found`);
        }
        
        setDataset(foundDataset);
        
        // Check if dataset is cached
        if (!datasetService.isDatasetCached(datasetId)) {
          await datasetService.cacheDataset(foundDataset, {});
        }
        
        const cachedFields = datasetService.getCachedFields(datasetId);
        setFields(cachedFields || []);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchDataset();
  }, [datasetId, datasetService]);

  return { dataset, fields, loading, error };
}
```

See the `examples/dataset-service-example.tsx` file for a complete example of using the DatasetService in a React component.