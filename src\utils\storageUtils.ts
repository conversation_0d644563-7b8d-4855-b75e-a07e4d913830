/**
 * Utility functions for working with local storage
 */

// Constants for storage keys
const STORAGE_KEYS = {
  PAGE_SIZE: 'guardbear_page_size',
};

/**
 * Save page size preference to local storage
 * @param pageSize - The page size to save
 */
export const savePageSizePreference = (pageSize: number): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.PAGE_SIZE, pageSize.toString());
  } catch (error) {
    console.error('Error saving page size preference to local storage:', error);
  }
};

/**
 * Load page size preference from local storage
 * @param defaultPageSize - The default page size to use if no preference is found
 * @returns The saved page size or the default if none is found
 */
export const loadPageSizePreference = (defaultPageSize: number): number => {
  try {
    const savedPageSize = localStorage.getItem(STORAGE_KEYS.PAGE_SIZE);
    if (savedPageSize) {
      const parsedPageSize = parseInt(savedPageSize, 10);
      if (!isNaN(parsedPageSize) && parsedPageSize > 0) {
        return parsedPageSize;
      }
    }
  } catch (error) {
    console.error('Error loading page size preference from local storage:', error);
  }
  
  return defaultPageSize;
};