import React, { useState, useRef, useEffect } from 'react';
import { useConfigurationSearch } from '../../hooks/useConfigurationSearch';
import { useConfigurationTime } from '../../hooks/useConfigurationTime';
import ConfigurationSearchHelp from './ConfigurationSearchHelp';
import ConfigurationSearchSuggestions from './ConfigurationSearchSuggestions';
import ConfigurationFilterBadge from './ConfigurationFilterBadge';
import ConfigurationExport from './ConfigurationExport';

/**
 * Toolbar component for the Configuration Assessment page
 * Contains search bar, time range picker, and refresh controls
 * 
 * Requirements: 2.1, 2.2, 2.3, 2.4
 */
const ConfigurationToolbar: React.FC = () => {
  const { 
    searchQuery, 
    setSearchQuery, 
    counts, 
    appliedFilters, 
    addFilter, 
    removeFilter, 
    parseQuery
  } = useConfigurationSearch();
  
  const { 
    timeRange, 
    timeRangeOptions, 
    setTimeRangePreset, 
    setCustomTimeRange, 
    formatDate, 
    refresh, 
    autoRefresh, 
    toggleAutoRefresh, 
    refreshInterval, 
    setRefreshInterval 
  } = useConfigurationTime();
  
  // Local state for search input
  const [searchInput, setSearchInput] = useState(searchQuery);
  
  // State for search help and suggestions
  const [isSearchHelpVisible, setIsSearchHelpVisible] = useState(false);
  const [isSearchSuggestionsVisible, setIsSearchSuggestionsVisible] = useState(false);
  
  // Search input ref for positioning suggestions
  const searchInputRef = useRef<HTMLDivElement>(null);
  
  // State for time range picker dropdown
  const [isTimePickerOpen, setIsTimePickerOpen] = useState(false);
  const [isCustomTimePickerOpen, setIsCustomTimePickerOpen] = useState(false);
  const [customStartDate, setCustomStartDate] = useState(timeRange.start.toISOString().split('T')[0]);
  const [customStartTime, setCustomStartTime] = useState(timeRange.start.toISOString().split('T')[1].substring(0, 5));
  const [customEndDate, setCustomEndDate] = useState(timeRange.end.toISOString().split('T')[0]);
  const [customEndTime, setCustomEndTime] = useState(timeRange.end.toISOString().split('T')[1].substring(0, 5));
  
  // State for refresh settings dropdown
  const [isRefreshSettingsOpen, setIsRefreshSettingsOpen] = useState(false);
  
  // Refs for dropdown menus
  const timePickerRef = useRef<HTMLDivElement>(null);
  const refreshSettingsRef = useRef<HTMLDivElement>(null);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  };
  
  // Handle search form submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Parse the search input to extract structured filters
    const { textSearch, filters } = parseQuery(searchInput);
    
    // Apply any structured filters found in the search query
    filters.forEach(filter => {
      addFilter(filter.field, filter.value, filter.operator);
    });
    
    // Set the remaining text search
    setSearchQuery(textSearch);
  };
  
  // Handle time range change
  const handleTimeRangeChange = (preset: string) => {
    if (preset === 'custom') {
      setIsCustomTimePickerOpen(true);
    } else {
      setTimeRangePreset(preset);
      setIsTimePickerOpen(false);
    }
  };
  
  // Handle custom time range apply
  const handleApplyCustomTimeRange = () => {
    const startDateTime = new Date(`${customStartDate}T${customStartTime}:00`);
    const endDateTime = new Date(`${customEndDate}T${customEndTime}:00`);
    
    if (startDateTime < endDateTime) {
      setCustomTimeRange(startDateTime, endDateTime);
      setIsCustomTimePickerOpen(false);
      setIsTimePickerOpen(false);
    }
  };
  
  // Handle refresh interval change
  
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (timePickerRef.current && !timePickerRef.current.contains(event.target as Node)) {
        setIsTimePickerOpen(false);
        setIsCustomTimePickerOpen(false);
      }
      
      if (refreshSettingsRef.current && !refreshSettingsRef.current.contains(event.target as Node)) {
        setIsRefreshSettingsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Get result statistics from counts
  const stats = {
    totalCount: counts.total,
    filteredCount: counts.filtered,
    percentage: counts.total > 0 ? Math.round((counts.filtered / counts.total) * 100) : 0,
  };
  
  return (
    <div style={{
      padding: '16px',
      borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
      background: 'rgba(10, 14, 23, 0.8)',
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        {/* Search bar */}
        <div 
          ref={searchInputRef}
          style={{ 
            flexGrow: 1, 
            marginRight: '16px',
            position: 'relative',
          }}
        >
          <form 
            onSubmit={handleSearchSubmit}
            style={{ 
              display: 'flex',
            }}
          >
            <input
              type="text"
              value={searchInput}
              onChange={handleSearchChange}
              placeholder="Search configurations..."
              onFocus={() => setIsSearchSuggestionsVisible(true)}
              onBlur={() => setTimeout(() => setIsSearchSuggestionsVisible(false), 200)}
              style={{
                flexGrow: 1,
                padding: '8px 12px',
                background: 'rgba(0, 0, 0, 0.3)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderRadius: '4px 0 0 4px',
                color: 'white',
                fontSize: '14px',
              }}
            />
            <button
              type="submit"
              style={{
                background: 'rgba(0, 229, 255, 0.2)',
                border: '1px solid rgba(0, 229, 255, 0.3)',
                borderLeft: 'none',
                borderRadius: '0 4px 4px 0',
                color: '#00e5ff',
                padding: '0 12px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="11" cy="11" r="8" />
                <path d="M21 21l-4.35-4.35" />
              </svg>
            </button>
          </form>
          
          {/* Search suggestions */}
          <ConfigurationSearchSuggestions 
            isVisible={isSearchSuggestionsVisible} 
            searchInput={searchInput}
            onSelectSuggestion={(suggestion) => {
              setSearchInput(searchInput + suggestion);
            }}
          />
        </div>
        
        {/* Time range picker */}
        <div 
          ref={timePickerRef}
          style={{ 
            marginRight: '16px',
            position: 'relative',
          }}
        >
          <button
            onClick={() => setIsTimePickerOpen(!isTimePickerOpen)}
            style={{
              padding: '8px 12px',
              background: 'rgba(0, 0, 0, 0.3)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderRadius: '4px',
              color: 'white',
              fontSize: '14px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              whiteSpace: 'nowrap',
            }}
          >
            <span style={{ marginRight: '8px' }}>
              {timeRange.preset 
                ? timeRangeOptions.find(o => o.value === timeRange.preset)?.label 
                : `${formatDate(timeRange.start, false)} to ${formatDate(timeRange.end, false)}`
              }
            </span>
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </button>
          
          {/* Time range dropdown */}
          {isTimePickerOpen && (
            <div style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              marginTop: '4px',
              background: 'rgba(10, 14, 23, 0.95)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderRadius: '4px',
              padding: '8px 0',
              zIndex: 10,
              width: '250px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            }}>
              {/* Preset options */}
              {!isCustomTimePickerOpen && timeRangeOptions.map(option => (
                <div 
                  key={option.value}
                  onClick={() => handleTimeRangeChange(option.value)}
                  style={{
                    padding: '8px 16px',
                    cursor: 'pointer',
                    color: 'white',
                    background: timeRange.preset === option.value 
                      ? 'rgba(0, 229, 255, 0.1)' 
                      : 'transparent',
                    transition: 'background-color 0.2s',
                  }}
                  onMouseOver={(e) => {
                    e.currentTarget.style.background = 'rgba(0, 229, 255, 0.05)';
                  }}
                  onMouseOut={(e) => {
                    if (timeRange.preset !== option.value) {
                      e.currentTarget.style.background = 'transparent';
                    } else {
                      e.currentTarget.style.background = 'rgba(0, 229, 255, 0.1)';
                    }
                  }}
                >
                  {option.label}
                </div>
              ))}
              
              {/* Custom time picker */}
              {isCustomTimePickerOpen && (
                <div style={{ padding: '8px 16px' }}>
                  <h4 style={{ 
                    margin: '0 0 12px 0', 
                    color: 'white',
                    fontSize: '14px',
                  }}>
                    Custom Time Range
                  </h4>
                  
                  <div style={{ marginBottom: '12px' }}>
                    <div style={{ 
                      marginBottom: '4px',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '12px',
                    }}>
                      Start
                    </div>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <input
                        type="date"
                        value={customStartDate}
                        onChange={(e) => setCustomStartDate(e.target.value)}
                        style={{
                          padding: '6px',
                          background: 'rgba(0, 0, 0, 0.3)',
                          border: '1px solid rgba(0, 229, 255, 0.3)',
                          borderRadius: '4px',
                          color: 'white',
                          fontSize: '14px',
                          flexGrow: 1,
                        }}
                      />
                      <input
                        type="time"
                        value={customStartTime}
                        onChange={(e) => setCustomStartTime(e.target.value)}
                        style={{
                          padding: '6px',
                          background: 'rgba(0, 0, 0, 0.3)',
                          border: '1px solid rgba(0, 229, 255, 0.3)',
                          borderRadius: '4px',
                          color: 'white',
                          fontSize: '14px',
                          width: '90px',
                        }}
                      />
                    </div>
                  </div>
                  
                  <div style={{ marginBottom: '16px' }}>
                    <div style={{ 
                      marginBottom: '4px',
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: '12px',
                    }}>
                      End
                    </div>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <input
                        type="date"
                        value={customEndDate}
                        onChange={(e) => setCustomEndDate(e.target.value)}
                        style={{
                          padding: '6px',
                          background: 'rgba(0, 0, 0, 0.3)',
                          border: '1px solid rgba(0, 229, 255, 0.3)',
                          borderRadius: '4px',
                          color: 'white',
                          fontSize: '14px',
                          flexGrow: 1,
                        }}
                      />
                      <input
                        type="time"
                        value={customEndTime}
                        onChange={(e) => setCustomEndTime(e.target.value)}
                        style={{
                          padding: '6px',
                          background: 'rgba(0, 0, 0, 0.3)',
                          border: '1px solid rgba(0, 229, 255, 0.3)',
                          borderRadius: '4px',
                          color: 'white',
                          fontSize: '14px',
                          width: '90px',
                        }}
                      />
                    </div>
                  </div>
                  
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <button
                      onClick={() => setIsCustomTimePickerOpen(false)}
                      style={{
                        padding: '6px 12px',
                        background: 'transparent',
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        borderRadius: '4px',
                        color: 'white',
                        fontSize: '14px',
                        cursor: 'pointer',
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleApplyCustomTimeRange}
                      style={{
                        padding: '6px 12px',
                        background: 'rgba(0, 229, 255, 0.2)',
                        border: '1px solid rgba(0, 229, 255, 0.3)',
                        borderRadius: '4px',
                        color: '#00e5ff',
                        fontSize: '14px',
                        cursor: 'pointer',
                      }}
                    >
                      Apply
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Export button */}
        <ConfigurationExport />
        
        {/* Refresh controls */}
        <div 
          ref={refreshSettingsRef}
          style={{ 
            position: 'relative',
          }}
        >
          <button
            onClick={refresh}
            style={{
              background: 'rgba(0, 229, 255, 0.2)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderRadius: '4px 0 0 4px',
              color: '#00e5ff',
              padding: '8px 12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '14px',
            }}
          >
            <svg 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none" 
              stroke="currentColor" 
              strokeWidth="2"
              style={{ marginRight: '4px' }}
            >
              <path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15" />
            </svg>
            Refresh
          </button>
          
          <button
            onClick={() => setIsRefreshSettingsOpen(!isRefreshSettingsOpen)}
            style={{
              background: 'rgba(0, 229, 255, 0.2)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderLeft: 'none',
              borderRadius: '0 4px 4px 0',
              color: '#00e5ff',
              padding: '8px 6px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="6 9 12 15 18 9" />
            </svg>
          </button>
          
          {/* Refresh settings dropdown */}
          {isRefreshSettingsOpen && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '4px',
              background: 'rgba(10, 14, 23, 0.95)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderRadius: '4px',
              padding: '12px 16px',
              zIndex: 10,
              width: '220px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            }}>
              <h4 style={{ 
                margin: '0 0 12px 0', 
                color: 'white',
                fontSize: '14px',
              }}>
                Refresh Settings
              </h4>
              
              <div style={{ marginBottom: '12px' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  color: 'white',
                  fontSize: '14px',
                  cursor: 'pointer',
                }}>
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => toggleAutoRefresh(e.target.checked)}
                    style={{ marginRight: '8px' }}
                  />
                  Auto-refresh
                </label>
              </div>
              
              {autoRefresh && (
                <div style={{ marginBottom: '12px' }}>
                  <div style={{ 
                    marginBottom: '4px',
                    color: 'rgba(255, 255, 255, 0.7)',
                    fontSize: '12px',
                  }}>
                    Refresh interval
                  </div>
                  <select
                    value={refreshInterval}
                    onChange={(e) => setRefreshInterval(Number(e.target.value))}
                    style={{
                      width: '100%',
                      padding: '6px',
                      background: 'rgba(0, 0, 0, 0.3)',
                      border: '1px solid rgba(0, 229, 255, 0.3)',
                      borderRadius: '4px',
                      color: 'white',
                      fontSize: '14px',
                    }}
                  >
                    <option value={5000}>5 seconds</option>
                    <option value={10000}>10 seconds</option>
                    <option value={30000}>30 seconds</option>
                    <option value={60000}>1 minute</option>
                    <option value={300000}>5 minutes</option>
                    <option value={900000}>15 minutes</option>
                  </select>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Results count and filters */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}>
        <div style={{
          fontSize: '13px',
          color: 'rgba(255, 255, 255, 0.7)',
        }}>
          {stats.filteredCount} configurations
        </div>
        
        {/* Applied filters */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '8px',
        }}>
          {appliedFilters.length > 0 && (
            <>
              <span style={{
                fontSize: '13px',
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Filters:
              </span>
              
              {appliedFilters.map((filter, index) => (
                <ConfigurationFilterBadge 
                  key={`${filter.field}-${index}`}
                  filter={filter}
                  onRemove={removeFilter}
                  onEdit={(updatedFilter) => {
                    // Remove the old filter and add the updated one
                    removeFilter(filter.field.toString());
                    addFilter(updatedFilter.field.toString(), updatedFilter.value, updatedFilter.operator);
                  }}
                />
              ))}
              
              {appliedFilters.length > 0 && (
                <button
                  onClick={() => appliedFilters.forEach(filter => removeFilter(filter.field.toString()))}
                  style={{
                    background: 'transparent',
                    border: 'none',
                    color: 'rgba(255, 255, 255, 0.7)',
                    cursor: 'pointer',
                    padding: '4px 8px',
                    fontSize: '12px',
                    textDecoration: 'underline',
                  }}
                >
                  Clear all
                </button>
              )}
            </>
          )}
        </div>
      </div>
      
      {/* Search help tooltip */}
      <div style={{
        marginTop: '8px',
        fontSize: '12px',
        color: 'rgba(255, 255, 255, 0.5)',
        position: 'relative',
      }}>
        <span>Search syntax: </span>
        <code style={{ 
          background: 'rgba(0, 0, 0, 0.2)', 
          padding: '2px 4px', 
          borderRadius: '2px',
          fontFamily: 'monospace',
        }}>
          field:value
        </code>
        <span> or </span>
        <code style={{ 
          background: 'rgba(0, 0, 0, 0.2)', 
          padding: '2px 4px', 
          borderRadius: '2px',
          fontFamily: 'monospace',
        }}>
          "exact phrase"
        </code>
        <button
          onClick={() => setIsSearchHelpVisible(!isSearchHelpVisible)}
          style={{
            background: 'transparent',
            border: 'none',
            color: '#00e5ff',
            cursor: 'pointer',
            padding: '0 4px',
            fontSize: '12px',
            textDecoration: 'underline',
            marginLeft: '8px',
          }}
        >
          {isSearchHelpVisible ? 'Hide help' : 'Show more'}
        </button>
        
        {/* Search help component */}
        <ConfigurationSearchHelp 
          isVisible={isSearchHelpVisible} 
          onClose={() => setIsSearchHelpVisible(false)} 
        />
      </div>
    </div>
  );
};

export default ConfigurationToolbar;