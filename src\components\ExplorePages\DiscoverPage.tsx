import React from 'react';
import { DiscoverProvider } from '../../context/DiscoverContext';
import { DiscoverLayout } from '../Discover';

const DiscoverPage: React.FC = () => {
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 10,
      background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
    }}>
      <DiscoverProvider>
        <DiscoverLayout />
      </DiscoverProvider>
    </div>
  );
};

export default DiscoverPage;