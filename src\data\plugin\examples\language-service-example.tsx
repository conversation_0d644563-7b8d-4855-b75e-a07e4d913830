import React, { useState, useEffect } from 'react';
import { LanguageServiceImpl } from '../services/language-service';
import { simpleQueryLanguage } from '../language-configs/simple-query-language';
import { Completion } from '../interfaces';

/**
 * Example component demonstrating the use of LanguageService
 */
const LanguageServiceExample: React.FC = () => {
  const [query, setQuery] = useState<string>('');
  const [highlightedQuery, setHighlightedQuery] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(true);
  const [validationError, setValidationError] = useState<string | undefined>(undefined);
  const [completions, setCompletions] = useState<Completion[]>([]);
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  
  // Initialize language service
  const languageService = React.useMemo(() => {
    const service = new LanguageServiceImpl();
    
    // Register the simple query language
    service.registerLanguage(simpleQueryLanguage);
    
    return service;
  }, []);
  
  // Update highlighted query and validation when query changes
  useEffect(() => {
    // Highlight the query
    const highlighted = languageService.highlightQuery(query, 'simple');
    setHighlightedQuery(highlighted);
    
    // Validate the query
    const validation = languageService.validateQuery(query, 'simple');
    setIsValid(validation.valid);
    setValidationError(validation.error);
    
    // Get completions based on cursor position
    const getCompletions = async () => {
      const suggestions = await languageService.getCompletions(query, cursorPosition, 'simple');
      setCompletions(suggestions);
    };
    
    getCompletions();
  }, [query, cursorPosition, languageService]);
  
  // Handle query input change
  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(e.target.value);
    setCursorPosition(e.target.selectionStart || 0);
  };
  
  // Handle cursor position change
  const handleQuerySelect = (e: React.SyntheticEvent<HTMLInputElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };
  
  // Apply a completion suggestion
  const applyCompletion = (completion: Completion) => {
    const beforeCursor = query.substring(0, cursorPosition);
    const afterCursor = query.substring(cursorPosition);
    
    // Find the word being completed
    const wordRegex = /[\w.]*$/;
    const match = beforeCursor.match(wordRegex);
    
    if (match) {
      const currentWord = match[0];
      const newBeforeCursor = beforeCursor.substring(0, beforeCursor.length - currentWord.length) + completion.value;
      const newQuery = newBeforeCursor + afterCursor;
      
      setQuery(newQuery);
      setCursorPosition(newBeforeCursor.length);
    }
  };
  
  return (
    <div className="language-service-example">
      <h2>Language Service Example</h2>
      
      <div className="query-input">
        <label htmlFor="query">Query (Simple Query Language):</label>
        <input
          id="query"
          type="text"
          value={query}
          onChange={handleQueryChange}
          onSelect={handleQuerySelect}
          className={isValid ? 'valid' : 'invalid'}
          placeholder="Try typing 'level:' or 'timestamp:'"
        />
      </div>
      
      {!isValid && validationError && (
        <div className="validation-error">
          Error: {validationError}
        </div>
      )}
      
      <div className="highlighted-query">
        <h3>Highlighted Query:</h3>
        <div dangerouslySetInnerHTML={{ __html: highlightedQuery || '&nbsp;' }} />
      </div>
      
      {completions.length > 0 && (
        <div className="completions">
          <h3>Suggestions:</h3>
          <ul>
            {completions.map((completion, index) => (
              <li key={index} onClick={() => applyCompletion(completion)}>
                <span className="completion-value">{completion.value}</span>
                <span className="completion-meta">{completion.meta}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="available-languages">
        <h3>Available Languages:</h3>
        <ul>
          {languageService.getLanguages().map((language) => (
            <li key={language.id}>
              <strong>{language.name}</strong> ({language.id}): {language.description}
            </li>
          ))}
        </ul>
      </div>
      
      <style jsx>{`
        .language-service-example {
          padding: 20px;
          font-family: sans-serif;
        }
        
        .query-input {
          margin-bottom: 15px;
        }
        
        .query-input input {
          width: 100%;
          padding: 8px;
          font-family: monospace;
          font-size: 14px;
        }
        
        .query-input input.invalid {
          border: 2px solid #ff6b6b;
          background-color: #fff0f0;
        }
        
        .validation-error {
          color: #ff6b6b;
          margin-bottom: 15px;
        }
        
        .highlighted-query {
          background-color: #f5f5f5;
          padding: 10px;
          border-radius: 4px;
          font-family: monospace;
          margin-bottom: 15px;
        }
        
        .highlighted-query .field {
          color: #0066cc;
          font-weight: bold;
        }
        
        .highlighted-query .operator {
          color: #cc6600;
          font-weight: bold;
        }
        
        .highlighted-query .string {
          color: #008800;
        }
        
        .highlighted-query .paren {
          color: #666666;
          font-weight: bold;
        }
        
        .completions ul {
          list-style: none;
          padding: 0;
          margin: 0;
          background-color: white;
          border: 1px solid #ddd;
          border-radius: 4px;
          max-height: 200px;
          overflow-y: auto;
        }
        
        .completions li {
          padding: 8px 12px;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
        }
        
        .completions li:hover {
          background-color: #f0f0f0;
        }
        
        .completion-meta {
          color: #888;
          font-size: 0.9em;
        }
      `}</style>
    </div>
  );
};

export default LanguageServiceExample;