import { QueryCacheService } from '../query-cache-service';
import { SearchRequest, SearchResponse } from '../../interfaces';

describe('QueryCacheService', () => {
  let queryCacheService: QueryCacheService;
  let mockRequest: SearchRequest;
  let mockResponse: SearchResponse;
  
  beforeEach(() => {
    // Create a new instance of QueryCacheService for each test
    queryCacheService = new QueryCacheService();
    
    // Create mock request and response
    mockRequest = {
      query: { query: 'test', language: 'kuery' },
      index: 'test-index',
      size: 10
    };
    
    mockResponse = {
      hits: {
        total: 2,
        hits: [
          {
            _id: '1',
            _source: { field1: 'value1' }
          },
          {
            _id: '2',
            _source: { field1: 'value2' }
          }
        ]
      },
      took: 5,
      timed_out: false
    };
  });
  
  it('should cache and retrieve search results', () => {
    // Set a value in the cache
    queryCacheService.set(mockRequest, mockResponse);
    
    // Get the value from the cache
    const cachedResponse = queryCacheService.get(mockRequest);
    
    // Verify that the cached response matches the original
    expect(cachedResponse).toEqual(mockResponse);
  });
  
  it('should return undefined for non-existent cache entries', () => {
    const nonExistentRequest = {
      query: { query: 'non-existent', language: 'kuery' },
      index: 'test-index',
      size: 10
    };
    
    const cachedResponse = queryCacheService.get(nonExistentRequest);
    
    expect(cachedResponse).toBeUndefined();
  });
  
  it('should handle cache expiration', () => {
    // Set a value in the cache with a very short expiration
    queryCacheService.set(mockRequest, mockResponse, 1); // 1ms expiration
    
    // Wait for the cache to expire
    return new Promise<void>(resolve => {
      setTimeout(() => {
        const cachedResponse = queryCacheService.get(mockRequest);
        expect(cachedResponse).toBeUndefined();
        resolve();
      }, 10);
    });
  });
  
  it('should invalidate cache for a specific dataset', () => {
    // Set values in the cache for different datasets
    const request1 = {
      query: { query: 'test', language: 'kuery' },
      index: 'dataset1',
      size: 10
    };
    
    const request2 = {
      query: { query: 'test', language: 'kuery' },
      index: 'dataset2',
      size: 10
    };
    
    queryCacheService.set(request1, mockResponse);
    queryCacheService.set(request2, mockResponse);
    
    // Invalidate cache for dataset1
    queryCacheService.invalidateForDataset('dataset1');
    
    // Verify that dataset1 is no longer cached but dataset2 still is
    expect(queryCacheService.get(request1)).toBeUndefined();
    expect(queryCacheService.get(request2)).toEqual(mockResponse);
  });
  
  it('should clear the entire cache', () => {
    // Set values in the cache
    queryCacheService.set(mockRequest, mockResponse);
    
    // Clear the cache
    queryCacheService.clear();
    
    // Verify that the cache is empty
    expect(queryCacheService.size()).toBe(0);
    expect(queryCacheService.get(mockRequest)).toBeUndefined();
  });
  
  it('should evict least valuable entries when cache is full', () => {
    // Set the MAX_CACHE_SIZE to a small value for testing
    // @ts-expect-error - accessing private property for testing
    queryCacheService['MAX_CACHE_SIZE'] = 3;
    
    // Create multiple requests
    const requests = Array.from({ length: 4 }, (_, i) => ({
      query: { query: `test${i}`, language: 'kuery' },
      index: `index${i}`,
      size: 10
    }));
    
    // Fill the cache
    requests.forEach(request => {
      queryCacheService.set(request, mockResponse);
    });
    
    // Verify that the cache size is limited to MAX_CACHE_SIZE
    expect(queryCacheService.size()).toBe(3);
    
    // The first request should have been evicted
    expect(queryCacheService.get(requests[0])).toBeUndefined();
    
    // The later requests should still be cached
    expect(queryCacheService.get(requests[1])).toBeDefined();
    expect(queryCacheService.get(requests[2])).toBeDefined();
    expect(queryCacheService.get(requests[3])).toBeDefined();
  });
  
  it('should extend expiration time for frequently accessed entries', () => {
    // Set the ADAPTIVE_EXPIRATION to true for testing
    // @ts-expect-error - accessing private property for testing
    queryCacheService['ADAPTIVE_EXPIRATION'] = true;
    
    // Set a value in the cache
    queryCacheService.set(mockRequest, mockResponse, 100); // 100ms expiration
    
    // Access the cache entry multiple times to increase hit count
    for (let i = 0; i < 10; i++) {
      queryCacheService.get(mockRequest);
    }
    
    // Get the cached entry to check its expiration time
    // @ts-expect-error - accessing private property for testing
    const cachedEntry = queryCacheService['cache'].get(queryCacheService['generateCacheKey'](mockRequest));
    
    // Verify that the expiration time has been extended
    expect(cachedEntry).toBeDefined();
    if (cachedEntry) {
      // The expiration time should be greater than the original 100ms
      expect(cachedEntry.expiresAt - cachedEntry.timestamp).toBeGreaterThan(100);
    }
  });
});