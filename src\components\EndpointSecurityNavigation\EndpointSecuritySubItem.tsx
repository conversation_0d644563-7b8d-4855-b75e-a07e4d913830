import React, { useState } from 'react';

interface EndpointSecuritySubItemProps {
  id: string;
  label: string;
  icon: React.ComponentType;
  isActive: boolean;
  isCollapsed: boolean;
  onClick: (id: string) => void;
}

const EndpointSecuritySubItem: React.FC<EndpointSecuritySubItemProps> = ({
  id,
  label,
  icon: Icon,
  isActive,
  isCollapsed,
  onClick
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getSubItemStyle = () => ({
    width: isCollapsed ? '32px' : '100%',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    gap: isCollapsed ? '0' : '8px',
    padding: isCollapsed ? '0' : '0 8px',
    borderRadius: '6px',
    cursor: 'pointer',
    color: isActive ? '#00e5ff' : (isHovered ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.7)'),
    backgroundColor: isActive 
      ? 'rgba(0, 229, 255, 0.15)' 
      : (isHovered ? 'rgba(255, 255, 255, 0.05)' : 'transparent'),
    border: isActive ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
    boxShadow: isActive ? '0 0 8px rgba(0, 229, 255, 0.3)' : 'none',
    transition: 'all 0.2s ease',
    position: 'relative' as const,
    justifyContent: isCollapsed ? 'center' : 'flex-start',
  });

  const getLabelStyle = () => ({
    fontSize: '13px',
    fontWeight: isActive ? 500 : 400,
    opacity: isCollapsed ? 0 : 1,
    transition: 'opacity 0.2s ease',
    whiteSpace: 'nowrap' as const,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  });

  const handleClick = () => {
    onClick(id);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleClick();
    }
  };

  return (
    <div
      style={getSubItemStyle()}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onKeyDown={handleKeyDown}
      role="menuitem"
      tabIndex={0}
      aria-label={`Navigate to ${label}`}
      aria-current={isActive ? 'page' : undefined}
      title={isCollapsed ? label : undefined}
    >
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center',
        minWidth: '18px',
        height: '18px'
      }}>
        <Icon />
      </div>
      {!isCollapsed && (
        <span style={getLabelStyle()}>
          {label}
        </span>
      )}
      {isActive && (
        <div style={{
          position: 'absolute',
          left: '-2px',
          top: '50%',
          transform: 'translateY(-50%)',
          width: '3px',
          height: '16px',
          backgroundColor: '#00e5ff',
          borderRadius: '0 2px 2px 0',
          boxShadow: '0 0 4px rgba(0, 229, 255, 0.8)',
        }} />
      )}
    </div>
  );
};

export default EndpointSecuritySubItem; 