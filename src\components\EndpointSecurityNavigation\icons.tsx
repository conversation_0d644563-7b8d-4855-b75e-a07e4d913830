import React from 'react';

const iconProps = {
  style: { pointerEvents: "none" as const }
};

export const EndpointSecurityIcon = () => (
  <svg {...iconProps} viewBox="0 0 24 24" width="22" height="22" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="3" y="3" width="18" height="18" rx="4"/>
    <path d="M12 8v4l3 3"/>
    <circle cx="12" cy="12" r="10" stroke="rgba(0,229,255,0.3)" strokeWidth="1" fill="none"/>
  </svg>
);

export const ConfigurationAssessmentIcon = () => (
  <svg {...iconProps} viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="4" y="4" width="16" height="16" rx="2"/>
    <path d="M8 12h8M8 16h8M8 8h8"/>
  </svg>
);

export const MalwareDetectionIcon = () => (
  <svg {...iconProps} viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"/>
    <path d="M8 15l4-4 4 4"/>
    <path d="M12 11V7"/>
  </svg>
);

export const FileIntegrityMonitoringIcon = () => (
  <svg {...iconProps} viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <rect x="3" y="3" width="18" height="18" rx="2"/>
    <path d="M9 9h6v6H9z"/>
  </svg>
);

export const ChevronDownIcon = () => (
  <svg
    {...iconProps}
    viewBox="0 0 24 24"
    width="16"
    height="16"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polyline points="6 9 12 15 18 9"/>
  </svg>
); 