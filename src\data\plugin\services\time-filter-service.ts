import { TimeFilterService, TimeRange } from '../interfaces';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Implementation of the TimeFilterService interface
 * TimeFilterService is responsible for managing the current time range
 */
export class TimeFilterServiceImpl implements TimeFilterService {
  private timeSubject: BehaviorSubject<TimeRange>;
  
  /**
   * Constructor for TimeFilterServiceImpl
   * @param initialTime The initial time range
   */
  constructor(initialTime: TimeRange = { from: 'now-15m', to: 'now' }) {
    this.timeSubject = new BehaviorSubject<TimeRange>(initialTime);
  }

  /**
   * Gets the current time range
   * @returns The current time range
   */
  public getTime(): TimeRange {
    return this.timeSubject.getValue();
  }

  /**
   * Sets a new time range
   * @param time The new time range
   */
  public setTime(time: TimeRange): void {
    this.timeSubject.next(time);
  }

  /**
   * Gets an observable that emits when the time range changes
   * @returns An observable of TimeRange
   */
  public getTimeUpdate$(): Observable<TimeRange> {
    return this.timeSubject.asObservable();
  }
}