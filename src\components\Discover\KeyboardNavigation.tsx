import React, { useEffect } from 'react';
import { useDiscoverLogs } from '../../hooks';

/**
 * Component to handle keyboard navigation for the Discover page
 */
const KeyboardNavigation: React.FC = () => {
  const { 
    logs, 
    expandedLogs, 
    expandLog, 
    collapseLog, 
    collapseAllLogs 
  } = useDiscoverLogs();
  
  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard navigation when not in an input field
      if (document.activeElement?.tagName === 'INPUT' || 
          document.activeElement?.tagName === 'TEXTAREA' || 
          document.activeElement?.tagName === 'SELECT') {
        return;
      }
      
      // Get expanded log IDs
      const expandedLogIds = Object.keys(expandedLogs).filter(id => expandedLogs[id]);
      
      switch (e.key) {
        case 'Escape':
          // Collapse all logs
          collapseAllLogs();
          break;
          
        case 'ArrowDown':
          // Navigate to next log
          if (expandedLogIds.length > 0) {
            const lastExpandedId = expandedLogIds[expandedLogIds.length - 1];
            const lastExpandedIndex = logs.findIndex(log => log.id === lastExpandedId);
            
            if (lastExpandedIndex < logs.length - 1) {
              // Expand next log
              expandLog(logs[lastExpandedIndex + 1].id);
            }
          } else if (logs.length > 0) {
            // Expand first log
            expandLog(logs[0].id);
          }
          break;
          
        case 'ArrowUp':
          // Navigate to previous log
          if (expandedLogIds.length > 0) {
            const firstExpandedId = expandedLogIds[0];
            const firstExpandedIndex = logs.findIndex(log => log.id === firstExpandedId);
            
            if (firstExpandedIndex > 0) {
              // Expand previous log
              expandLog(logs[firstExpandedIndex - 1].id);
            }
          } else if (logs.length > 0) {
            // Expand last log
            expandLog(logs[logs.length - 1].id);
          }
          break;
          
        case 'ArrowRight':
          // Expand selected log
          if (expandedLogIds.length === 1) {
            // Already expanded, do nothing
          } else if (logs.length > 0) {
            // Expand first log
            expandLog(logs[0].id);
          }
          break;
          
        case 'ArrowLeft':
          // Collapse selected log
          if (expandedLogIds.length === 1) {
            collapseLog(expandedLogIds[0]);
          }
          break;
      }
    };
    
    // Add event listener
    window.addEventListener('keydown', handleKeyDown);
    
    // Remove event listener on cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [logs, expandedLogs, expandLog, collapseLog, collapseAllLogs]);
  
  // This component doesn't render anything
  return null;
};

export default KeyboardNavigation;