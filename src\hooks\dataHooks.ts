import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useDataPlugin } from '../context/DataPluginContext';
import {
  SearchResponse,
  Query,
  Dataset,
  Filter,
  TimeRange
} from '../data/plugin/interfaces';
import { errorHand<PERSON>, ErrorCategory } from '../services/ErrorHandler';

/**
 * Hook for executing searches
 * @returns Object with search state and functions
 */
export const useSearch = () => {
  const dataPlugin = useDataPlugin();
  const [results, setResults] = useState<SearchResponse | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Get the query service to subscribe to query updates
  const queryService = dataPlugin.getQueryService();

  // Get current query state for memoization
  const currentQuery = useMemo(() => queryService.queryString.getQuery(), [queryService]);
  const currentFilters = useMemo(() => queryService.filterManager.getFilters(), [queryService]);
  const currentTimeRange = useMemo(() => queryService.timefilter.getTime(), [queryService]);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Execute search with the current query
  const executeSearch = useCallback(async (options = { bypassCache: false }) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const searchService = dataPlugin.getSearchService();
      const searchSource = await searchService.searchSource.create();
      
      // Configure the search
      searchSource.setField('query', { 
        query: currentQuery.query, 
        language: currentQuery.language 
      });
      
      // Set the dataset if available
      if (currentQuery.dataset) {
        searchSource.setField('index', currentQuery.dataset.id);
      }
      
      // Set filters
      if (currentFilters && currentFilters.length > 0) {
        searchSource.setField('filters', currentFilters);
      }
      
      // Set time range
      if (currentTimeRange) {
        searchSource.setField('timeRange', currentTimeRange);
      }
      
      // Execute the search
      const response = await searchSource.fetch(options);
      setResults(response);
    } catch (err) {
      // Use the error handler to process the error
      const errorInfo = errorHandler.handleError(err);
      
      // Set a more user-friendly error message based on the category
      let friendlyError: Error;
      
      switch (errorInfo.category) {
        case ErrorCategory.NETWORK:
          friendlyError = new Error('Unable to connect to the search service. Please check your network connection.');
          break;
        case ErrorCategory.SERVER:
          friendlyError = new Error('The search service encountered an error. Please try again later.');
          break;
        default:
          friendlyError = new Error(errorInfo.message);
      }
      
      setError(friendlyError);
    } finally {
      setIsLoading(false);
    }
  }, [dataPlugin, currentQuery, currentFilters, currentTimeRange]);
  
  // Debounced search execution
  const debouncedExecuteSearch = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      console.log('[useSearch] Executing debounced search');
      executeSearch();
    }, 150); // 150ms debounce delay
  }, [executeSearch]);

  // Subscribe to query updates
  useEffect(() => {
    console.log('[useSearch] Setting up query subscription');
    const subscription = queryService.queryString.getUpdates$().subscribe(() => {
      console.log('[useSearch] Query updated, executing debounced search');
      debouncedExecuteSearch();
    });

    // Initial search (not debounced)
    console.log('[useSearch] Executing initial search');
    executeSearch();

    return () => {
      console.log('[useSearch] Cleaning up query subscription');
      subscription.unsubscribe();
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [queryService, executeSearch, debouncedExecuteSearch]);
  
  return { 
    results, 
    isLoading, 
    error, 
    executeSearch 
  };
};

/**
 * Hook for managing queries
 * @returns Object with query state and functions
 */
export const useQuery = () => {
  const dataPlugin = useDataPlugin();
  const queryService = dataPlugin.getQueryService();
  const [query, setQueryState] = useState<Query>(queryService.queryString.getQuery());
  
  // Update the query
  const setQuery = useCallback((newQuery: Query) => {
    console.log('[useQuery] Setting query:', newQuery);
    queryService.queryString.setQuery(newQuery);
  }, [queryService]);

  // Subscribe to query updates
  useEffect(() => {
    const subscription = queryService.queryString.getUpdates$().subscribe((updatedQuery) => {
      console.log('[useQuery] Query updated:', updatedQuery);
      setQueryState(updatedQuery);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);
  
  // Get and set filters
  const [filters, setFiltersState] = useState<Filter[]>(queryService.filterManager.getFilters());
  
  const setFilters = useCallback((newFilters: Filter[]) => {
    console.log('[useQuery] Setting filters:', newFilters);
    queryService.filterManager.setFilters(newFilters);
  }, [queryService]);

  // Subscribe to filter updates
  useEffect(() => {
    const subscription = queryService.filterManager.getFiltersUpdate$().subscribe((updatedFilters) => {
      console.log('[useQuery] Filters updated:', updatedFilters);
      setFiltersState(updatedFilters);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);
  
  // Get and set time range
  const [timeRange, setTimeRangeState] = useState<TimeRange>(queryService.timefilter.getTime());
  
  const setTimeRange = useCallback((newTimeRange: TimeRange) => {
    console.log('[useQuery] Setting time range:', newTimeRange);
    queryService.timefilter.setTime(newTimeRange);
  }, [queryService]);

  // Subscribe to time range updates
  useEffect(() => {
    const subscription = queryService.timefilter.getTimeUpdate$().subscribe((updatedTimeRange) => {
      console.log('[useQuery] Time range updated:', updatedTimeRange);
      setTimeRangeState(updatedTimeRange);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);
  
  return {
    query,
    setQuery,
    filters,
    setFilters,
    timeRange,
    setTimeRange
  };
};

/**
 * Hook for managing datasets
 * @returns Object with dataset state and functions
 */
export const useDataset = () => {
  const dataPlugin = useDataPlugin();
  const queryService = dataPlugin.getQueryService();
  const datasetService = queryService.getDatasetService();
  
  const [datasets] = useState<Dataset[]>(datasetService.getDatasets());
  const [selectedDataset, setSelectedDatasetState] = useState<Dataset | undefined>(() => {
    const currentQuery = queryService.queryString.getQuery();
    return currentQuery.dataset;
  });
  
  // Select a dataset
  const selectDataset = useCallback(async (datasetId: string) => {
    const dataset = datasetService.getDataset(datasetId);
    
    if (dataset) {
      // Update the current query with the selected dataset
      const currentQuery = queryService.queryString.getQuery();
      queryService.queryString.setQuery({
        ...currentQuery,
        dataset
      });
      
      // Cache the dataset if needed
      await datasetService.cacheDataset(dataset, dataPlugin);
    }
  }, [dataPlugin, datasetService, queryService]);
  
  // Subscribe to query updates to track dataset changes
  useEffect(() => {
    const subscription = queryService.queryString.getUpdates$().subscribe((updatedQuery) => {
      if (updatedQuery.dataset) {
        setSelectedDatasetState(updatedQuery.dataset);
      }
    });
    
    return () => {
      subscription.unsubscribe();
    };
  }, [queryService]);
  
  return {
    datasets,
    selectedDataset,
    selectDataset
  };
};