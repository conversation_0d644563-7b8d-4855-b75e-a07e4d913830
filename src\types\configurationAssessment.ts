import { ConfigurationEntry, ConfigurationFilter, ConfigurationSort } from './configuration';

/**
 * Configuration Assessment State interface
 */
export interface ConfigurationAssessmentState {
  searchQuery: string;
  timeRange: {
    start: Date;
    end: Date;
    preset?: string;
  };
  selectedFields: string[];
  appliedFilters: ConfigurationFilter[];
  configData: ConfigurationEntry[];
  filteredData: ConfigurationEntry[];
  isLoading: boolean;
  error: string | null;
  autoRefresh: boolean;
  refreshInterval: number;
  pagination: {
    currentPage: number;
    pageSize: number;
    totalItems: number;
  };
  sort: ConfigurationSort | null;
  processingOperation: boolean; // Flag for ongoing operations
}

/**
 * Configuration Assessment Action types
 */
export type ConfigurationAssessmentAction =
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_TIME_RANGE'; payload: { start: Date; end: Date; preset?: string } }
  | { type: 'ADD_FILTER'; payload: ConfigurationFilter }
  | { type: 'REMOVE_FILTER'; payload: string }
  | { type: 'TOGGLE_FIELD'; payload: string }
  | { type: 'SET_CONFIG_DATA'; payload: ConfigurationEntry[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_AUTO_REFRESH'; payload: boolean }
  | { type: 'SET_REFRESH_INTERVAL'; payload: number }
  | { type: 'SET_CURRENT_PAGE'; payload: number }
  | { type: 'SET_PAGE_SIZE'; payload: number }
  | { type: 'SET_SORT'; payload: ConfigurationSort | null }
  | { type: 'SET_PROCESSING_OPERATION'; payload: boolean };
