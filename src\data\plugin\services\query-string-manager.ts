import { QueryStringManager, Query } from '../interfaces';
import { BehaviorSubject, Observable } from 'rxjs';

/**
 * Implementation of the QueryStringManager interface
 * QueryStringManager is responsible for managing the current query state
 */
export class QueryStringManagerImpl implements QueryStringManager {
  private querySubject: BehaviorSubject<Query>;
  
  /**
   * Constructor for QueryStringManagerImpl
   * @param initialQuery The initial query state
   */
  constructor(initialQuery: Query = { query: '', language: 'kuery' }) {
    this.querySubject = new BehaviorSubject<Query>(initialQuery);
  }

  /**
   * Gets the current query
   * @returns The current query
   */
  public getQuery(): Query {
    return this.querySubject.getValue();
  }

  /**
   * Sets a new query
   * @param query The new query
   */
  public setQuery(query: Query): void {
    this.querySubject.next(query);
  }

  /**
   * Gets an observable that emits when the query changes
   * @returns An observable of Query
   */
  public getUpdates$(): Observable<Query> {
    return this.querySubject.asObservable();
  }
}