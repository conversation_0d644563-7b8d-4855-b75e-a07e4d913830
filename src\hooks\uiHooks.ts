import { useState, useCallback } from 'react';
import { useDataPlugin } from '../context/DataPluginContext';
import { FieldFormat, Completion } from '../data/plugin/interfaces';

/**
 * Hook for accessing UI components
 * @param componentId The ID of the component to access
 * @returns The component or undefined if not found
 */
export const useUiComponent = (componentId: string) => {
  const dataPlugin = useDataPlugin();
  const uiService = dataPlugin.getUiService();
  
  // Get the component
  const component = uiService.getComponent(componentId);
  
  return component;
};

/**
 * Hook for field formatting
 * @param fieldType The type of field to format
 * @param formatId Optional format ID, uses default if not provided
 * @returns Object with format function and format ID
 */
export const useFieldFormat = (fieldType: string, formatId?: string) => {
  const dataPlugin = useDataPlugin();
  const fieldFormatsService = dataPlugin.getFieldFormatsService();
  
  // Get the format
  const [format] = useState<FieldFormat | undefined>(() => {
    if (formatId) {
      return fieldFormatsService.getFormat(fieldType, formatId);
    } else {
      return fieldFormatsService.getDefaultFormat(fieldType);
    }
  });
  
  // Format a value
  const formatValue = useCallback((value: unknown, params?: Record<string, unknown>) => {
    if (format) {
      return format.format(value, params);
    }
    return String(value);
  }, [format]);
  
  return {
    format,
    formatValue,
    formatId: format?.id
  };
};

/**
 * Hook for autocomplete
 * @param type The type of autocomplete provider to use
 * @returns Object with getCompletions function
 */
export const useAutocomplete = (type: string) => {
  const dataPlugin = useDataPlugin();
  const autocompleteService = dataPlugin.getAutocompleteService();
  
  // Get the provider
  const provider = autocompleteService.getProvider(type);
  
  // Get completions
  const getCompletions = useCallback(async (context: unknown): Promise<Completion[]> => {
    if (provider) {
      return await provider.getCompletions(context);
    }
    return [];
  }, [provider]);
  
  return {
    getCompletions,
    hasProvider: !!provider
  };
};

/**
 * Hook for language-specific autocomplete
 * @param language The language to get completions for
 * @returns Object with getCompletions function
 */
export const useLanguageAutocomplete = (language: string) => {
  const dataPlugin = useDataPlugin();
  const queryService = dataPlugin.getQueryService();
  const languageService = queryService.getLanguageService();
  
  // Get the language config
  const languageConfig = languageService.getLanguage(language);
  
  // Get completions
  const getCompletions = useCallback(async (query: string, position: number): Promise<Completion[]> => {
    if (languageConfig?.autocomplete) {
      return await languageConfig.autocomplete.getCompletions(query, position);
    }
    return [];
  }, [languageConfig]);
  
  // Validate query
  const validateQuery = useCallback((query: string) => {
    if (languageConfig?.syntax?.validate) {
      return languageConfig.syntax.validate(query);
    }
    return { valid: true };
  }, [languageConfig]);
  
  // Highlight query
  const highlightQuery = useCallback((query: string) => {
    if (languageConfig?.syntax?.highlight) {
      return languageConfig.syntax.highlight(query);
    }
    return query;
  }, [languageConfig]);
  
  return {
    getCompletions,
    validateQuery,
    highlightQuery,
    hasAutocomplete: !!languageConfig?.autocomplete
  };
};

/**
 * Hook for accessing UI components with state
 * @param componentId The ID of the component to access
 * @param initialProps Initial props for the component
 * @returns Object with component and props state
 */
export const useUiComponentWithState = (componentId: string, initialProps: Record<string, unknown> = {}) => {
  const component = useUiComponent(componentId);
  const [props, setProps] = useState(initialProps);
  
  // Update props
  const updateProps = useCallback((newProps: Record<string, unknown>) => {
    setProps(prevProps => ({
      ...prevProps,
      ...newProps
    }));
  }, []);
  
  return {
    component,
    props,
    updateProps
  };
};