import React, { useState, useRef, useEffect } from 'react';

interface TableColumnResizerProps {
  index: number;
  onResize: (index: number, width: number) => void;
}

/**
 * Component for resizing table columns
 */
const TableColumnResizer: React.FC<TableColumnResizerProps> = ({ index, onResize }) => {
  const [isDragging, setIsDragging] = useState(false);
  const startXRef = useRef<number>(0);
  const startWidthRef = useRef<number>(0);
  const columnRef = useRef<HTMLElement | null>(null);
  
  // Handle mouse down to start resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Find the parent column element
    const column = e.currentTarget.parentElement as HTMLElement;
    columnRef.current = column;
    
    // Store initial values
    startXRef.current = e.clientX;
    startWidthRef.current = column.offsetWidth;
    
    // Start dragging
    setIsDragging(true);
    
    // Add event listeners for dragging
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };
  
  // Handle mouse move during resizing
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !columnRef.current) return;
      
      const deltaX = e.clientX - startXRef.current;
      const newWidth = Math.max(50, startWidthRef.current + deltaX);
      
      columnRef.current.style.width = `${newWidth}px`;
      columnRef.current.style.minWidth = `${newWidth}px`;
      columnRef.current.style.maxWidth = `${newWidth}px`;
      columnRef.current.style.flexGrow = '0';
      
      onResize(index, newWidth);
    };
    
    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, index, onResize]);
  
  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        right: 0,
        width: '8px',
        height: '100%',
        cursor: 'col-resize',
        zIndex: 1,
      }}
      onMouseDown={handleMouseDown}
    />
  );
};

export default TableColumnResizer;