import { SearchRequest, SearchResponse, Dataset } from '../interfaces';

/**
 * Interface for search strategies
 * Search strategies are responsible for executing search requests against specific data sources
 */
export interface SearchStrategy {
  /**
   * The unique identifier for this search strategy
   */
  id: string;
  
  /**
   * Executes a search request
   * @param request The search request to execute
   * @param options Additional options for the search
   * @returns A promise that resolves to the search response
   */
  search(request: SearchRequest, options?: { signal?: AbortSignal }): Promise<SearchResponse>;
}

/**
 * Registry for search strategies
 * This registry manages all available search strategies and provides methods to
 * get the appropriate strategy for a given dataset or request.
 */
export class SearchStrategyRegistry {
  private strategies: Map<string, SearchStrategy> = new Map();
  private defaultStrategyId: string | null = null;
  private datasetStrategyMap: Map<string, string> = new Map();
  
  /**
   * Registers a search strategy
   * @param strategy The search strategy to register
   * @param isDefault Whether this strategy should be the default
   */
  public register(strategy: SearchStrategy, isDefault: boolean = false): void {
    this.strategies.set(strategy.id, strategy);
    
    if (isDefault || this.defaultStrategyId === null) {
      this.defaultStrategyId = strategy.id;
    }
  }
  
  /**
   * Maps a dataset type to a specific search strategy
   * @param datasetType The dataset type
   * @param strategyId The ID of the search strategy to use for this dataset type
   */
  public mapDatasetTypeToStrategy(datasetType: string, strategyId: string): void {
    if (!this.strategies.has(strategyId)) {
      throw new Error(`Search strategy with ID ${strategyId} is not registered`);
    }
    
    this.datasetStrategyMap.set(datasetType, strategyId);
  }
  
  /**
   * Gets a search strategy by ID
   * @param id The ID of the search strategy
   * @returns The search strategy, or undefined if not found
   */
  public get(id: string): SearchStrategy | undefined {
    return this.strategies.get(id);
  }
  
  /**
   * Gets all registered search strategies
   * @returns An array of all registered search strategies
   */
  public getAll(): SearchStrategy[] {
    return Array.from(this.strategies.values());
  }
  
  /**
   * Gets the default search strategy
   * @returns The default search strategy, or undefined if none is registered
   */
  public getDefault(): SearchStrategy | undefined {
    if (this.defaultStrategyId === null) {
      return undefined;
    }
    
    return this.strategies.get(this.defaultStrategyId);
  }
  
  /**
   * Gets the appropriate search strategy for a dataset
   * @param dataset The dataset
   * @returns The search strategy for the dataset, or the default strategy if no specific mapping exists
   */
  public getForDataset(dataset: Dataset): SearchStrategy | undefined {
    if (!dataset || !dataset.type) {
      return this.getDefault();
    }
    
    const strategyId = this.datasetStrategyMap.get(dataset.type);
    if (!strategyId) {
      return this.getDefault();
    }
    
    return this.strategies.get(strategyId) || this.getDefault();
  }
  
  /**
   * Gets the appropriate search strategy for a search request
   * @param request The search request
   * @returns The search strategy for the request, or the default strategy if no specific mapping exists
   */
  public getForRequest(request: SearchRequest): SearchStrategy | undefined {
    if (!request || !request.query || !request.query.dataset) {
      return this.getDefault();
    }
    
    return this.getForDataset(request.query.dataset);
  }
}