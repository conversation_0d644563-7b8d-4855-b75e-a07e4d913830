# Router Module

This module contains the React Router configuration for the GuardBear application.

## Files

- `index.tsx`: Main router component that sets up the BrowserRouter and defines routes
- `constants.ts`: Defines route path constants to avoid string literals throughout the codebase
- `types.ts`: TypeScript interfaces for router-related types
- `utils.ts`: Utility functions for working with routes and navigation

## Usage

The router is designed to be imported and used in the main App component:

```tsx
import Router from './router';

function App() {
  return (
    <Router />
  );
}
```

## Route Structure

The router follows a hierarchical structure that mirrors the current navigation system:

- `/` - Dashboard (home)
- `/explore` - Explore section
  - `/explore/discover` - Discover page
  - `/explore/dashboards` - Dashboards page
  - `/explore/visualize` - Visualize page
  - `/explore/reporting` - Reporting page
  - `/explore/alerting` - Alerting page
  - `/explore/maps` - Maps page
  - `/explore/notifications` - Notifications page
- `/grid` - Grid View
- `/history` - History
- `/files` - Files
- `/profile` - Profile
- `/settings` - Settings