import React from 'react';

interface ErrorMessageProps {
  error: Error | string;
  title?: string;
  showDetails?: boolean;
  onRetry?: () => void;
  className?: string;
}

/**
 * A component for displaying user-friendly error messages
 */
const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  title = 'An error occurred',
  showDetails = true,
  onRetry,
  className = ''
}) => {
  // Get the error message
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  // Get the error stack trace if available
  const errorStack = typeof error === 'string' ? undefined : error.stack;
  
  // Map common error messages to user-friendly messages
  const getFriendlyMessage = (message: string): string => {
    if (message.includes('Network Error') || message.includes('Failed to fetch')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    
    if (message.includes('timeout') || message.includes('Timeout')) {
      return 'The request timed out. Please try again later.';
    }
    
    if (message.includes('401') || message.includes('Unauthorized')) {
      return 'You are not authorized to access this resource. Please log in and try again.';
    }
    
    if (message.includes('403') || message.includes('Forbidden')) {
      return 'You do not have permission to access this resource.';
    }
    
    if (message.includes('404') || message.includes('Not Found')) {
      return 'The requested resource could not be found.';
    }
    
    if (message.includes('500') || message.includes('Internal Server Error')) {
      return 'An internal server error occurred. Please try again later.';
    }
    
    return message;
  };
  
  const friendlyMessage = getFriendlyMessage(errorMessage);
  
  return (
    <div className={`error-message ${className}`}>
      <div className="error-icon">⚠️</div>
      <h3 className="error-title">{title}</h3>
      <p className="error-text">{friendlyMessage}</p>
      
      {showDetails && errorStack && (
        <details className="error-details">
          <summary>Technical details</summary>
          <pre className="error-stack">{errorStack}</pre>
        </details>
      )}
      
      {onRetry && (
        <button onClick={onRetry} className="error-retry-button">
          Try Again
        </button>
      )}
    </div>
  );
};

export default ErrorMessage;