import React, { useState } from 'react';
import { ConfigurationEntry, SeverityLevel } from '../../types/configuration';
import { useConfigurationEntries } from '../../hooks';
import { getSeverityFromScore } from '../../utils/configurationUtils';
import { formatConfigurationEntryForDetails } from '../../utils/configurationAdapters';

interface ConfigurationDetailsProps {
  entry?: ConfigurationEntry;
  onClose?: () => void;
}

/**
 * Component for displaying detailed information about a configuration assessment entry
 * This is a standalone component that can be used in a sidebar or modal
 */
const ConfigurationDetails: React.FC<ConfigurationDetailsProps> = ({ entry, onClose }) => {
  const { formatFieldValue } = useConfigurationEntries();
  const [activeTab, setActiveTab] = useState<'overview' | 'remediation' | 'compliance' | 'data'>('overview');
  
  if (!entry) {
    return (
      <div style={{
        padding: '24px',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'rgba(255, 255, 255, 0.7)',
        textAlign: 'center',
      }}>
        <div style={{ fontSize: '18px', marginBottom: '16px' }}>
          No configuration entry selected
        </div>
        <div style={{ fontSize: '14px', maxWidth: '400px' }}>
          Select a configuration entry from the table to view its details.
        </div>
      </div>
    );
  }
  
  // Format entry for display
  const formattedEntry = formatConfigurationEntryForDetails(entry);
  
  // Get severity level
  const severityLevel = getSeverityFromScore(entry.score);
  
  // Get severity color
  const getSeverityColor = (severity: SeverityLevel): string => {
    switch (severity) {
      case SeverityLevel.LOW:
        return '#4CAF50'; // Green
      case SeverityLevel.MEDIUM:
        return '#FF9800'; // Orange
      case SeverityLevel.HIGH:
        return '#F44336'; // Red
      case SeverityLevel.CRITICAL:
        return '#9C27B0'; // Purple
      default:
        return 'inherit';
    }
  };
  
  // Get result color
  const getResultColor = (result: string): string => {
    switch (result) {
      case 'passed':
        return '#4CAF50'; // Green
      case 'failed':
        return '#F44336'; // Red
      case 'not_applicable':
        return '#9E9E9E'; // Gray
      default:
        return 'inherit';
    }
  };
  
  return (
    <div style={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      color: 'white',
    }}>
      {/* Header */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        background: 'rgba(10, 14, 23, 0.8)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <h3 style={{ margin: 0, fontSize: '18px' }}>Configuration Details</h3>
        
        {onClose && (
          <button
            onClick={onClose}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'rgba(255, 255, 255, 0.7)',
              cursor: 'pointer',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M18 6L6 18M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
      
      {/* Summary */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
        background: 'rgba(0, 0, 0, 0.2)',
      }}>
        <h4 style={{ margin: '0 0 12px 0', color: '#00e5ff' }}>
          {entry.check.title}
        </h4>
        
        <div style={{ fontSize: '14px', marginBottom: '16px' }}>
          {entry.check.description}
        </div>
        
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          {/* Result badge */}
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            background: getResultColor(entry.result),
            color: 'white',
            fontWeight: 'bold',
            fontSize: '12px',
            textTransform: 'uppercase',
          }}>
            {entry.result}
          </div>
          
          {/* Severity badge */}
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            background: getSeverityColor(severityLevel),
            color: 'white',
            fontWeight: 'bold',
            fontSize: '12px',
            textTransform: 'uppercase',
          }}>
            {severityLevel} ({entry.score})
          </div>
          
          {/* Component badge */}
          <div style={{
            padding: '4px 8px',
            borderRadius: '4px',
            background: 'rgba(0, 229, 255, 0.1)',
            color: 'white',
            fontSize: '12px',
          }}>
            {entry.component}
          </div>
        </div>
      </div>
      
      {/* Tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        background: 'rgba(10, 14, 23, 0.8)',
      }}>
        <button
          role="tab"
          aria-selected={activeTab === 'overview'}
          aria-controls="overview-panel"
          id="overview-tab"
          onClick={() => setActiveTab('overview')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'overview' ? '2px solid #00e5ff' : '2px solid transparent',
            color: activeTab === 'overview' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
            padding: '12px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
            outline: 'none',
          }}
        >
          Overview
        </button>
        <button
          role="tab"
          aria-selected={activeTab === 'remediation'}
          aria-controls="remediation-panel"
          id="remediation-tab"
          onClick={() => setActiveTab('remediation')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'remediation' ? '2px solid #00e5ff' : '2px solid transparent',
            color: activeTab === 'remediation' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
            padding: '12px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
            outline: 'none',
          }}
        >
          Remediation
        </button>
        <button
          role="tab"
          aria-selected={activeTab === 'compliance'}
          aria-controls="compliance-panel"
          id="compliance-tab"
          onClick={() => setActiveTab('compliance')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'compliance' ? '2px solid #00e5ff' : '2px solid transparent',
            color: activeTab === 'compliance' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
            padding: '12px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
            outline: 'none',
          }}
        >
          Compliance
        </button>
        <button
          role="tab"
          aria-selected={activeTab === 'data'}
          aria-controls="data-panel"
          id="data-tab"
          onClick={() => setActiveTab('data')}
          style={{
            background: 'transparent',
            border: 'none',
            borderBottom: activeTab === 'data' ? '2px solid #00e5ff' : '2px solid transparent',
            color: activeTab === 'data' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
            padding: '12px 16px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold',
            outline: 'none',
          }}
        >
          Raw Data
        </button>
      </div>
      
      {/* Tab content */}
      <div style={{
        flex: 1,
        minHeight: 0,
        overflowY: 'auto',
        padding: '16px',
      }}>
        {/* Overview tab */}
        <div
          role="tabpanel"
          id="overview-panel"
          aria-labelledby="overview-tab"
          hidden={activeTab !== 'overview'}
          style={{
            display: activeTab === 'overview' ? 'block' : 'none',
          }}
        >
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
            gap: '8px',
          }}>
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Rule ID</div>
            <div>{entry.rule.id}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Rule Description</div>
            <div>{entry.rule.description}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Check ID</div>
            <div>{entry.check.id}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Check Title</div>
            <div>{entry.check.title}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Check Description</div>
            <div>{entry.check.description}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Result</div>
            <div style={{ color: getResultColor(entry.result) }}>{entry.result}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Score</div>
            <div>{entry.score}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Severity</div>
            <div style={{ color: getSeverityColor(severityLevel) }}>{severityLevel}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Component</div>
            <div>{entry.component}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Configuration</div>
            <div>{entry.configuration}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Agent</div>
            <div>{entry.agent.name} ({entry.agent.ip})</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Timestamp</div>
            <div>{entry.timestamp.toLocaleString()}</div>
            
            <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>Scan ID</div>
            <div>{entry.scan_id}</div>
          </div>
        </div>
        
        {/* Remediation tab */}
        <div
          role="tabpanel"
          id="remediation-panel"
          aria-labelledby="remediation-tab"
          hidden={activeTab !== 'remediation'}
          style={{
            display: activeTab === 'remediation' ? 'flex' : 'none',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          <div style={{
            padding: '16px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
          }}>
            <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Rationale</h5>
            <p style={{ margin: 0, lineHeight: '1.5' }}>{entry.check.rationale}</p>
          </div>
          
          <div style={{
            padding: '16px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '4px',
            border: entry.result === 'failed' ? '1px solid rgba(244, 67, 54, 0.3)' : 'none',
          }}>
            <h5 style={{ 
              margin: '0 0 8px 0', 
              color: entry.result === 'failed' ? '#F44336' : '#00e5ff',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}>
              {entry.result === 'failed' && (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              )}
              Remediation Steps
            </h5>
            <p style={{ margin: 0, lineHeight: '1.5' }}>{entry.check.remediation}</p>
          </div>
          
          {Object.keys(entry.data).length > 0 && (
            <div style={{
              padding: '16px',
              background: 'rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
            }}>
              <h5 style={{ margin: '0 0 8px 0', color: '#00e5ff' }}>Configuration Details</h5>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'minmax(150px, 1fr) 3fr',
                gap: '8px',
              }}>
                {Object.entries(entry.data).map(([key, value]) => (
                  <React.Fragment key={key}>
                    <div style={{ fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.7)' }}>{key}</div>
                    <div>{formatFieldValue(value, `data.${key}`)}</div>
                  </React.Fragment>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Compliance tab */}
        <div
          role="tabpanel"
          id="compliance-panel"
          aria-labelledby="compliance-tab"
          hidden={activeTab !== 'compliance'}
          style={{
            display: activeTab === 'compliance' ? 'flex' : 'none',
            flexDirection: 'column',
            gap: '16px',
          }}
        >
          {entry.check.compliance.length > 0 ? (
            <>
              <div style={{
                padding: '16px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              }}>
                <h5 style={{ margin: '0 0 16px 0', color: '#00e5ff' }}>Compliance Standards</h5>
                <div style={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '12px',
                }}>
                  {entry.check.compliance.map(standard => (
                    <div key={standard} style={{
                      padding: '8px 12px',
                      background: 'rgba(0, 229, 255, 0.1)',
                      borderRadius: '4px',
                      fontSize: '14px',
                      fontWeight: 'bold',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}>
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-9.618 5.04L12 21.012l9.618-13.028A11.955 11.955 0 0112 2.944z" />
                      </svg>
                      {standard}
                    </div>
                  ))}
                </div>
              </div>
              
              <div style={{
                padding: '16px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
              }}>
                <h5 style={{ margin: '0 0 16px 0', color: '#00e5ff' }}>Compliance Details</h5>
                <p style={{ margin: '0 0 16px 0', lineHeight: '1.5' }}>
                  This configuration check is part of the following compliance standards. 
                  Ensuring this check passes helps maintain compliance with these standards.
                </p>
                
                {entry.check.compliance.includes('PCI DSS') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>PCI DSS</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      The Payment Card Industry Data Security Standard (PCI DSS) is a set of security standards designed to ensure that all companies that accept, process, store or transmit credit card information maintain a secure environment.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('HIPAA') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>HIPAA</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      The Health Insurance Portability and Accountability Act (HIPAA) sets the standard for protecting sensitive patient data. Any company that deals with protected health information (PHI) must ensure that all the required physical, network, and process security measures are in place and followed.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('NIST') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>NIST</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      The National Institute of Standards and Technology (NIST) Cybersecurity Framework provides a policy framework of computer security guidance for how private sector organizations can assess and improve their ability to prevent, detect, and respond to cyber attacks.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('GDPR') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>GDPR</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      The General Data Protection Regulation (GDPR) is a regulation in EU law on data protection and privacy in the European Union and the European Economic Area. It addresses the transfer of personal data outside the EU and EEA areas.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('CIS') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>CIS</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      The Center for Internet Security (CIS) Benchmarks are configuration guidelines for various technology groups to safeguard systems against today's evolving cyber threats.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('SOC2') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>SOC2</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      SOC 2 is a voluntary compliance standard for service organizations, developed by the American Institute of CPAs (AICPA), which specifies how organizations should manage customer data.
                    </p>
                  </div>
                )}
                
                {entry.check.compliance.includes('ISO27001') && (
                  <div style={{ marginBottom: '16px' }}>
                    <h6 style={{ margin: '0 0 8px 0', color: 'white' }}>ISO27001</h6>
                    <p style={{ margin: 0, lineHeight: '1.5', fontSize: '14px' }}>
                      ISO/IEC 27001 is an international standard on how to manage information security. It details requirements for establishing, implementing, maintaining and continually improving an information security management system (ISMS).
                    </p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div style={{
              padding: '24px',
              textAlign: 'center',
              color: 'rgba(255, 255, 255, 0.7)',
            }}>
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                No compliance information available
              </div>
              <div style={{ fontSize: '14px' }}>
                This configuration check is not associated with any specific compliance standards.
              </div>
            </div>
          )}
        </div>
        
        {/* Raw Data tab */}
        <div
          role="tabpanel"
          id="data-panel"
          aria-labelledby="data-tab"
          hidden={activeTab !== 'data'}
          style={{
            display: activeTab === 'data' ? 'block' : 'none',
          }}
        >
          <pre style={{
            background: 'rgba(0, 0, 0, 0.3)',
            padding: '16px',
            borderRadius: '4px',
            overflowX: 'auto',
            fontSize: '12px',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
          }}>
            {JSON.stringify(formattedEntry, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default ConfigurationDetails;