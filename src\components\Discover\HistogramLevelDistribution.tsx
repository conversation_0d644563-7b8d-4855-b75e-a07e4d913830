import React, { useState } from 'react';
import { useDiscover } from '../../context/DiscoverContext';
import { useDiscoverSearch } from '../../hooks';
import { DiscoverUtils } from '../../utils/discoverUtils';

interface LevelDistributionProps {
  compact?: boolean;
}

/**
 * Component to display log level distribution in the histogram
 */
const HistogramLevelDistribution: React.FC<LevelDistributionProps> = ({ compact = false }) => {
  const { state } = useDiscover();
  const { filteredData } = state;
  const { addFilter } = useDiscoverSearch();
  const [hoveredLevel, setHoveredLevel] = useState<string | null>(null);
  
  // Calculate level distribution
  const levelDistribution = DiscoverUtils.calculateLevelDistribution(filteredData);
  const totalLogs = Object.values(levelDistribution).reduce((sum, count) => sum + count, 0);
  
  // Format count with thousands separator
  const formatCount = (count: number): string => {
    return new Intl.NumberFormat().format(count);
  };
  
  // Get percentage of logs for a specific level
  const getPercentage = (count: number): string => {
    if (totalLogs === 0) return '0%';
    return `${Math.round((count / totalLogs) * 100)}%`;
  };
  
  // Get color for log level
  const getLevelColor = (level: string): string => {
    switch (level) {
      case 'info': return 'rgba(0, 191, 255, 0.8)'; // Light blue
      case 'warning': return 'rgba(255, 165, 0, 0.8)'; // Orange
      case 'error': return 'rgba(255, 69, 0, 0.8)'; // Red-orange
      case 'critical': return 'rgba(255, 0, 0, 0.8)'; // Red
      default: return 'rgba(0, 191, 255, 0.8)';
    }
  };
  
  // Get level display name
  const getLevelName = (level: string): string => {
    switch (level) {
      case 'info': return 'Info';
      case 'warning': return 'Warning';
      case 'error': return 'Error';
      case 'critical': return 'Critical';
      default: return level;
    }
  };
  
  // Levels to display
  const levels = ['info', 'warning', 'error', 'critical'];
  
  if (compact) {
    // Compact view (horizontal bars)
    return (
      <div style={{ marginTop: '8px' }}>
        <div style={{
          display: 'flex',
          height: '4px',
          width: '100%',
          borderRadius: '2px',
          overflow: 'hidden',
        }}>
          {levels.map(level => {
            const count = levelDistribution[level] || 0;
            const percentage = totalLogs > 0 ? (count / totalLogs) * 100 : 0;
            
            return (
              <div
                key={level}
                style={{
                  width: `${percentage}%`,
                  height: '100%',
                  background: getLevelColor(level),
                  cursor: 'pointer',
                  transition: 'opacity 0.2s ease',
                }}
                onClick={() => addFilter('level', level, 'is')}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '0.8';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '1';
                }}
                title={`${getLevelName(level)}: ${formatCount(count)} (${getPercentage(count)}) - Click to filter`}
              />
            );
          })}
        </div>
        
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          marginTop: '4px',
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
        }}>
          <div>{formatCount(totalLogs)} logs</div>
          <div style={{ display: 'flex', gap: '8px' }}>
            {levels.map(level => {
              const isHovered = hoveredLevel === level;
              return (
                <div 
                  key={level} 
                  style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '4px',
                    cursor: 'pointer',
                    padding: '2px 4px',
                    borderRadius: '2px',
                    background: isHovered ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                    transition: 'background-color 0.2s ease'
                  }}
                  onClick={() => addFilter('level', level, 'is')}
                  onMouseEnter={() => setHoveredLevel(level)}
                  onMouseLeave={() => setHoveredLevel(null)}
                  title={`Click to filter for ${getLevelName(level)} logs`}
                >
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: getLevelColor(level),
                    boxShadow: isHovered ? '0 0 3px rgba(0, 229, 255, 0.5)' : 'none'
                  }} />
                  <span style={{ 
                    color: isHovered ? '#00e5ff' : 'rgba(255, 255, 255, 0.6)',
                    transition: 'color 0.2s ease'
                  }}>
                    {getLevelName(level)}: {getPercentage(levelDistribution[level] || 0)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  }
  
  // Full view (with counts)
  return (
    <div style={{
      marginTop: '16px',
      padding: '12px',
      background: 'rgba(0, 0, 0, 0.2)',
      borderRadius: '4px',
    }}>
      <h4 style={{ 
        margin: '0 0 12px 0', 
        color: 'white',
        fontSize: '14px',
      }}>
        Log Level Distribution
      </h4>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {levels.map(level => {
          const count = levelDistribution[level] || 0;
          const percentage = totalLogs > 0 ? (count / totalLogs) * 100 : 0;
          const isHovered = hoveredLevel === level;
          
          return (
            <div 
              key={level} 
              style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                gap: '4px',
                cursor: 'pointer',
                padding: '4px',
                borderRadius: '4px',
                background: isHovered ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                transition: 'background-color 0.2s ease'
              }}
              onClick={() => addFilter('level', level, 'is')}
              onMouseEnter={() => setHoveredLevel(level)}
              onMouseLeave={() => setHoveredLevel(null)}
              title={`Click to filter for ${getLevelName(level)} logs`}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                  <div style={{
                    width: '10px',
                    height: '10px',
                    borderRadius: '50%',
                    background: getLevelColor(level),
                    boxShadow: isHovered ? '0 0 5px rgba(0, 229, 255, 0.5)' : 'none'
                  }} />
                  <span style={{ 
                    color: isHovered ? '#00e5ff' : 'white', 
                    fontSize: '12px',
                    transition: 'color 0.2s ease'
                  }}>
                    {getLevelName(level)}
                  </span>
                </div>
                <div style={{ 
                  color: isHovered ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)', 
                  fontSize: '12px',
                  transition: 'color 0.2s ease'
                }}>
                  {formatCount(count)} ({getPercentage(count)})
                </div>
              </div>
              
              <div style={{ height: '6px', width: '100%', background: 'rgba(0, 0, 0, 0.3)', borderRadius: '3px', overflow: 'hidden' }}>
                <div
                  style={{
                    height: '100%',
                    width: `${percentage}%`,
                    background: isHovered ? getLevelColor(level).replace('0.8', '1') : getLevelColor(level),
                    borderRadius: '3px',
                    boxShadow: isHovered ? '0 0 8px rgba(0, 229, 255, 0.5)' : 'none',
                    transition: 'all 0.2s ease'
                  }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default HistogramLevelDistribution;