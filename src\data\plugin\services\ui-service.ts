import { UiService } from '../interfaces';
import React from 'react';

/**
 * Implementation of the UiService interface
 */
export class UiServiceImpl implements UiService {
  private components: Map<string, React.ComponentType<unknown>> = new Map();

  /**
   * Gets a UI component by ID
   * @param id The component ID
   * @returns The component or undefined if not found
   */
  public getComponent(id: string): React.ComponentType<unknown> | undefined {
    return this.components.get(id);
  }

  /**
   * Registers a new UI component
   * @param id The component ID
   * @param component The React component
   */
  public registerComponent(id: string, component: React.ComponentType<unknown>): void {
    this.components.set(id, component);
  }
}