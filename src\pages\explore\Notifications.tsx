import React, { useState } from 'react';

/**
 * Notifications page component.
 * 
 * This component renders the Notifications page content from the Explore section.
 */
const Notifications: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  
  // Sample notification data
  const notifications = [
    { 
      id: 'N1001', 
      type: 'alert', 
      title: 'Critical Security Alert', 
      message: 'Ransomware activity detected on endpoint DEV-WS-42', 
      time: '10 minutes ago',
      read: false
    },
    { 
      id: 'N1002', 
      type: 'system', 
      title: 'System Update Available', 
      message: 'Security patch KB4023057 is available for installation', 
      time: '1 hour ago',
      read: false
    },
    { 
      id: 'N1003', 
      type: 'info', 
      title: 'Scan Completed', 
      message: 'Weekly vulnerability scan completed with 3 findings', 
      time: '3 hours ago',
      read: true
    },
    { 
      id: 'N1004', 
      type: 'alert', 
      title: 'Firewall Rule Change', 
      message: 'Firewall rule #42 was modified by admin', 
      time: '5 hours ago',
      read: true
    },
    { 
      id: 'N1005', 
      type: 'system', 
      title: 'Backup Completed', 
      message: 'System backup completed successfully', 
      time: '1 day ago',
      read: true
    },
  ];
  
  // Filter notifications based on active tab
  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    if (activeTab === 'unread') return !notification.read;
    return notification.type === activeTab;
  });
  
  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'alert':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#ff4d4d" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        );
      case 'system':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#00e5ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
        );
      case 'info':
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9966ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        );
      default:
        return null;
    }
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '16px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Notifications
      </h1>
      
      {/* Notification filters */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        marginBottom: '24px',
      }}>
        {['all', 'unread', 'alert', 'system', 'info'].map((tab) => (
          <div 
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: '12px 24px',
              cursor: 'pointer',
              color: activeTab === tab ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
              borderBottom: activeTab === tab ? '2px solid #00e5ff' : 'none',
              fontWeight: activeTab === tab ? 500 : 400,
              fontSize: '14px',
              textTransform: 'capitalize',
            }}
          >
            {tab}
          </div>
        ))}
      </div>
      
      {/* Notification list */}
      <div style={{
        borderRadius: '12px',
        background: 'rgba(16, 24, 45, 0.7)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        border: '1px solid rgba(0, 229, 255, 0.2)',
        boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
        overflow: 'hidden',
      }}>
        {filteredNotifications.length > 0 ? (
          filteredNotifications.map((notification, index) => (
            <div 
              key={notification.id}
              style={{
                padding: '16px',
                borderBottom: index < filteredNotifications.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '16px',
                cursor: 'pointer',
                transition: 'background 0.2s ease',
                background: notification.read ? 'transparent' : 'rgba(0, 229, 255, 0.05)',
              }}
            >
              <div style={{ marginTop: '4px' }}>
                {getNotificationIcon(notification.type)}
              </div>
              
              <div style={{ flex: 1 }}>
                <div style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                  <div style={{ 
                    fontSize: '16px', 
                    color: 'white', 
                    fontWeight: notification.read ? 400 : 500,
                  }}>
                    {notification.title}
                    {!notification.read && (
                      <span style={{
                        display: 'inline-block',
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: '#00e5ff',
                        marginLeft: '8px',
                      }} />
                    )}
                  </div>
                  <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.5)' }}>
                    {notification.time}
                  </div>
                </div>
                <div style={{ 
                  fontSize: '14px', 
                  color: 'rgba(255, 255, 255, 0.7)',
                  marginTop: '4px',
                }}>
                  {notification.message}
                </div>
              </div>
              
              <div>
                <button style={{
                  background: 'transparent',
                  border: 'none',
                  color: 'rgba(255, 255, 255, 0.5)',
                  cursor: 'pointer',
                  fontSize: '18px',
                }}>
                  &times;
                </button>
              </div>
            </div>
          ))
        ) : (
          <div style={{
            padding: '32px',
            textAlign: 'center',
            color: 'rgba(255, 255, 255, 0.5)',
            fontSize: '14px',
          }}>
            No notifications found
          </div>
        )}
      </div>
      
      {/* Actions */}
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        marginTop: '16px',
        gap: '12px',
      }}>
        <button style={{
          background: 'transparent',
          border: '1px solid rgba(0, 229, 255, 0.3)',
          borderRadius: '4px',
          padding: '8px 16px',
          color: '#00e5ff',
          fontSize: '14px',
          cursor: 'pointer',
        }}>
          Mark all as read
        </button>
        <button style={{
          background: 'transparent',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '4px',
          padding: '8px 16px',
          color: 'rgba(255, 255, 255, 0.7)',
          fontSize: '14px',
          cursor: 'pointer',
        }}>
          Clear all
        </button>
      </div>
    </div>
  );
};

export default Notifications;