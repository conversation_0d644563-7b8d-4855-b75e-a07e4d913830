import React, { useState } from 'react';
import DiscoverSidebar from './DiscoverSidebar';
import DiscoverMainContent from './DiscoverMainContent';
import KeyboardNavigation from './KeyboardNavigation';
import FocusManager from './FocusManager';

/**
 * Main layout component for the Discover page
 * Provides the overall structure with sidebar and main content
 */
const DiscoverLayout: React.FC = () => {
  // State for sidebar collapse
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Toggle sidebar collapse
  const toggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };
  
  return (
    <div style={{
      display: 'flex',
      height: '100%',
      width: '100%',
      overflow: 'hidden',
    }}>
      {/* Sidebar */}
      <DiscoverSidebar 
        isCollapsed={isSidebarCollapsed} 
        onToggleCollapse={toggleSidebar} 
      />
      
      {/* Main content */}
      <DiscoverMainContent 
        isSidebarCollapsed={isSidebarCollapsed}
      />
      
      {/* Keyboard navigation handler */}
      <KeyboardNavigation />
      
      {/* Focus management and keyboard shortcuts */}
      <FocusManager />
    </div>
  );
};

export default DiscoverLayout;