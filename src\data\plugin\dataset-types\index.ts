import { DataPlugin } from '../plugin';
import { sampleDatasetType } from './sample-dataset-type';
import { customSampleDatasetType } from './custom-sample-dataset-type';

/**
 * Registers all dataset types with the DatasetService
 * @param dataPlugin The data plugin instance
 */
export function registerDatasetTypes(dataPlugin: DataPlugin): void {
  const datasetService = dataPlugin.getQueryService().getDatasetService();
  
  // Register the sample dataset type
  datasetService.registerType(sampleDatasetType);
  
  // Register the custom sample dataset type
  datasetService.registerType(customSampleDatasetType);
}