import React from 'react';
import { Outlet } from 'react-router-dom';
import NavigationSidebar from '../components/NavigationSidebar';
import { NavigationProvider } from '../context/NavigationContext';
import { DataPluginProvider } from '../context/DataPluginContext';

/**
 * Main application layout component.
 * 
 * This component provides the main layout structure for the application,
 * including the navigation sidebar and content area.
 */
const AppLayout: React.FC = () => {
  return (
    <DataPluginProvider>
      <NavigationProvider>
        <div style={{
          width: '100vw',
          height: '100vh',
          display: 'flex',
          background: 'linear-gradient(135deg, #0a0e17 0%, #121a2c 100%)',
          color: 'white',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
          position: 'relative',
        }}>
          {/* Navigation Sidebar */}
          <NavigationSidebar />
          
          {/* Main Content Area */}
          <main style={{
            flex: 1,
            overflow: 'auto',
            position: 'relative',
          }}>
            <Outlet />
          </main>
        </div>
      </NavigationProvider>
    </DataPluginProvider>
  );
};

export default AppLayout;