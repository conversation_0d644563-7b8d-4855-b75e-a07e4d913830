import { ExploreItem } from './types';
import {
  SearchIcon,
  DashboardIcon,
  ChartIcon,
  ReportIcon,
  AlertIcon,
  MapIcon,
  NotificationIcon
} from './icons';

export const EXPLORE_ITEMS: ExploreItem[] = [
  {
    id: 'discover',
    label: 'Discover',
    icon: SearchIcon,
    route: '/explore/discover',
    description: 'Search and explore your data'
  },
  {
    id: 'dashboards',
    label: 'Dashboards',
    icon: DashboardIcon,
    route: '/explore/dashboards',
    description: 'View and manage dashboards'
  },
  {
    id: 'visualize',
    label: 'Visualize',
    icon: ChartIcon,
    route: '/explore/visualize',
    description: 'Create data visualizations'
  },
  {
    id: 'reporting',
    label: 'Reporting',
    icon: ReportIcon,
    route: '/explore/reporting',
    description: 'Generate and manage reports'
  },
  {
    id: 'alerting',
    label: 'Alerting',
    icon: AlertIcon,
    route: '/explore/alerting',
    description: 'Configure alerts and notifications'
  },
  {
    id: 'maps',
    label: 'Maps',
    icon: MapIcon,
    route: '/explore/maps',
    description: 'Geographic data visualization'
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: NotificationIcon,
    route: '/explore/notifications',
    description: 'Manage system notifications'
  }
];