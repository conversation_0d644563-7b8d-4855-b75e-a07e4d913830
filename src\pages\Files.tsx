import React, { useState } from 'react';

/**
 * Files page component.
 * 
 * This component displays a file management interface for security-related files.
 */
const Files: React.FC = () => {
  const [activeFolder, setActiveFolder] = useState('reports');
  const [viewMode, setViewMode] = useState('grid');
  const [sortBy, setSortBy] = useState('name');
  
  // Sample folders
  const folders = [
    { id: 'reports', name: 'Reports', count: 24 },
    { id: 'logs', name: 'Log Files', count: 156 },
    { id: 'backups', name: 'Backups', count: 12 },
    { id: 'configs', name: 'Configuration', count: 8 },
    { id: 'exports', name: 'Exports', count: 35 },
    { id: 'shared', name: 'Shared Files', count: 17 }
  ];
  
  // Sample files
  const files = {
    reports: [
      { id: 'f1', name: 'Security_Posture_Summary_Jul2025.pdf', type: 'pdf', size: '2.4 MB', modified: '2025-07-17T10:23:45', shared: false },
      { id: 'f2', name: 'Threat_Intelligence_Weekly.xlsx', type: 'xlsx', size: '1.8 MB', modified: '2025-07-16T14:30:22', shared: true },
      { id: 'f3', name: 'Compliance_Status_Q2_2025.pdf', type: 'pdf', size: '4.2 MB', modified: '2025-07-15T09:12:33', shared: false },
      { id: 'f4', name: 'Vulnerability_Assessment_Jul2025.pdf', type: 'pdf', size: '3.7 MB', modified: '2025-07-14T16:45:10', shared: true },
      { id: 'f5', name: 'User_Activity_Analysis.xlsx', type: 'xlsx', size: '2.1 MB', modified: '2025-07-13T11:20:05', shared: false },
      { id: 'f6', name: 'Network_Traffic_Analysis.pdf', type: 'pdf', size: '5.3 MB', modified: '2025-07-12T08:15:30', shared: false },
      { id: 'f7', name: 'Incident_Response_Summary.docx', type: 'docx', size: '1.5 MB', modified: '2025-07-11T13:40:18', shared: true },
      { id: 'f8', name: 'Executive_Security_Briefing.pptx', type: 'pptx', size: '6.2 MB', modified: '2025-07-10T15:55:42', shared: false }
    ],
    logs: [
      { id: 'f9', name: 'firewall_logs_20250717.log', type: 'log', size: '8.7 MB', modified: '2025-07-17T23:59:59', shared: false },
      { id: 'f10', name: 'ids_alerts_20250717.log', type: 'log', size: '4.3 MB', modified: '2025-07-17T23:59:59', shared: false },
      { id: 'f11', name: 'auth_logs_20250717.log', type: 'log', size: '2.8 MB', modified: '2025-07-17T23:59:59', shared: false },
      { id: 'f12', name: 'system_logs_20250717.log', type: 'log', size: '5.1 MB', modified: '2025-07-17T23:59:59', shared: false }
    ],
    backups: [
      { id: 'f13', name: 'full_backup_20250715.bak', type: 'bak', size: '1.2 GB', modified: '2025-07-15T02:00:00', shared: false },
      { id: 'f14', name: 'incremental_backup_20250716.bak', type: 'bak', size: '450 MB', modified: '2025-07-16T02:00:00', shared: false },
      { id: 'f15', name: 'incremental_backup_20250717.bak', type: 'bak', size: '320 MB', modified: '2025-07-17T02:00:00', shared: false }
    ],
    configs: [
      { id: 'f16', name: 'firewall_rules.conf', type: 'conf', size: '24 KB', modified: '2025-07-10T14:22:15', shared: false },
      { id: 'f17', name: 'ids_config.xml', type: 'xml', size: '18 KB', modified: '2025-07-12T09:33:47', shared: false },
      { id: 'f18', name: 'vpn_settings.conf', type: 'conf', size: '12 KB', modified: '2025-07-14T11:15:30', shared: false }
    ],
    exports: [
      { id: 'f19', name: 'security_metrics_export.csv', type: 'csv', size: '1.7 MB', modified: '2025-07-17T15:30:22', shared: true },
      { id: 'f20', name: 'user_activity_export.csv', type: 'csv', size: '2.3 MB', modified: '2025-07-16T10:45:18', shared: false },
      { id: 'f21', name: 'alerts_export_jul2025.csv', type: 'csv', size: '3.1 MB', modified: '2025-07-15T14:20:05', shared: true }
    ],
    shared: [
      { id: 'f22', name: 'Security_Policy_2025.pdf', type: 'pdf', size: '1.8 MB', modified: '2025-07-01T09:00:00', shared: true },
      { id: 'f23', name: 'Incident_Response_Plan.docx', type: 'docx', size: '2.2 MB', modified: '2025-07-05T11:30:15', shared: true },
      { id: 'f24', name: 'Security_Training_Materials.zip', type: 'zip', size: '45.6 MB', modified: '2025-07-08T13:45:30', shared: true }
    ]
  };
  
  // Get current files based on active folder
  const currentFiles = files[activeFolder as keyof typeof files] || [];
  
  // Sort files
  const sortedFiles = [...currentFiles].sort((a, b) => {
    if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    } else if (sortBy === 'modified') {
      return new Date(b.modified).getTime() - new Date(a.modified).getTime();
    } else if (sortBy === 'size') {
      // Simple size comparison (not accurate for different units)
      return a.size.localeCompare(b.size);
    } else if (sortBy === 'type') {
      return a.type.localeCompare(b.type);
    }
    return 0;
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };
  
  // Get file icon based on type
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#ff6b6b" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="9" y1="15" x2="15" y2="15"></line>
          </svg>
        );
      case 'xlsx':
      case 'csv':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#4CAF50" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="8" y1="13" x2="16" y2="13"></line>
            <line x1="8" y1="17" x2="16" y2="17"></line>
          </svg>
        );
      case 'docx':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#2196F3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="8" y1="13" x2="16" y2="13"></line>
            <line x1="8" y1="17" x2="16" y2="17"></line>
          </svg>
        );
      case 'pptx':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FF9800" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
          </svg>
        );
      case 'log':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9C27B0" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
            <line x1="8" y1="13" x2="16" y2="13"></line>
            <line x1="8" y1="17" x2="16" y2="17"></line>
          </svg>
        );
      case 'bak':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#795548" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="2" y="4" width="20" height="16" rx="2" ry="2"></rect>
            <circle cx="8" cy="12" r="2"></circle>
            <circle cx="16" cy="12" r="2"></circle>
          </svg>
        );
      case 'conf':
      case 'xml':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#607D8B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="16 18 22 12 16 6"></polyline>
            <polyline points="8 6 2 12 8 18"></polyline>
          </svg>
        );
      case 'zip':
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#FFC107" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M21 8v13H3V8"></path>
            <path d="M1 3h22v5H1z"></path>
            <path d="M10 12h4"></path>
          </svg>
        );
      default:
        return (
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#00e5ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14 2 14 8 20 8"></polyline>
          </svg>
        );
    }
  };
  
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'hidden',
      position: 'relative',
      padding: '24px',
      display: 'flex',
      flexDirection: 'column',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
      }}>
        <h1 style={{ 
          fontSize: '28px', 
          fontWeight: 600,
          background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
        }}>
          Files
        </h1>
        
        <div style={{
          display: 'flex',
          gap: '12px',
        }}>
          <button style={{
            background: 'rgba(0, 229, 255, 0.1)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '4px',
            padding: '8px 12px',
            color: '#00e5ff',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Upload
          </button>
          <button style={{
            background: 'rgba(0, 229, 255, 0.1)',
            border: '1px solid rgba(0, 229, 255, 0.3)',
            borderRadius: '4px',
            padding: '8px 12px',
            color: '#00e5ff',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="12" y1="8" x2="12" y2="16"></line>
              <line x1="8" y1="12" x2="16" y2="12"></line>
            </svg>
            New Folder
          </button>
        </div>
      </div>
      
      <div style={{
        display: 'flex',
        gap: '24px',
        flex: 1,
        overflow: 'hidden',
      }}>
        {/* Folders sidebar */}
        <div style={{
          width: '220px',
          borderRadius: '12px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        }}>
          {folders.map(folder => (
            <div 
              key={folder.id}
              onClick={() => setActiveFolder(folder.id)}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '10px 12px',
                borderRadius: '8px',
                cursor: 'pointer',
                background: activeFolder === folder.id ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: activeFolder === folder.id ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
                transition: 'all 0.2s ease',
              }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
              }}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke={activeFolder === folder.id ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)'} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                </svg>
                <span style={{ 
                  fontSize: '14px', 
                  color: activeFolder === folder.id ? '#00e5ff' : 'white',
                }}>
                  {folder.name}
                </span>
              </div>
              <span style={{ 
                fontSize: '12px', 
                color: 'rgba(255, 255, 255, 0.5)',
                background: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '10px',
                padding: '2px 6px',
              }}>
                {folder.count}
              </span>
            </div>
          ))}
        </div>
        
        {/* Files content */}
        <div style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}>
          {/* Toolbar */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '16px',
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}>
              <input 
                type="text" 
                placeholder="Search files..." 
                style={{
                  background: 'rgba(16, 24, 45, 0.7)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '1px solid rgba(0, 229, 255, 0.2)',
                  borderRadius: '4px',
                  padding: '8px 12px',
                  color: 'white',
                  fontSize: '14px',
                  width: '250px',
                }}
              />
              <select 
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{
                  background: 'rgba(16, 24, 45, 0.7)',
                  backdropFilter: 'blur(10px)',
                  WebkitBackdropFilter: 'blur(10px)',
                  border: '1px solid rgba(0, 229, 255, 0.2)',
                  borderRadius: '4px',
                  padding: '8px 12px',
                  color: 'white',
                  fontSize: '14px',
                  cursor: 'pointer',
                }}
              >
                <option value="name">Sort by Name</option>
                <option value="modified">Sort by Date</option>
                <option value="size">Sort by Size</option>
                <option value="type">Sort by Type</option>
              </select>
            </div>
            
            <div style={{
              display: 'flex',
              gap: '8px',
            }}>
              <button 
                onClick={() => setViewMode('grid')}
                style={{
                  background: viewMode === 'grid' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                  border: viewMode === 'grid' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '4px',
                  padding: '8px',
                  color: viewMode === 'grid' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect x="3" y="3" width="7" height="7"></rect>
                  <rect x="14" y="3" width="7" height="7"></rect>
                  <rect x="14" y="14" width="7" height="7"></rect>
                  <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
              </button>
              <button 
                onClick={() => setViewMode('list')}
                style={{
                  background: viewMode === 'list' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                  border: viewMode === 'list' ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '4px',
                  padding: '8px',
                  color: viewMode === 'list' ? '#00e5ff' : 'rgba(255, 255, 255, 0.7)',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="8" y1="6" x2="21" y2="6"></line>
                  <line x1="8" y1="12" x2="21" y2="12"></line>
                  <line x1="8" y1="18" x2="21" y2="18"></line>
                  <line x1="3" y1="6" x2="3.01" y2="6"></line>
                  <line x1="3" y1="12" x2="3.01" y2="12"></line>
                  <line x1="3" y1="18" x2="3.01" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>
          
          {/* Files list */}
          <div style={{
            flex: 1,
            borderRadius: '12px',
            background: 'rgba(16, 24, 45, 0.7)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
            border: '1px solid rgba(0, 229, 255, 0.2)',
            boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
            padding: '16px',
            overflow: 'auto',
          }}>
            {viewMode === 'grid' ? (
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fill, minmax(180px, 1fr))',
                gap: '16px',
              }}>
                {sortedFiles.map(file => (
                  <div 
                    key={file.id}
                    style={{
                      borderRadius: '8px',
                      padding: '16px',
                      background: 'rgba(255, 255, 255, 0.05)',
                      border: '1px solid rgba(255, 255, 255, 0.1)',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      position: 'relative',
                    }}
                  >
                    {file.shared && (
                      <div style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        color: 'rgba(255, 255, 255, 0.7)',
                      }}>
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                          <polyline points="16 6 12 2 8 6"></polyline>
                          <line x1="12" y1="2" x2="12" y2="15"></line>
                        </svg>
                      </div>
                    )}
                    
                    {getFileIcon(file.type)}
                    
                    <div style={{
                      textAlign: 'center',
                    }}>
                      <div style={{ 
                        fontSize: '14px', 
                        color: 'white',
                        marginBottom: '4px',
                        wordBreak: 'break-word',
                      }}>
                        {file.name.length > 20 ? `${file.name.substring(0, 17)}...` : file.name}
                      </div>
                      <div style={{ 
                        fontSize: '12px', 
                        color: 'rgba(255, 255, 255, 0.5)',
                      }}>
                        {file.size}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
              }}>
                <thead>
                  <tr style={{
                    borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                  }}>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'left',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      Name
                    </th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'left',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      Type
                    </th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'left',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      Size
                    </th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'left',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(255, 255, 255, 0.7)',
                    }}>
                      Modified
                    </th>
                    <th style={{
                      padding: '12px 16px',
                      textAlign: 'center',
                      fontSize: '14px',
                      fontWeight: 500,
                      color: 'rgba(255, 255, 255, 0.7)',
                      width: '80px',
                    }}>
                      Shared
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {sortedFiles.map((file, index) => (
                    <tr 
                      key={file.id}
                      style={{
                        borderBottom: index < sortedFiles.length - 1 ? '1px solid rgba(255, 255, 255, 0.05)' : 'none',
                        cursor: 'pointer',
                        transition: 'background 0.2s ease',
                        ':hover': {
                          background: 'rgba(0, 229, 255, 0.05)',
                        },
                      }}
                    >
                      <td style={{
                        padding: '12px 16px',
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px',
                        }}>
                          {getFileIcon(file.type)}
                          <span style={{ fontSize: '14px', color: 'white' }}>
                            {file.name}
                          </span>
                        </div>
                      </td>
                      <td style={{
                        padding: '12px 16px',
                        fontSize: '14px',
                        color: 'rgba(255, 255, 255, 0.7)',
                        textTransform: 'uppercase',
                      }}>
                        {file.type}
                      </td>
                      <td style={{
                        padding: '12px 16px',
                        fontSize: '14px',
                        color: 'rgba(255, 255, 255, 0.7)',
                      }}>
                        {file.size}
                      </td>
                      <td style={{
                        padding: '12px 16px',
                        fontSize: '14px',
                        color: 'rgba(255, 255, 255, 0.7)',
                      }}>
                        {formatDate(file.modified)}
                      </td>
                      <td style={{
                        padding: '12px 16px',
                        textAlign: 'center',
                      }}>
                        {file.shared && (
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#00e5ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                            <polyline points="16 6 12 2 8 6"></polyline>
                            <line x1="12" y1="2" x2="12" y2="15"></line>
                          </svg>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Files;