import React from 'react';

interface ConfigurationEmptyStateProps {
  message?: string;
  subMessage?: string;
}

/**
 * Empty state component for Configuration Assessment
 * Displays a message when no data is available
 * 
 * Requirements: 6.4
 */
const ConfigurationEmptyState: React.FC<ConfigurationEmptyStateProps> = ({
  message = 'No configurations found',
  subMessage = 'Try adjusting your search query or filters, or changing the time range to see more results.',
}) => {
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'rgba(255, 255, 255, 0.7)',
      padding: '24px',
      textAlign: 'center',
    }}>
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="rgba(0, 229, 255, 0.5)" strokeWidth="1.5" style={{ marginBottom: '16px' }}>
        <circle cx="11" cy="11" r="8" />
        <path d="M21 21l-4.35-4.35" />
      </svg>
      <h3 style={{ margin: '0 0 8px 0', color: 'white' }}>{message}</h3>
      <p style={{ margin: 0, maxWidth: '400px' }}>
        {subMessage}
      </p>
    </div>
  );
};

export default ConfigurationEmptyState;