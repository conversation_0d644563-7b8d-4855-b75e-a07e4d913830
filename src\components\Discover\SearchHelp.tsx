import React from 'react';

interface SearchHelpProps {
  isVisible: boolean;
  onClose: () => void;
}

/**
 * Component that displays help information about search syntax
 */
const SearchHelp: React.FC<SearchHelpProps> = ({ isVisible, onClose }) => {
  if (!isVisible) return null;
  
  return (
    <div style={{
      position: 'absolute',
      top: '100%',
      left: 0,
      right: 0,
      marginTop: '8px',
      background: 'rgba(10, 14, 23, 0.95)',
      border: '1px solid rgba(0, 229, 255, 0.3)',
      borderRadius: '4px',
      padding: '16px',
      zIndex: 10,
      color: 'white',
      fontSize: '14px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
      maxWidth: '600px',
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px',
      }}>
        <h3 style={{ margin: 0, fontSize: '16px' }}>Search Syntax Help</h3>
        <button
          onClick={onClose}
          style={{
            background: 'transparent',
            border: 'none',
            color: 'rgba(255, 255, 255, 0.7)',
            cursor: 'pointer',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div style={{ marginBottom: '16px' }}>
        <p>You can use the following syntax in your search queries:</p>
        
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          marginTop: '8px',
        }}>
          <thead>
            <tr>
              <th style={{ 
                textAlign: 'left', 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Syntax
              </th>
              <th style={{ 
                textAlign: 'left', 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Description
              </th>
              <th style={{ 
                textAlign: 'left', 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
                color: 'rgba(255, 255, 255, 0.7)',
              }}>
                Example
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                field:value
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
              }}>
                Search for logs where the specified field contains the value
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                level:error
              </td>
            </tr>
            <tr>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                -field:value
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
              }}>
                Exclude logs where the field contains the value
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                -source:web-server-01
              </td>
            </tr>
            <tr>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                "exact phrase"
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
              }}>
                Search for logs containing the exact phrase
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                "password failed"
              </td>
            </tr>
            <tr>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                _exists_:field
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
              }}>
                Find logs where the field exists
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                _exists_:http.status
              </td>
            </tr>
            <tr>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                field:*value*
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
              }}>
                Search for logs where the field contains the value
              </td>
              <td style={{ 
                padding: '8px', 
                borderBottom: '1px solid rgba(0, 229, 255, 0.1)',
                fontFamily: 'monospace',
              }}>
                message:*error*
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <div>
        <p style={{ marginBottom: '8px' }}>Common fields you can search:</p>
        <ul style={{
          listStyle: 'none',
          padding: 0,
          margin: 0,
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fill, minmax(150px, 1fr))',
          gap: '8px',
        }}>
          <li style={{ fontFamily: 'monospace' }}>level</li>
          <li style={{ fontFamily: 'monospace' }}>source</li>
          <li style={{ fontFamily: 'monospace' }}>message</li>
          <li style={{ fontFamily: 'monospace' }}>agent.name</li>
          <li style={{ fontFamily: 'monospace' }}>agent.ip</li>
          <li style={{ fontFamily: 'monospace' }}>rule.id</li>
          <li style={{ fontFamily: 'monospace' }}>rule.description</li>
          <li style={{ fontFamily: 'monospace' }}>location</li>
        </ul>
      </div>
    </div>
  );
};

export default SearchHelp;