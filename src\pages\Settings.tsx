import React, { useState } from 'react';

/**
 * Settings page component.
 * 
 * This component displays application settings and configuration options.
 */
const Settings: React.FC = () => {
  const [activeSection, setActiveSection] = useState('general');
  
  // Sample settings data
  const settings = {
    general: {
      theme: 'dark',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '24h'
    },
    security: {
      sessionTimeout: 30,
      passwordExpiry: 90,
      mfaRequired: true,
      ipRestriction: false,
      loginAttempts: 5
    },
    notifications: {
      email: true,
      push: true,
      slack: false,
      criticalOnly: false,
      digestFrequency: 'daily'
    },
    display: {
      density: 'comfortable',
      animations: true,
      colorMode: 'dark',
      fontSize: 'medium',
      dashboardRefresh: 60
    },
    integrations: [
      { id: 'slack', name: 'Slack', connected: false },
      { id: 'jira', name: '<PERSON><PERSON>', connected: true },
      { id: 'teams', name: 'Microsoft Teams', connected: false },
      { id: 'email', name: 'Email', connected: true }
    ]
  }; 
 
  return (
    <div style={{
      height: '100%',
      width: '100%',
      overflow: 'auto',
      position: 'relative',
      padding: '24px',
    }}>
      <h1 style={{ 
        fontSize: '28px', 
        fontWeight: 600, 
        marginBottom: '24px',
        background: 'linear-gradient(90deg, #ffffff, #00e5ff)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: '0 0 10px rgba(0, 229, 255, 0.3)',
      }}>
        Settings
      </h1>
      
      <div style={{
        display: 'flex',
        gap: '24px',
      }}>
        {/* Settings navigation */}
        <div style={{
          width: '220px',
          borderRadius: '12px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
          padding: '16px',
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        }}>
          {['general', 'security', 'notifications', 'display', 'integrations'].map(section => (
            <div 
              key={section}
              onClick={() => setActiveSection(section)}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '10px 12px',
                borderRadius: '8px',
                cursor: 'pointer',
                background: activeSection === section ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
                border: activeSection === section ? '1px solid rgba(0, 229, 255, 0.3)' : '1px solid transparent',
                transition: 'all 0.2s ease',
              }}
            >
              <span style={{ 
                fontSize: '14px', 
                color: activeSection === section ? '#00e5ff' : 'white',
                textTransform: 'capitalize',
              }}>
                {section}
              </span>
            </div>
          ))}
        </div>        
 
       {/* Settings content */}
        <div style={{
          flex: 1,
          borderRadius: '12px',
          background: 'rgba(16, 24, 45, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          border: '1px solid rgba(0, 229, 255, 0.2)',
          boxShadow: '0 0 20px rgba(0, 229, 255, 0.1)',
          padding: '24px',
        }}>
          <h2 style={{ 
            fontSize: '20px', 
            fontWeight: 500, 
            marginBottom: '24px',
            color: '#00e5ff',
            textTransform: 'capitalize',
          }}>
            {activeSection} Settings
          </h2>
          
          {/* General Settings */}
          {activeSection === 'general' && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Theme
                </label>
                <select 
                  value={settings.general.theme}
                  style={{
                    background: 'rgba(16, 24, 45, 0.7)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 229, 255, 0.2)',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    color: 'white',
                    fontSize: '14px',
                    width: '100%',
                    maxWidth: '300px',
                  }}
                >
                  <option value="dark">Dark</option>
                  <option value="light">Light</option>
                  <option value="system">System Default</option>
                </select>
              </div>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Language
                </label>
                <select 
                  value={settings.general.language}
                  style={{
                    background: 'rgba(16, 24, 45, 0.7)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 229, 255, 0.2)',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    color: 'white',
                    fontSize: '14px',
                    width: '100%',
                    maxWidth: '300px',
                  }}
                >
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                  <option value="ja">Japanese</option>
                </select>
              </div>
              
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Timezone
                </label>
                <select 
                  value={settings.general.timezone}
                  style={{
                    background: 'rgba(16, 24, 45, 0.7)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 229, 255, 0.2)',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    color: 'white',
                    fontSize: '14px',
                    width: '100%',
                    maxWidth: '300px',
                  }}
                >
                  <option value="UTC">UTC</option>
                  <option value="EST">Eastern Time (EST)</option>
                  <option value="CST">Central Time (CST)</option>
                  <option value="MST">Mountain Time (MST)</option>
                  <option value="PST">Pacific Time (PST)</option>
                </select>
              </div>
            </div>
          )} 
         
          {/* Security Settings */}
          {activeSection === 'security' && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Session Timeout (minutes)
                </label>
                <input 
                  type="number" 
                  value={settings.security.sessionTimeout}
                  style={{
                    background: 'rgba(16, 24, 45, 0.7)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 229, 255, 0.2)',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    color: 'white',
                    fontSize: '14px',
                    width: '100%',
                    maxWidth: '300px',
                  }}
                />
              </div>
              
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                maxWidth: '300px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Require MFA
                </label>
                <div style={{
                  width: '40px',
                  height: '20px',
                  borderRadius: '10px',
                  background: settings.security.mfaRequired ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                  position: 'relative',
                  cursor: 'pointer',
                }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: settings.security.mfaRequired ? '22px' : '2px',
                    transition: 'left 0.2s ease',
                  }} />
                </div>
              </div>
            </div>
          )}
          
          {/* Notifications Settings */}
          {activeSection === 'notifications' && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                maxWidth: '300px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Email Notifications
                </label>
                <div style={{
                  width: '40px',
                  height: '20px',
                  borderRadius: '10px',
                  background: settings.notifications.email ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                  position: 'relative',
                  cursor: 'pointer',
                }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: settings.notifications.email ? '22px' : '2px',
                    transition: 'left 0.2s ease',
                  }} />
                </div>
              </div>
              
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                maxWidth: '300px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Push Notifications
                </label>
                <div style={{
                  width: '40px',
                  height: '20px',
                  borderRadius: '10px',
                  background: settings.notifications.push ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                  position: 'relative',
                  cursor: 'pointer',
                }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: settings.notifications.push ? '22px' : '2px',
                    transition: 'left 0.2s ease',
                  }} />
                </div>
              </div>
            </div>
          )} 
         
          {/* Display Settings */}
          {activeSection === 'display' && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Density
                </label>
                <select 
                  value={settings.display.density}
                  style={{
                    background: 'rgba(16, 24, 45, 0.7)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(0, 229, 255, 0.2)',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    color: 'white',
                    fontSize: '14px',
                    width: '100%',
                    maxWidth: '300px',
                  }}
                >
                  <option value="comfortable">Comfortable</option>
                  <option value="compact">Compact</option>
                </select>
              </div>
              
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                maxWidth: '300px',
              }}>
                <label style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.7)' }}>
                  Animations
                </label>
                <div style={{
                  width: '40px',
                  height: '20px',
                  borderRadius: '10px',
                  background: settings.display.animations ? '#00e5ff' : 'rgba(255, 255, 255, 0.2)',
                  position: 'relative',
                  cursor: 'pointer',
                }}>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    background: 'white',
                    position: 'absolute',
                    top: '2px',
                    left: settings.display.animations ? '22px' : '2px',
                    transition: 'left 0.2s ease',
                  }} />
                </div>
              </div>
            </div>
          )}
          
          {/* Integrations Settings */}
          {activeSection === 'integrations' && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '20px',
            }}>
              {settings.integrations.map(integration => (
                <div 
                  key={integration.id}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '16px',
                    borderRadius: '8px',
                    background: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                  }}
                >
                  <div>
                    <div style={{ fontSize: '16px', color: 'white', marginBottom: '4px' }}>
                      {integration.name}
                    </div>
                    <div style={{ fontSize: '14px', color: 'rgba(255, 255, 255, 0.5)' }}>
                      {integration.connected ? 'Connected' : 'Not connected'}
                    </div>
                  </div>
                  
                  <button style={{
                    background: integration.connected ? 'transparent' : 'rgba(0, 229, 255, 0.1)',
                    border: integration.connected ? '1px solid rgba(255, 99, 132, 0.5)' : '1px solid rgba(0, 229, 255, 0.3)',
                    borderRadius: '4px',
                    padding: '8px 16px',
                    color: integration.connected ? 'rgba(255, 99, 132, 1)' : '#00e5ff',
                    fontSize: '14px',
                    cursor: 'pointer',
                  }}>
                    {integration.connected ? 'Disconnect' : 'Connect'}
                  </button>
                </div>
              ))}
            </div>
          )}  
        
          {/* Save button */}
          <div style={{
            marginTop: '32px',
            display: 'flex',
            justifyContent: 'flex-end',
          }}>
            <button style={{
              background: 'rgba(0, 229, 255, 0.1)',
              border: '1px solid rgba(0, 229, 255, 0.3)',
              borderRadius: '4px',
              padding: '8px 24px',
              color: '#00e5ff',
              fontSize: '14px',
              cursor: 'pointer',
            }}>
              Save Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;