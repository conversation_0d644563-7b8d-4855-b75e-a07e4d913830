import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { initPerformanceMonitoring } from './utils/initPerformanceMonitoring'

// Initialize performance monitoring system
if (process.env.NODE_ENV !== 'production') {
  initPerformanceMonitoring(true, true);
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
