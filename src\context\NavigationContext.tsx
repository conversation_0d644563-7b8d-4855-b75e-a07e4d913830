import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ROUTES } from '../router/constants';

// Define the shape of our navigation state
interface NavigationState {
  activeNav: string;
  exploreExpanded: boolean;
  activeExploreItem?: string;
  endpointSecurityExpanded: boolean;
  activeEndpointSecurityItem?: string;
  collapsed: boolean;
}

// Define the shape of our navigation context
interface NavigationContextType {
  state: NavigationState;
  setActiveNav: (navId: string) => void;
  toggleExplore: () => void;
  setActiveExploreItem: (itemId: string) => void;
  toggleEndpointSecurity: () => void;
  setActiveEndpointSecurityItem: (itemId: string) => void;
  toggleSidebar: () => void;
  navigateTo: (path: string) => void;
}

// Create the context with a default value
const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Provider props interface
interface NavigationProviderProps {
  children: ReactNode;
}

/**
 * Navigation Provider component.
 * 
 * This component provides navigation state and functions to the application.
 */
export function NavigationProvider({ children }: NavigationProviderProps) {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Initialize state
  const [state, setState] = useState<NavigationState>({
    activeNav: 'dashboard',
    exploreExpanded: false,
    activeExploreItem: undefined,
    endpointSecurityExpanded: false,
    activeEndpointSecurityItem: undefined,
    collapsed: false,
  });
  
  // Determine active navigation item based on current path
  const getActiveNavFromPath = (path: string) => {
    if (path === ROUTES.HOME) return 'dashboard';
    // FIXED: Check for endpoint-security paths correctly
    if (path.startsWith('/endpoint-security')) return 'endpoint-security';
    if (path.startsWith(ROUTES.EXPLORE)) return 'explore';
    if (path === ROUTES.GRID) return 'grid';
    if (path === ROUTES.HISTORY) return 'history';
    if (path === ROUTES.FILES) return 'files';
    if (path === ROUTES.PROFILE) return 'profile';
    if (path === ROUTES.SETTINGS) return 'settings';
    return 'dashboard';
  };
  
  // Determine active explore item based on current path
  const getActiveExploreItemFromPath = (path: string) => {
    if (path === ROUTES.EXPLORE_DISCOVER) return 'discover';
    if (path === ROUTES.EXPLORE_DASHBOARDS) return 'dashboards';
    if (path === ROUTES.EXPLORE_VISUALIZE) return 'visualize';
    if (path === ROUTES.EXPLORE_REPORTING) return 'reporting';
    if (path === ROUTES.EXPLORE_ALERTING) return 'alerting';
    if (path === ROUTES.EXPLORE_MAPS) return 'maps';
    if (path === ROUTES.EXPLORE_NOTIFICATIONS) return 'notifications';
    return undefined;
  };

  // Determine active endpoint security item based on current path
  const getActiveEndpointSecurityItemFromPath = (path: string) => {
    if (path === '/endpoint-security/configuration-assessment') return 'configuration-assessment';
    if (path === '/endpoint-security/malware-detection') return 'malware-detection';
    if (path === '/endpoint-security/file-integrity-monitoring') return 'file-integrity-monitoring';
    return undefined;
  };
  
  // Update navigation state when location changes
  useEffect(() => {
    const activeNav = getActiveNavFromPath(location.pathname);
    const activeExploreItem = getActiveExploreItemFromPath(location.pathname);
    const activeEndpointSecurityItem = getActiveEndpointSecurityItemFromPath(location.pathname);
    setState(prevState => ({
      ...prevState,
      activeNav,
      activeExploreItem,
      endpointSecurityExpanded: activeNav === 'endpoint-security' ? prevState.endpointSecurityExpanded : false,
      activeEndpointSecurityItem,
      exploreExpanded: activeNav === 'explore' ? prevState.exploreExpanded : false,
    }));
  }, [location.pathname]);
  
  // Set active navigation item
  const setActiveNav = (navId: string) => {
    setState(prevState => ({
      ...prevState,
      activeNav: navId,
      exploreExpanded: navId === 'explore' ? prevState.exploreExpanded : false,
      activeExploreItem: navId !== 'explore' ? undefined : prevState.activeExploreItem,
      endpointSecurityExpanded: navId === 'endpoint-security' ? prevState.endpointSecurityExpanded : false,
      activeEndpointSecurityItem: navId !== 'endpoint-security' ? undefined : prevState.activeEndpointSecurityItem,
    }));
    
    // Navigate to the corresponding route
    switch (navId) {
      case 'dashboard':
        navigate(ROUTES.HOME);
        break;
      case 'explore':
        // Do not navigate on explore click, just toggle
        break;
      case 'endpoint-security':
        break;
      case 'grid':
        navigate(ROUTES.GRID);
        break;
      case 'history':
        navigate(ROUTES.HISTORY);
        break;
      case 'files':
        navigate(ROUTES.FILES);
        break;
      case 'profile':
        navigate(ROUTES.PROFILE);
        break;
      case 'settings':
        navigate(ROUTES.SETTINGS);
        break;
      default:
        navigate(ROUTES.HOME);
    }
  };
  
  // Toggle explore dropdown
  const toggleExplore = () => {
    setState(prevState => ({
      ...prevState,
      activeNav: 'explore',
      exploreExpanded: !prevState.exploreExpanded,
    }));
  };
  
  // Set active explore item
  const setActiveExploreItem = (itemId: string) => {
    setState(prevState => ({
      ...prevState,
      activeNav: 'explore',
      activeExploreItem: itemId,
      exploreExpanded: true, // Keep dropdown open when an item is selected
    }));
    
    // Navigate to the corresponding explore route
    switch (itemId) {
      case 'discover':
        navigate(ROUTES.EXPLORE_DISCOVER);
        break;
      case 'dashboards':
        navigate(ROUTES.EXPLORE_DASHBOARDS);
        break;
      case 'visualize':
        navigate(ROUTES.EXPLORE_VISUALIZE);
        break;
      case 'reporting':
        navigate(ROUTES.EXPLORE_REPORTING);
        break;
      case 'alerting':
        navigate(ROUTES.EXPLORE_ALERTING);
        break;
      case 'maps':
        navigate(ROUTES.EXPLORE_MAPS);
        break;
      case 'notifications':
        navigate(ROUTES.EXPLORE_NOTIFICATIONS);
        break;
      default:
        navigate(ROUTES.EXPLORE);
    }
  };

  // Toggle endpoint security dropdown
  const toggleEndpointSecurity = () => {
    setState(prevState => ({
      ...prevState,
      activeNav: 'endpoint-security',
      endpointSecurityExpanded: !prevState.endpointSecurityExpanded,
    }));
  };

  // Set active endpoint security item
  const setActiveEndpointSecurityItem = (itemId: string) => {
    setState(prevState => ({
      ...prevState,
      activeNav: 'endpoint-security',
      activeEndpointSecurityItem: itemId,
      endpointSecurityExpanded: true,
    }));
    // Navigate to the corresponding endpoint security route
    switch (itemId) {
      case 'configuration-assessment':
        navigate('/endpoint-security/configuration-assessment');
        break;
      case 'malware-detection':
        navigate('/endpoint-security/malware-detection');
        break;
      case 'file-integrity-monitoring':
        navigate('/endpoint-security/file-integrity-monitoring');
        break;
      default:
        navigate('/endpoint-security/configuration-assessment');
    }
  };
  
  // Toggle sidebar collapsed state
  const toggleSidebar = () => {
    setState(prevState => ({
      ...prevState,
      collapsed: !prevState.collapsed,
    }));
  };
  
  // Navigate to a specific path
  const navigateTo = (path: string) => {
    navigate(path);
  };
  
  // Create the context value
  const contextValue: NavigationContextType = {
    state,
    setActiveNav,
    toggleExplore,
    setActiveExploreItem,
    toggleEndpointSecurity,
    setActiveEndpointSecurityItem,
    toggleSidebar,
    navigateTo,
  };
  
  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};

/**
 * Custom hook to use the navigation context.
 * 
 * @returns The navigation context value
 * @throws Error if used outside of a NavigationProvider
 */
export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  
  return context;
};

export default NavigationContext;