import React, { useState } from 'react';
import { useDiscoverFields } from '../../hooks';

interface FieldValue {
  value: unknown;
  count: number;
  percentage: number;
}

interface FieldDetailsProps {
  fieldName: string;
  fieldType: string;
  totalCount: number;
  topValues?: Array<{value: unknown, count: number}>;
  onAddFilter?: (field: string, value: unknown, operator: string) => void;
  onClose: () => void;
}

/**
 * Component for displaying detailed information about a field
 * Shows statistics and top values
 */
const FieldDetails: React.FC<FieldDetailsProps> = ({
  fieldName,
  fieldType,
  totalCount,
  topValues = [],
  onAddFilter,
  onClose
}) => {
  const { formatFieldValue } = useDiscoverFields();
  const [activeTab, setActiveTab] = useState<'values' | 'stats'>('values');
  
  // Calculate percentages for top values
  const valuesWithPercentage: FieldValue[] = topValues.map(({ value, count }) => ({
    value,
    count,
    percentage: (count / totalCount) * 100
  }));
  
  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      width: '500px',
      maxWidth: '90vw',
      background: 'rgba(10, 14, 23, 0.95)',
      borderRadius: '8px',
      border: '1px solid rgba(0, 229, 255, 0.3)',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
      zIndex: 1000,
      overflow: 'hidden',
    }}>
      {/* Header */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}>
        <h3 style={{ margin: 0, color: 'white' }}>
          {fieldName}
          <span style={{ 
            fontSize: '14px', 
            color: 'rgba(255, 255, 255, 0.6)',
            marginLeft: '8px',
            fontWeight: 'normal',
          }}>
            {fieldType}
          </span>
        </h3>
        <button
          onClick={onClose}
          aria-label="Close field details"
          style={{
            background: 'transparent',
            border: 'none',
            color: '#00e5ff',
            cursor: 'pointer',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* Tabs */}
      <div style={{
        display: 'flex',
        borderBottom: '1px solid rgba(0, 229, 255, 0.2)',
      }}>
        <button
          onClick={() => setActiveTab('values')}
          style={{
            flex: 1,
            padding: '12px',
            background: activeTab === 'values' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
            border: 'none',
            borderBottom: activeTab === 'values' ? '2px solid #00e5ff' : 'none',
            color: activeTab === 'values' ? '#00e5ff' : 'white',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: activeTab === 'values' ? 'bold' : 'normal',
          }}
        >
          Top Values
        </button>
        <button
          onClick={() => setActiveTab('stats')}
          style={{
            flex: 1,
            padding: '12px',
            background: activeTab === 'stats' ? 'rgba(0, 229, 255, 0.1)' : 'transparent',
            border: 'none',
            borderBottom: activeTab === 'stats' ? '2px solid #00e5ff' : 'none',
            color: activeTab === 'stats' ? '#00e5ff' : 'white',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: activeTab === 'stats' ? 'bold' : 'normal',
          }}
        >
          Statistics
        </button>
      </div>
      
      {/* Content */}
      <div style={{
        maxHeight: '400px',
        overflowY: 'auto',
        padding: '16px',
      }}>
        {activeTab === 'values' && (
          <>
            {valuesWithPercentage.length === 0 ? (
              <p style={{ color: 'rgba(255, 255, 255, 0.6)', textAlign: 'center' }}>
                No values available for this field
              </p>
            ) : (
              <ul style={{ 
                listStyle: 'none', 
                padding: 0, 
                margin: 0,
              }}>
                {valuesWithPercentage.map((item, index) => (
                  <li 
                    key={index}
                    style={{
                      padding: '8px',
                      marginBottom: '8px',
                      background: 'rgba(0, 0, 0, 0.2)',
                      borderRadius: '4px',
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '4px',
                    }}>
                      <span style={{ 
                        color: 'white',
                        fontSize: '14px',
                        wordBreak: 'break-all',
                      }}>
                        {formatFieldValue(item.value)}
                      </span>
                      <div style={{
                        display: 'flex',
                        gap: '8px',
                      }}>
                        {onAddFilter && (
                          <>
                            <button
                              onClick={() => onAddFilter(fieldName, item.value, 'is')}
                              title="Filter for value"
                              style={{
                                background: 'transparent',
                                border: 'none',
                                color: '#00e5ff',
                                cursor: 'pointer',
                                padding: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => onAddFilter(fieldName, item.value, 'is not')}
                              title="Filter out value"
                              style={{
                                background: 'transparent',
                                border: 'none',
                                color: '#00e5ff',
                                cursor: 'pointer',
                                padding: '2px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" />
                                <line x1="2" y1="3" x2="22" y2="22" />
                              </svg>
                            </button>
                          </>
                        )}
                      </div>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '6px',
                      background: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '3px',
                      overflow: 'hidden',
                    }}>
                      <div style={{
                        width: `${item.percentage}%`,
                        height: '100%',
                        background: '#00e5ff',
                      }} />
                    </div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginTop: '4px',
                      fontSize: '12px',
                      color: 'rgba(255, 255, 255, 0.6)',
                    }}>
                      <span>{item.count} occurrences</span>
                      <span>{item.percentage.toFixed(1)}%</span>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </>
        )}
        
        {activeTab === 'stats' && (
          <div style={{ color: 'white' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
            }}>
              <tbody>
                <tr>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Count</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)', textAlign: 'right' }}>{totalCount}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Type</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)', textAlign: 'right' }}>{fieldType}</td>
                </tr>
                <tr>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>Unique values</td>
                  <td style={{ padding: '8px', borderBottom: '1px solid rgba(255, 255, 255, 0.1)', textAlign: 'right' }}>{topValues.length}</td>
                </tr>
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Footer */}
      <div style={{
        padding: '12px 16px',
        borderTop: '1px solid rgba(0, 229, 255, 0.2)',
        display: 'flex',
        justifyContent: 'flex-end',
      }}>
        <button
          onClick={onClose}
          style={{
            padding: '8px 16px',
            background: 'rgba(0, 229, 255, 0.2)',
            border: '1px solid rgba(0, 229, 255, 0.5)',
            borderRadius: '4px',
            color: '#00e5ff',
            cursor: 'pointer',
            fontSize: '14px',
          }}
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default FieldDetails;