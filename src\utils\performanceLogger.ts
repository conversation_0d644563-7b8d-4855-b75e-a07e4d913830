/**
 * Performance logger utility
 * 
 * This module provides utilities for logging performance metrics to the console
 * in a structured and readable format.
 */

import { 
  getPerformanceStats 
} from './performanceTracking';
import { 
  getComparisonResults, 
  logPerformanceComparison 
} from './performanceComparison';

// Console styling constants
const STYLES = {
  header: 'font-weight: bold; font-size: 14px; color: #00e5ff;',
  subheader: 'font-weight: bold; font-size: 12px; color: #00e5ff;',
  good: 'color: #4caf50;',
  warning: 'color: #ff9800;',
  error: 'color: #f44336;',
  info: 'color: #2196f3;',
  muted: 'color: #9e9e9e;',
  bold: 'font-weight: bold;',
  code: 'font-family: monospace; background-color: rgba(0,0,0,0.1); padding: 2px 4px; border-radius: 2px;'
};

/**
 * Log a performance report to the console
 * @param title Report title
 * @param includeComparison Whether to include comparison with Discover page
 * @param detailed Whether to show detailed metrics
 */
export function logPerformanceReport(
  title: string = 'Performance Report',
  includeComparison: boolean = true,
  detailed: boolean = false
): void {
  console.group(`%c${title}`, STYLES.header);
  console.log(`%cGenerated at: ${new Date().toLocaleString()}`, STYLES.muted);
  
  // Log all performance metrics
  console.group('%cPerformance Metrics', STYLES.subheader);
  logAllPerformanceMetrics(detailed);
  console.groupEnd();
  
  // Log comparison with Discover page if requested
  if (includeComparison) {
    console.group('%cComparison with Discover Page', STYLES.subheader);
    logPerformanceComparison();
    console.groupEnd();
  }
  
  console.groupEnd();
}

/**
 * Log performance metrics for a specific operation
 * @param operationName Full operation name (including component prefix)
 * @param category Optional category filter
 */
export function logOperationPerformance(operationName: string, category?: string): void {
  const stats = getPerformanceStats(operationName, category);
  
  console.group(`%cOperation: ${operationName}`, STYLES.subheader);
  
  if (stats.count === 0) {
    console.log('%cNo metrics recorded for this operation', STYLES.muted);
  } else {
    console.log(`%cSamples: ${stats.count}`, STYLES.bold);
    console.log(`%cAverage: ${stats.avg.toFixed(2)}ms`, getPerformanceStyle(stats.avg));
    console.log(`%cMedian: ${stats.median.toFixed(2)}ms`, getPerformanceStyle(stats.median));
    console.log(`%cMin: ${stats.min.toFixed(2)}ms`, STYLES.info);
    console.log(`%cMax: ${stats.max.toFixed(2)}ms`, STYLES.info);
    console.log(`%cTotal: ${stats.total.toFixed(2)}ms`, STYLES.muted);
  }
  
  console.groupEnd();
}

/**
 * Get console style based on performance time
 * @param time Time in milliseconds
 * @returns CSS style string
 */
function getPerformanceStyle(time: number): string {
  if (time < 16.67) { // 60fps threshold
    return STYLES.good;
  } else if (time < 50) { // Acceptable threshold
    return STYLES.warning;
  } else {
    return STYLES.error;
  }
}

/**
 * Create a performance logger for a specific component
 * @param componentName Name of the component
 * @returns Object with logging methods
 */
export function createPerformanceLogger(componentName: string) {
  return {
    /**
     * Log performance metrics for this component
     * @param detailed Whether to show detailed metrics
     */
    logComponentPerformance: (detailed: boolean = false) => {
      console.group(`%cComponent: ${componentName}`, STYLES.subheader);
      
      // Get all operations for this component
      const operations = new Set<string>();
      const componentPrefix = `${componentName}.`;
      
      // Find all operations for this component
      getComparisonResults().forEach(result => {
        if (result.operationName.startsWith(componentPrefix)) {
          operations.add(result.operationName.substring(componentPrefix.length));
        }
      });
      
      if (operations.size === 0) {
        console.log('%cNo metrics recorded for this component', STYLES.muted);
      } else {
        // Log each operation
        operations.forEach(operation => {
          const fullOperationName = `${componentName}.${operation}`;
          const stats = getPerformanceStats(fullOperationName);
          
          console.group(`%c${operation}`, STYLES.bold);
          
          if (stats.count === 0) {
            console.log('%cNo metrics recorded', STYLES.muted);
          } else {
            console.log(`%cSamples: ${stats.count}`, STYLES.muted);
            console.log(`%cAverage: ${stats.avg.toFixed(2)}ms`, getPerformanceStyle(stats.avg));
            console.log(`%cMedian: ${stats.median.toFixed(2)}ms`, getPerformanceStyle(stats.median));
            console.log(`%cMin: ${stats.min.toFixed(2)}ms`, STYLES.info);
            console.log(`%cMax: ${stats.max.toFixed(2)}ms`, STYLES.info);
            
            if (detailed) {
              // Show comparison with Discover page if available
              const comparisons = getComparisonResults().filter(
                result => result.operationName === fullOperationName
              );
              
              if (comparisons.length > 0) {
                console.group('%cComparison with Discover', STYLES.muted);
                
                comparisons.forEach(comparison => {
                  const status = comparison.isFaster ? '✅ FASTER' : '❌ SLOWER';
                  const style = comparison.isFaster ? STYLES.good : STYLES.error;
                  
                  console.log(
                    `%c${status} | ${comparison.configAssessmentTime.toFixed(2)}ms vs ${comparison.discoverTime.toFixed(2)}ms ` +
                    `(${Math.abs(comparison.percentageDifference).toFixed(2)}% ${comparison.isFaster ? 'faster' : 'slower'})`,
                    style
                  );
                });
                
                console.groupEnd();
              }
            }
          }
          
          console.groupEnd();
        });
      }
      
      console.groupEnd();
    },
    
    /**
     * Log performance metrics for a specific operation
     * @param operationName Name of the operation (without component prefix)
     * @param category Optional category filter
     */
    logOperationPerformance: (operationName: string, category?: string) => {
      logOperationPerformance(`${componentName}.${operationName}`, category);
    }
  };
}

/**
 * Add a console command for logging performance metrics
 * This makes it easy to access performance data from the browser console
 */
export function registerConsoleCommands(): void {
  if (typeof window !== 'undefined') {
    (window as unknown).logPerformance = {
      report: logPerformanceReport,
      operation: logOperationPerformance,
      all: () => logAllPerformanceMetrics(true),
      comparison: logPerformanceComparison
    };
    
    console.log(
      '%cPerformance logging commands registered. Use window.logPerformance.report() to view performance metrics.',
      'font-weight: bold; color: #00e5ff;'
    );
  }
}