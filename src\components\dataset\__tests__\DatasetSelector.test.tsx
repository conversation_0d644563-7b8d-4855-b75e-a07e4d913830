import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DatasetSelector from '../DatasetSelector';
import { DataPluginProvider } from '../../../context/DataPluginContext';
import { useDataset } from '../../../hooks/dataHooks';

// Mock the useDataset hook
jest.mock('../../../hooks/dataHooks', () => {
  const originalModule = jest.requireActual('../../../hooks/dataHooks');
  
  return {
    ...originalModule,
    useDataset: jest.fn()
  };
});

describe('DatasetSelector', () => {
  const mockSelectDataset = jest.fn().mockResolvedValue(undefined);
  const mockOnDatasetSelected = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation
    (useDataset as jest.Mock).mockReturnValue({
      datasets: [
        { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
        { id: 'dataset2', title: 'Dataset 2', type: 'sample' },
        { id: 'dataset3', title: 'Test Dataset', type: 'logs' }
      ],
      selectedDataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample' },
      selectDataset: mockSelectDataset
    });
  });
  
  it('should render the dataset list', () => {
    render(
      <DataPluginProvider>
        <DatasetSelector />
      </DataPluginProvider>
    );
    
    // Check that the component renders
    expect(screen.getByText('Select Dataset')).toBeInTheDocument();
    
    // Check that all datasets are rendered
    expect(screen.getByText('Dataset 1')).toBeInTheDocument();
    expect(screen.getByText('Dataset 2')).toBeInTheDocument();
    expect(screen.getByText('Test Dataset')).toBeInTheDocument();
    
    // Check that the selected dataset is marked
    const selectedDataset = screen.getByText('Dataset 1').closest('.dataset-item');
    expect(selectedDataset).toHaveClass('selected');
  });
  
  it('should filter datasets based on search term', () => {
    render(
      <DataPluginProvider>
        <DatasetSelector />
      </DataPluginProvider>
    );
    
    // Enter search term
    const searchInput = screen.getByPlaceholderText('Search datasets...');
    fireEvent.change(searchInput, { target: { value: 'Test' } });
    
    // Check that only matching datasets are shown
    expect(screen.queryByText('Dataset 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Dataset 2')).not.toBeInTheDocument();
    expect(screen.getByText('Test Dataset')).toBeInTheDocument();
  });
  
  it('should select a dataset when clicked', async () => {
    render(
      <DataPluginProvider>
        <DatasetSelector onDatasetSelected={mockOnDatasetSelected} />
      </DataPluginProvider>
    );
    
    // Click on a dataset
    const datasetButton = screen.getByText('Dataset 2').closest('button');
    fireEvent.click(datasetButton!);
    
    // Check that selectDataset was called
    expect(mockSelectDataset).toHaveBeenCalledWith('dataset2');
    
    // Wait for the selection to complete
    await waitFor(() => {
      expect(mockOnDatasetSelected).toHaveBeenCalledWith({
        id: 'dataset2',
        title: 'Dataset 2',
        type: 'sample'
      });
    });
  });
  
  it('should show loading state during dataset selection', async () => {
    // Mock a delayed selection
    mockSelectDataset.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <DataPluginProvider>
        <DatasetSelector />
      </DataPluginProvider>
    );
    
    // Click on a dataset
    const datasetButton = screen.getByText('Dataset 2').closest('button');
    fireEvent.click(datasetButton!);
    
    // Check that loading state is shown
    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    // Wait for the selection to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
  });
  
  it('should show error state when dataset selection fails', async () => {
    // Mock a failed selection
    const mockError = new Error('Failed to select dataset');
    mockSelectDataset.mockRejectedValue(mockError);
    
    render(
      <DataPluginProvider>
        <DatasetSelector />
      </DataPluginProvider>
    );
    
    // Click on a dataset
    const datasetButton = screen.getByText('Dataset 2').closest('button');
    fireEvent.click(datasetButton!);
    
    // Wait for the error to be shown
    await waitFor(() => {
      expect(screen.getByText('Error: Failed to select dataset')).toBeInTheDocument();
    });
  });
  
  it('should show selected dataset info', () => {
    // Mock a dataset with timeFieldName
    (useDataset as jest.Mock).mockReturnValue({
      datasets: [
        { id: 'dataset1', title: 'Dataset 1', type: 'sample', timeFieldName: '@timestamp' }
      ],
      selectedDataset: { id: 'dataset1', title: 'Dataset 1', type: 'sample', timeFieldName: '@timestamp' },
      selectDataset: mockSelectDataset
    });
    
    render(
      <DataPluginProvider>
        <DatasetSelector />
      </DataPluginProvider>
    );
    
    // Check that selected dataset info is shown
    expect(screen.getByText('Selected Dataset')).toBeInTheDocument();
    expect(screen.getByText('ID:')).toBeInTheDocument();
    expect(screen.getByText('dataset1', { exact: false })).toBeInTheDocument();
    expect(screen.getByText('Title:')).toBeInTheDocument();
    expect(screen.getByText('Dataset 1', { exact: false })).toBeInTheDocument();
    expect(screen.getByText('Type:')).toBeInTheDocument();
    expect(screen.getByText('sample', { exact: false })).toBeInTheDocument();
    expect(screen.getByText('Time Field:')).toBeInTheDocument();
    expect(screen.getByText('@timestamp', { exact: false })).toBeInTheDocument();
  });
});