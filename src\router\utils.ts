import { ROUTES } from './constants';

/**
 * Extract active navigation item from current location pathname
 * @param pathname The current location pathname
 * @returns The active navigation item ID
 */
export const getActiveNavFromPath = (pathname: string): string => {
  if (pathname === ROUTES.HOME) return 'dashboard';
  if (pathname.startsWith('/explore')) return 'explore';
  
  // Remove leading slash and return the first segment
  const path = pathname.substring(1);
  return path.split('/')[0];
};

/**
 * Extract active explore item if in explore section
 * @param pathname The current location pathname
 * @returns The active explore item ID or undefined
 */
export const getActiveExploreItem = (pathname: string): string | undefined => {
  if (!pathname.startsWith('/explore/')) return undefined;
  return pathname.split('/')[2]; // Get the part after /explore/
};